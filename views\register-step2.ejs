<!DOCTYPE html>
<html lang="en">

<head>
	<meta charset="UTF-8" />
	<meta http-equiv="X-UA-Compatible" content="IE=edge" />
	<meta name="viewport" content="width=device-width, initial-scale=1.0" />
	<title>
		<%= title %>
	</title>
	<!-- Google Font: Source Sans Pro -->
	<link rel="stylesheet"
		href="https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,400i,700&display=fallback" />
	<!-- Font Awesome -->
	<link rel="stylesheet" href="/plugins/fontawesome-free/css/all.min.css" />
	<!-- Ionicons -->
	<link rel="stylesheet" href="https://code.ionicframework.com/ionicons/2.0.1/css/ionicons.min.css" />
	<!-- Tempusdominus Bootstrap 4 -->
	<link rel="stylesheet"
		href="/plugins/tempusdominus-bootstrap-4/css/tempusdominus-bootstrap-4.min.css" />
	<!-- iCheck -->
	<link rel="stylesheet" href="/plugins/icheck-bootstrap/icheck-bootstrap.min.css" />
	<!-- Select2 -->
	<link rel="stylesheet" href="/plugins/select2/css/select2.min.css" />
	<link rel="stylesheet" href="/plugins/select2-bootstrap4-theme/select2-bootstrap4.min.css" />
	<!-- Theme style -->
	<link rel="stylesheet" href="/dist/css/adminlte.min.css" />
	<!-- overlayScrollbars -->
	<link rel="stylesheet" href="/plugins/overlayScrollbars/css/OverlayScrollbars.min.css" />
	<!-- Daterange picker -->
	<link rel="stylesheet" href="/plugins/daterangepicker/daterangepicker.css" />
	<!-- DataTables -->
	<link rel="stylesheet" href="/plugins/datatables-bs4/css/dataTables.bootstrap4.min.css" />
	<link rel="stylesheet" href="/plugins/datatables-responsive/css/responsive.bootstrap4.min.css" />
	<link rel="stylesheet" href="/plugins/datatables-buttons/css/buttons.bootstrap4.min.css" />
	<style type="text/css">
		.seat {
			width: 50px;
		}

		input::-webkit-outer-spin-button,
		input::-webkit-inner-spin-button {
			-webkit-appearance: none;
			margin: 0;
		}
        .card-title{
            font-weight: bold !important;
        }
        em{
            color: red;
        }
	</style>
</head>

<body class="hold-transition sidebar-mini layout-fixed sidebar-collapse">
	<div class="wrapper">
		<!-- Preloader -->
		<div class="preloader flex-column justify-content-center align-items-center">
			<img class="animation__shake" src="/images/loader.gif" alt="CodeBucket" height="60"
				width="60" />
		</div>

		<!-- Navbar -->
		<%- include('partials/header'); %>
			<!-- /.navbar -->

			<!-- Main Sidebar Container -->
			<%- include('partials/open-sidebar'); %>

				<!-- Content Wrapper. Contains page content -->
				<div class="content-wrapper">
					<!-- Content Header (Page header) -->
					<div class="content-header">
						<div class="container-fluid">
							<div class="row mb-2">
								<div class="col-sm-6">
									<h1 class="m-0">New User Registration</h1>
								</div>
								<!-- /.col -->
								<div class="col-sm-6">
									<ol class="breadcrumb float-sm-right">
										<li class="breadcrumb-item"><a href="/">Home</a></li>
										<li class="breadcrumb-item active">Register New User</li>
									</ol>
								</div>
								<!-- /.col -->
							</div>
							<!-- /.row -->
						</div>
						<!-- /.container-fluid -->
					</div>

					<section class="content">
						<div class="container-fluid">
							<div class="row">
								<section class="col-12">
									<form id="frmregister" name="frmregister" action="" method="post">
										<div class="card card-primary">
											<!-- /.card-header -->
                                            <div class="card-header">
                                                <h3 class="card-title">Bank Details</h3>
                                            </div>
											<div class="card-body">
												<div class="row">
                                                    <div class="col-md-6">
														<div class="form-group">
															<label>Bank Account No<em>*</em></label>
															<input type="text" class="form-control" name="bankaccount"
																id="bankaccount" placeholder="Enter Bank Account No" required />
														</div>
													</div>
                                                    <div class="col-md-6">
														<div class="form-group">
															<label>Bank Name<em>*</em></label>
															<input type="text" class="form-control" name="bankname"
																id="bankname" placeholder="Enter Bank Name" required />
														</div>
													</div>
                                                    <div class="col-md-6">
														<div class="form-group">
															<label>Branch Name<em>*</em></label>
															<input type="text" class="form-control" name="bankbranch"
																id="bankbranch" placeholder="Enter Bank Branch" required />
														</div>
													</div>
                                                    <div class="col-md-6">
														<div class="form-group">
															<label>IFSC Code<em>*</em></label>
															<input type="text" class="form-control" name="ifsc"
																id="ifsc" placeholder="Enter Bank IFSC Code" required />
														</div>
													</div>
												</div>
											</div>
										</div>

                                        <div class="card card-primary">
											<!-- /.card-header -->
                                            <div class="card-header">
                                                <h3 class="card-title">Pay Entitlement Details</h3>
                                            </div>
											<div class="card-body">
												<div class="row">
													<div class="col-md-6">
														<div class="form-group">
															<label>Pay Level.<em>*</em></label>
															<input type="text" class="form-control" name="paylevel"
																id="paylevel" placeholder="Enter Pay Level" required />
														</div>
													</div>
                                                    <div class="col-md-6">
														<div class="form-group">
															<label>Basic Salary<em>*</em></label>
															<input type="text" class="form-control" name="basicsalary"
																id="basicsalary" placeholder="Enter Basic Salary" required />
														</div>
													</div>
                                                    <div class="col-md-6">
														<div class="form-group">
															<label>HRA<em>*</em></label>
															<input type="text" class="form-control" name="hra"
																id="hra" placeholder="Enter HRA" required />
														</div>
													</div>
                                                    <div class="col-md-6">
														<div class="form-group">
															<label>CTA<em>*</em></label>
															<input type="text" class="form-control" name="cta"
																id="cta" placeholder="Enter CTA" required />
														</div>
													</div>
                                                    <div class="col-md-6">
														<div class="form-group">
															<label>DA<em>*</em></label>
															<input type="text" class="form-control" name="da"
																id="da" placeholder="Enter DA" required />
														</div>
													</div>
                                                    <div class="col-md-6">
														<div class="form-group">
															<label>Special/Other Allowance<em>*</em></label>
															<input type="text" class="form-control" name="specialallowance"
																id="specialallowance" placeholder="Enter Special Allowance" required />
														</div>
													</div>
                                                    <div class="col-md-6">
														<div class="form-group">
															<label>Medical Allowance<em>*</em></label>
															<input type="text" class="form-control" name="medical"
																id="medical" placeholder="Enter Medical Allowance" required />
														</div>
													</div>
												</div>
											</div>
										</div>

                                        <div class="card card-primary">
											<!-- /.card-header -->
                                            <div class="card-header">
                                                <h3 class="card-title">Pay Entitlement Deduction</h3>
                                            </div>
											<div class="card-body">
												<div class="row">
													<div class="col-md-6">
														<div class="form-group">
															<label>Income Tax</label>
															<input type="text" class="form-control" name="incometax"
																id="incometax" placeholder="Enter Income Tax" />
														</div>
													</div>
                                                    <div class="col-md-6">
														<div class="form-group">
															<label>NPS Opted<em>*</em></label>
															<select class="form-control select2" name="npsopted"
																id="npsopted" style="width: 100%" required>
                                                                <option value="">Choose Status</option>
                                                                <option value="Yes">Yes</option>
                                                                <option value="No">No</option>
                                                            </select>
														</div>
													</div>
                                                    <div class="col-md-6">
														<div class="form-group">
															<label>P.F.</label>
															<input type="text" class="form-control" name="pf"
																id="pf" placeholder="Enter PF" />
														</div>
													</div>
                                                    <div class="col-md-6">
														<div class="form-group">
															<label>NPS</label>
															<input type="text" class="form-control" name="nps"
																id="nps" placeholder="Enter NPS" />
														</div>
													</div>
                                                    <div class="col-md-6">
														<div class="form-group">
															<label>P.F. Loan</label>
															<input type="text" class="form-control" name="pfloan"
																id="pfloan" placeholder="Enter PF Loan" />
														</div>
													</div>
                                                    <div class="col-md-6">
														<div class="form-group">
															<label>LIC</label>
															<input type="text" class="form-control" name="lic"
																id="lic" placeholder="Enter LIC" />
														</div>
													</div>
                                                    <div class="col-md-6">
														<div class="form-group">
															<label>G.I.P.</label>
															<input type="text" class="form-control" name="gip"
																id="gip" placeholder="Enter GIP" />
														</div>
													</div>
                                                    <div class="col-md-6">
														<div class="form-group">
															<label>Net Payable</label>
															<input type="text" class="form-control" name="netpayable"
																id="netpayable" placeholder="Enter Net Payable" />
														</div>
													</div>
												</div>
											</div>
											<div class="card-footer">
												<button type="submit" id="btnsubmit"
													class="btn btn-primary float-right mr-1">
													Save and Next
												</button>
											</div>
										</div>
									</form>
								</section>
							</div>
							<!-- Main row -->
						</div>
						<!-- /.container-fluid -->
					</section>
					<!-- /.content -->
				</div>
				<!-- /.content-wrapper -->
				
				<%- include('partials/footer'); %>
					<!-- Control Sidebar -->
					<aside class="control-sidebar control-sidebar-dark">
						<!-- Control sidebar content goes here -->
					</aside>
					<!-- /.control-sidebar -->
	</div>
	<!-- ./wrapper -->

	<!-- jQuery -->
	<script src="/plugins/jquery/jquery.min.js"></script>
	<!-- jQuery UI 1.11.4 -->
	<script src="/plugins/jquery-ui/jquery-ui.min.js"></script>
	<!-- Resolve conflict in jQuery UI tooltip with Bootstrap tooltip -->
	<script>
		$.widget.bridge("uibutton", $.ui.button);
	</script>
	<!-- Bootstrap 4 -->
	<script src="/plugins/bootstrap/js/bootstrap.bundle.min.js"></script>
	<!-- DataTables  & Plugins -->
	<script src="/plugins/datatables/jquery.dataTables.min.js"></script>
	<script src="/plugins/datatables-bs4/js/dataTables.bootstrap4.min.js"></script>
	<script src="/plugins/datatables-responsive/js/dataTables.responsive.min.js"></script>
	<script src="/plugins/datatables-responsive/js/responsive.bootstrap4.min.js"></script>
	<script src="/plugins/datatables-buttons/js/dataTables.buttons.min.js"></script>
	<script src="/plugins/datatables-buttons/js/buttons.bootstrap4.min.js"></script>
	<script src="/plugins/jszip/jszip.min.js"></script>
	<script src="/plugins/pdfmake/pdfmake.min.js"></script>
	<script src="/plugins/pdfmake/vfs_fonts.js"></script>
	<script src="/plugins/datatables-buttons/js/buttons.html5.min.js"></script>
	<script src="/plugins/datatables-buttons/js/buttons.print.min.js"></script>
	<script src="/plugins/datatables-buttons/js/buttons.colVis.min.js"></script>
	<!-- Sparkline -->
	<script src="/plugins/sparklines/sparkline.js"></script>
	<!-- Select2 -->
	<script src="/plugins/select2/js/select2.full.min.js"></script>
	<!-- daterangepicker -->
	<script src="/plugins/moment/moment.min.js"></script>
	<script src="/plugins/daterangepicker/daterangepicker.js"></script>
	<!-- Tempusdominus Bootstrap 4 -->
	<script src="/plugins/tempusdominus-bootstrap-4/js/tempusdominus-bootstrap-4.min.js"></script>
	<!-- overlayScrollbars -->
	<script src="/plugins/overlayScrollbars/js/jquery.overlayScrollbars.min.js"></script>
	<!-- AdminLTE App -->
	<script src="/dist/js/adminlte.js"></script>
	<!-- AdminLTE for demo purposes -->
	<script src="/dist/js/demo.js"></script>
	<!-- jquery-validation -->
	<script src="/plugins/jquery-validation/jquery.validate.min.js"></script>
	<script src="/plugins/jquery-validation/additional-methods.min.js"></script>
	<!-- Toastr -->
	<script src="/plugins/toastr/toastr.min.js"></script>
	<script>
		$(function () {
            $('.select2').select2()
            //Date picker
            $('#dob').datetimepicker({
                format: 'L'
            });
            $('#orderdate').datetimepicker({
                format: 'L'
            });
            $('#joiningdate').datetimepicker({
                format: 'L'
            });
			const apiBaseUrl = "<%= process.env.apiBaseUrl %>";
            $("#paymentverification").on("select2:select", function (e) {
                var paymentverificationstatus = $("#paymentverification").val();
                if(paymentverificationstatus=="Yes")
                    $("#pvccontainer").show();
                else
                    $("#pvccontainer").hide();
            });
            $("#frmregister").on("submit", function (e) {
			    e.preventDefault();
                window.location = "/register-step2";
            });
		});
	</script>
	
</body>

</html>