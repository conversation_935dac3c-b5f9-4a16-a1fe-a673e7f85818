<!DOCTYPE html>
<html lang="en">

<head>
	<meta charset="UTF-8" />
	<meta http-equiv="X-UA-Compatible" content="IE=edge" />
	<meta name="viewport" content="width=device-width, initial-scale=1.0" />
	<title>
		<%= title %>
	</title>
		<!-- Google Font: Source Sans Pro -->
        <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,400i,700&display=fallback" />

        <!-- Font Awesome -->
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css" />
    
        <!-- Ionicons -->
        <link rel="stylesheet" href="https://code.ionicframework.com/ionicons/2.0.1/css/ionicons.min.css" />
    
    
        <!-- Tempusdominus Bootstrap 4 -->
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/tempusdominus-bootstrap-4/5.39.0/css/tempusdominus-bootstrap-4.min.css" />
    
        <!-- iCheck -->
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/icheck-bootstrap/3.0.1/icheck-bootstrap.min.css" />
    
        <!-- Select2 -->
        <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
        <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/select2@4.0.13/dist/css/select2.min.css" />
        
        <!-- Theme style -->
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/admin-lte/3.1.0/css/adminlte.min.css" />
    
        <!-- overlayScrollbars -->
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/overlayscrollbars/1.13.0/css/OverlayScrollbars.min.css" />
    
        <!-- Daterange picker -->
        <link rel="stylesheet" href="https://cdn.datatables.net/1.10.24/css/dataTables.bootstrap4.min.css">
    
    
        <!-- DataTables -->
        <link rel="stylesheet" href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap4.min.css">
        <link rel="stylesheet" href="https://cdn.datatables.net/responsive/2.2.9/css/responsive.bootstrap4.min.css">
        <link rel="stylesheet" href="https://cdn.datatables.net/buttons/2.0.1/css/buttons.bootstrap4.min.css">
        <!-- Baoxicon -->
	<link href='https://unpkg.com/boxicons@2.1.4/css/boxicons.min.css' rel='stylesheet'>
	<style type="text/css">
		.seat {
			width: 50px;
		}

		input::-webkit-outer-spin-button,
		input::-webkit-inner-spin-button {
			-webkit-appearance: none;
			margin: 0;
		}
        .card-title{
            font-weight: bold !important;
        }
        em{
            color: red;
        }
  .loader {
 position: relative;
 width: 2.5em;
 height: 2.5em;
 transform: rotate(165deg);
}

.loader:before, .loader:after {
 content: "";
 position: absolute;
 top: 50%;
 left: 50%;
 display: block;
 width: 0.5em;
 height: 0.5em;
 border-radius: 0.25em;
 transform: translate(-50%, -50%);
}

.loader:before {
 animation: before8 2s infinite;
}

.loader:after {
 animation: after6 2s infinite;
}

@keyframes before8 {
 0% {
  width: 0.5em;
  box-shadow: 1em -0.5em rgba(225, 20, 98, 0.75), -1em 0.5em rgba(111, 202, 220, 0.75);
 }

 35% {
  width: 2.5em;
  box-shadow: 0 -0.5em rgba(225, 20, 98, 0.75), 0 0.5em rgba(111, 202, 220, 0.75);
 }

 70% {
  width: 0.5em;
  box-shadow: -1em -0.5em rgba(225, 20, 98, 0.75), 1em 0.5em rgba(111, 202, 220, 0.75);
 }

 100% {
  box-shadow: 1em -0.5em rgba(225, 20, 98, 0.75), -1em 0.5em rgba(111, 202, 220, 0.75);
 }
}

@keyframes after6 {
 0% {
  height: 0.5em;
  box-shadow: 0.5em 1em rgba(61, 184, 143, 0.75), -0.5em -1em rgba(233, 169, 32, 0.75);
 }

 35% {
  height: 2.5em;
  box-shadow: 0.5em 0 rgba(61, 184, 143, 0.75), -0.5em 0 rgba(233, 169, 32, 0.75);
 }

 70% {
  height: 0.5em;
  box-shadow: 0.5em -1em rgba(61, 184, 143, 0.75), -0.5em 1em rgba(233, 169, 32, 0.75);
 }

 100% {
  box-shadow: 0.5em 1em rgba(61, 184, 143, 0.75), -0.5em -1em rgba(233, 169, 32, 0.75);
 }
}

.loader {
 position: absolute;
 top: calc(50% - 1.25em);
 left: calc(50% - 1.25em);
}
	</style>
</head>

<body class="hold-transition sidebar-mini layout-fixed sidebar-collapse">
	<div class="wrapper">
		<!-- Preloader -->
		<div class="preloader flex-column justify-content-center align-items-center">
			<img class="animation__shake" src="/images/finger.gif" alt="CodeBucket" height="100"
				width="100" />
		</div>

		<!-- Navbar -->
		<%- include('partials/header'); %>
			<!-- /.navbar -->

			<!-- Main Sidebar Container -->
			<%- include('partials/sidebar'); %>

			<!-- Content Wrapper. Contains page content -->
<div class="content-wrapper">
    <!-- Content Header (Page header) -->
    <div class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1 class="m-0" style="color: blue;">Registration Details Of Employee</h1>
                </div>
                <!-- /.col -->
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="/">Home</a></li>
                        <li class="breadcrumb-item active">Dashboard</li>
                    </ol>
                </div>
                <!-- /.col -->
            </div>
            <!-- /.row -->
        </div>
        <!-- /.container-fluid -->
    </div>

    <!-- Main content -->
    <section class="content">
        <div class="container-fluid">
            <div class="row">
                <section class="col-12">
                    <div class="row">
                        <!-- <div class="col-lg-4 col-12"> -->
                            <!-- small card -->
                            <!-- <div class="small-box bg-info">
                                <div class="inner">
                                    <h3>Total Employee &nbsp; &nbsp;<span id="totalEmployee">111</span></h3>
                                    <p>Employee Details</p>
                                </div>
                                <div class="icon">
                                    <i class="ion ion-stats-bars"></i>
                                </div>
                                <a href="#pendinglist" onclick="showPendingList()" class="small-box-footer">
                                    View Pending List <i class="fas fa-arrow-circle-right"></i>
                                </a>
                            </div>
                        </div> -->

                        <div class="col-lg-6 col-12">
                            <!-- small card -->
                            <div class="small-box bg-primary">
                                <div class="inner">
                                    <h3>Total Saved Employee &nbsp; &nbsp; <span id="totalSavedEmployee">0</span></h3>
                                    <p>View All Saved Employees</p>
                                </div>
                                <div class="icon">
                                    <i class="ion ion-stats-bars"></i>
                                </div>
                                <a href="#showSavedEmployee" id="showSavedEmployeeLink" class="small-box-footer">
                                    View Details Of Saved Employees <i class="fas fa-arrow-circle-right"></i>
                                </a>
                            </div>
                        </div>

                        <div class="col-lg-6 col-12">
                            <!-- small card -->
                            <div class="small-box bg-success">
                                <div class="inner">
                                    <h3>Final Submit Employee &nbsp; &nbsp; <span id="totalFinalSubmitEmployee">0</span></h3>
                                    <p>View All  Final Submitted Employees</p>
                                </div>
                                <div class="icon">
                                    <i class="ion ion-stats-bars"></i>
                                </div>
                                <a href="#showSubmittedEmployee" id="showSubmittedEmployeeLink" class="small-box-footer">
                                    View Details Of Final Submitted Employees <i class="fas fa-arrow-circle-right"></i>
                                </a>
                            </div>
                        </div>
                    </div>
                </section>
                <div class="loader" style="display: none; margin-top: 10px;"></div>
                <section class="col-12">
                    <div id="savedEmployeeDetails" class="card card-default" style="display: none;">
                        <div class="card-header">
                            <h1 class="card-title" style="color: red;">Total Saved Employees:</h1>
                        </div>
                        <div class="card-body">
                        
                            <table class="table table-bordered" id="savedEmployeeTable" style="width: 100%;">
                                <thead>
                                    <tr>
                                        <th>Serail No.</th>
                                        <th>Name</th>
                                        <th>Payee ID</th>
                                        <th>Mobile Number</th>
                                        <th>Employee Type</th>
                                        <th>Current Designation</th>
                                        <th>Current Office/Department/College</th>
                                        <th>Date Of Retirement</th>
                                    </tr>
                                </thead>
                                <tbody id="savedEmployeeTbody">
                                    <!-- Table Body Here -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                
            </div>
        </div>
    </section>
    <div id="finalSubmittedEmployeeDetails" class="card card-default" style="display: none;">
        <div class="card-header">
         
            <h1 class="card-title" style="color: red;">Total Final Submitted Employees Details:</h1>
        </div>
        <div class="card-body">
            
            <table class="table table-bordered" id="finalSubmittedEmployeeTable" style="width: 100%;">
                <thead>
                    <tr>
                        <th>Serail No.</th>
                        <th>Name</th>
                        <th>Payee ID</th>
                        <th>Mobile Number</th>
                        <th>Employee Type</th>
                        <th>Current Designation</th>
                        <th>Current Office/Department/College</th>
                        <th>Date Of Retirement</th>
                    </tr>
                </thead>
                <tbody id="finalSubmittedEmployeeTbody">
                </tbody>
            </table>
        </div>
    </div>
</section>
</div>
    <script>
                        $.widget.bridge("uibutton", $.ui.button);
                    </script>
                    <!-- jQuery -->
                <!-- jQuery -->
                <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
                
                <!-- jQuery UI -->
                <script src="https://code.jquery.com/ui/1.12.1/jquery-ui.min.js"></script>
                
                <!-- Bootstrap 4 -->
                <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.bundle.min.js"></script>
                <!-- Select2 -->
                <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
                
                <!-- Moment.js -->
                <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.1/moment.min.js"></script>
                
                <!-- Date Range Picker -->
                <script src="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.min.js"></script>
                
                <!-- Tempusdominus Bootstrap 4 -->
                <script src="https://cdnjs.cloudflare.com/ajax/libs/tempusdominus-bootstrap-4/5.1.2/js/tempusdominus-bootstrap-4.min.js"></script>
                
                <!-- OverlayScrollbars -->
                <script src="https://cdnjs.cloudflare.com/ajax/libs/overlayscrollbars/1.13.0/js/jquery.overlayScrollbars.min.js"></script>
                
                <!-- AdminLTE App -->
                <script src="https://cdnjs.cloudflare.com/ajax/libs/admin-lte/3.2.0/js/adminlte.min.js"></script>
                <!-- jQuery Validation -->
                <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-validate/1.19.3/jquery.validate.min.js"></script>
                <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-validate/1.19.3/additional-methods.min.js"></script>
                
                <!-- Toastr -->
                <script src="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/js/toastr.min.js"></script>
                <!-- SweetAlert2 -->
                <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
                
                <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
                <!-- boxicon -->
                <script src="https://unpkg.com/boxicons@2.1.4/dist/boxicons.js"></script>
                <script>
                $(function () {
                    var apiBaseUrl = "<%= process.env.apiBaseUrl %>";
                    const token = localStorage.getItem("access-token-new");
		             var userDetails = localStorage.getItem('user-data');
		              var userDetailsObj = JSON.parse(userDetails);
                        const college = usersData.college;
                        const university = usersData.university;
		                 $('#college').text(college);
                         $('#college1').text(college);
                         $('#university1').text( university);
                        $.ajax({
                            url: apiBaseUrl + "v1/stage-two/total-employee",
                            method: 'POST',
                         
                            data: {
                                collegeName: college,
                                universityName: university
                            },
                            success: function(res) {
                              $("#totalEmployee").text(res.data.totalEmployee);
                              $("#totalSavedEmployee").text(res.data.totalSavedEmployee);
                              $("#totalFinalSubmitEmployee").text(res.data.totalFinalSubmittedEmployee);

                            },
                            error: function(error) {
                                console.error('API call failed:', error);
                               
                            }
                        });
             
    $(document).ready(function() {
    $('#showSavedEmployeeLink').on('click', function(e) {
        e.preventDefault();
        $('#finalSubmittedEmployeeDetails').hide();
        $('.loader').show();
        var apiBaseUrl = "<%= process.env.apiBaseUrl %>";
        $.ajax({
           url: apiBaseUrl + "v1/stage-two/total-employee",
            type: 'POST',
            contentType: 'application/json',
            data: JSON.stringify({
                collegeName: college,
                universityName: university,
                detailType: "saveEmployee"
            }),
            beforeSend: function(e){
                $('.loader').show();
            },
            success: function(response) {
                $('.loader').hide();
          
                var employees = response.data.details;

                $('#savedEmployeeTbody').empty();

                function formatDate(dateString) {
                    if (!dateString) {
                        return 'Invalid Date'; 
                    }
                    var parts = dateString.split('/');
                    if (parts.length !== 3) {
                        return 'Invalid Date'; 
                    }
                    var day = parts[0];
                    var month = parts[1];
                    var year = parts[2];
                    var date = new Date(year, month - 1, day); 

                    if (isNaN(date.getTime())) {
                        return 'Invalid Date'; 
                    }

                    return day + "/" + month + "/" + year;
                }
                employees.forEach(function(employee, index) {
                    var formattedDate = formatDate(employee.dateOfRetirement);

                    var row = '<tr>' +
                        '<td>' + (index + 1) + '</td>' +
                    '<td>' + employee.prefix + ' ' + employee.firstName + ' ' + employee.lastName + '</td>' +
                    '<td>' + employee.payIdNo + '</td>' +
                     "<td>" + (employee.personalMobileNumber ? employee.personalMobileNumber.slice(-3).padStart(employee.personalMobileNumber.length, 'x') : '') + "</td>"+
                     '<td>' + employee.employeeType + '</td>' +
                    '<td>' + employee.currentDesignation + '</td>' +
                    '<td>' + employee.currentlyPostedAtUniversityOfficeDepartmentCollege + '</td>' +
                    '<td>' + formattedDate + '</td>' +
                        '</tr>';
                    $('#savedEmployeeTbody').append(row);
                });

                $('#savedEmployeeDetails').show();
                $('#finalSubmittedEmployeeDetails').hide();
            },
            error: function(xhr, status, error) {
                console.error('AJAX Error: ' + status + error);
            }
        });
    });
});  
$('#showSubmittedEmployeeLink').on('click', function(e) {
        e.preventDefault();
        $('#savedEmployeeDetails').hide();
        var apiBaseUrl = "<%= process.env.apiBaseUrl %>";
        $.ajax({
           url: apiBaseUrl + "v1/stage-two/total-employee", 
            type: 'POST',
            contentType: 'application/json',
            data: JSON.stringify({
                collegeName: college,
                universityName: university,
                detailType:"finalSubmitEmployee"
            }),
            beforeSend: function(e){
                $('.loader').show();
            },
            success: function(response) {
                $('.loader').hide();
            var employees = response.data.details;
         
            // Clear existing table data
            $('#finalSubmittedEmployeeTbody').empty();
            function formatDate(dateString) {
                    if (!dateString) {
                        return 'Invalid Date'; 
                    }
                    
               
                    var parts = dateString.split('/');
                    if (parts.length !== 3) {
                        return 'Invalid Date'; 
                    }

                    var day = parts[0];
                    var month = parts[1];
                    var year = parts[2];

                   
                    var date = new Date(year, month - 1, day); 

                    if (isNaN(date.getTime())) {
                        return 'Invalid Date'; 
                    }

                    return day + "/" + month + "/" + year;
                }
         
           employees.forEach(function(employee, index) {
            var formattedDate = formatDate(employee.dateOfRetirement);
                var row = '<tr>' +
                    '<td>' + (index + 1) + '</td>' +
                    '<td>' + employee.prefix + ' ' + employee.firstName + ' ' + employee.lastName + '</td>' +
                    '<td>' + employee.payIdNo + '</td>' +
                     "<td>" + (employee.personalMobileNumber ? employee.personalMobileNumber.slice(-3).padStart(employee.personalMobileNumber.length, 'x') : '') + "</td>"+
                     '<td>' + employee.employeeType + '</td>' +
                    '<td>' + employee.currentDesignation + '</td>' +
                    '<td>' + employee.currentlyPostedAtUniversityOfficeDepartmentCollege + '</td>' +
                    '<td>' + formattedDate + '</td>' +
                    '</tr>';
                $('#finalSubmittedEmployeeTbody').append(row);
            });

            // Show the table section
            $('#finalSubmittedEmployeeDetails').show();
            $('#savedEmployeeDetails').hide(); 
        },
        error: function(xhr, status, error) {
            // Handle errors here
            console.error('AJAX Error: ' + status + error);
        }
});
});
})    
</script>
<script>
    function isTokenExpired(token) {
        if (!token) return true;
        try {
            // Decode JWT payload
            const payloadBase64 = token.split('.')[1];
            const payload = JSON.parse(atob(payloadBase64));
            // Check if current time is past the expiration time
            const isExpired = payload.exp * 1000 < Date.now();
            return isExpired;
        } catch (error) {
            console.error("Failed to decode token:", error);
            return true; 
        }
    }
    // Check token expiration on load
    $(function () {
       const token = localStorage.getItem("access-token-new");
       var userDetails = localStorage.getItem('user-data');
       const userData = JSON.parse(userDetails);
        if (token && userData) {
            // Check role and token expiration
            if (userData.role !== "College-Admin" || isTokenExpired(token)) {
                alert("Session expired or unauthorized access. Redirecting to login.");
                window.location = "/";
            } else {
                setInterval(() => {
                    if (isTokenExpired(token)) {
                        alert("Session expired. Please login again.");
                        window.location = "/";
                    }
                }, 60000);
            }
        } else {
            window.location = "/";
        }
    });
                    </script> 