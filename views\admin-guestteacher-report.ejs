<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8" />
  <meta http-equiv="X-UA-Compatible" content="IE=edge" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>
    <%= title %>
  </title>
  <!-- Google Font: Source Sans Pro -->
  <link rel="stylesheet"
    href="https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,400i,700&display=fallback" />
  <!-- Font Awesome -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css" />
  <!-- Ionicons -->
  <link rel="stylesheet" href="https://code.ionicframework.com/ionicons/2.0.1/css/ionicons.min.css" />
  <!-- Tempusdominus Bootstrap 4 -->
  <link rel="stylesheet"
    href="https://cdnjs.cloudflare.com/ajax/libs/tempusdominus-bootstrap-4/5.39.0/css/tempusdominus-bootstrap-4.min.css" />
  <!-- iCheck -->
  <link rel="stylesheet"
    href="https://cdnjs.cloudflare.com/ajax/libs/icheck-bootstrap/3.0.1/icheck-bootstrap.min.css" />
  <!-- Theme style -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/admin-lte/3.1.0/css/adminlte.min.css" />
  <!-- overlayScrollbars -->
  <link rel="stylesheet"
    href="https://cdnjs.cloudflare.com/ajax/libs/overlayscrollbars/1.13.0/css/OverlayScrollbars.min.css" />
  <!-- DataTables -->
  <link rel="stylesheet" href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap4.min.css">

  <link rel="stylesheet" href="https://cdn.datatables.net/responsive/2.2.9/css/responsive.bootstrap4.min.css">

  <link rel="stylesheet" href="https://cdn.datatables.net/buttons/2.0.1/css/buttons.bootstrap4.min.css">
  <!-- Baoxicon -->
  <link href='https://unpkg.com/boxicons@2.1.4/css/boxicons.min.css' rel='stylesheet'>
  <style type="text/css">
  </style>
</head>
<body class="hold-transition sidebar-mini layout-fixed sidebar-collapse">
  <div class="wrapper">
    <!-- Preloader -->
    <div class="preloader flex-column justify-content-center align-items-center">
      <img class="animation__shake" src="/images/loader.gif" alt="CodeBucket" height="100" width="100" />
    </div>
    <!-- Navbar -->
    <%- include('partials/header'); %>
      <!-- /.navbar -->
      <!-- Main Sidebar Container -->
      <%- include('partials/sidebar'); %>
        <div class="content-wrapper">
          <div class="content-header">
            <div class="container-fluid">
              <div class="row mb-2">
                <div class="col-sm-6">
                  <h1 style="color: black; font-weight: bolder;" class="m-0">Admin University&College Wise Guest Teacher
                    Report:</h1>
                </div>
                <div class="col-sm-6">
                  <ol class="breadcrumb float-sm-right">
                    <li class="breadcrumb-item"><a href="/adminreport">Home</a></li>
                    <li class="breadcrumb-item"><a href="/logout">Signout</a></li>
                  </ol>
                </div>
              </div>
            </div>
          </div>
          <section class="content">
            <div class="container-fluid">
              <div class="row">
                <section class="col-12">
                  <div class="card-header">
                    <button type="button" id="btnexcel" class="btn btn-primary" onclick="doExport()">
                      Export to Excel
                    </button>
                  </div>
                  <div class="card-body">
                    <div class="col-md-12">
                      <table id="mainTable" class="table table-bordered">
                        <thead id="thead"></thead>
                        <tbody id="tbody"></tbody>
                      </table>
                    </div>
                  </div>
              </div>
          </section>
          <div class="card card-default" id="seatdetails" style="display: none;">
            <div class="card-header">
              <h3>Deatils</h3>
            </div>
            <div class="card-header">
              <button type="button" id="btnexcel" class="btn btn-primary" onclick="doExportList()">
                Export to Excel
              </button>
            </div>
            <div class="card-body">
              <div class="col-md-12" style="overflow-x: scroll">
                <table class="table table-bordered" id="mainTable2">
                  <thead id="thead2"></thead>
                  <tbody id="tbody2"></tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
  </div>
  </div>
  </section>
  </div>
  <%- include('./partials/footer'); %>
    <aside class="control-sidebar control-sidebar-dark"></aside>
    </div>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- jQuery UI -->
    <script src="https://code.jquery.com/ui/1.12.1/jquery-ui.min.js"></script>
    <!-- Bootstrap 4 -->
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.bundle.min.js"></script>
    <!-- Moment.js -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.1/moment.min.js"></script>
    <!-- Tempusdominus Bootstrap 4 -->
    <script
      src="https://cdnjs.cloudflare.com/ajax/libs/tempusdominus-bootstrap-4/5.1.2/js/tempusdominus-bootstrap-4.min.js"></script>
    <!-- OverlayScrollbars -->
    <script
      src="https://cdnjs.cloudflare.com/ajax/libs/overlayscrollbars/1.13.0/js/jquery.overlayScrollbars.min.js"></script>
    <!-- AdminLTE App -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/admin-lte/3.2.0/js/adminlte.min.js"></script>
    <!-- jQuery Validation -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-validate/1.19.3/jquery.validate.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-validate/1.19.3/additional-methods.min.js"></script>
    <!-- Toastr -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/js/toastr.min.js"></script>
    <!-- boxicon -->
    <script src="https://unpkg.com/boxicons@2.1.4/dist/boxicons.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
    <!-- FileSaver.js -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/FileSaver.js/2.0.5/FileSaver.min.js"></script>
    <!-- js-xlsx -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.core.min.js"></script>
    <!-- tableExport.js -->
    <script src="https://cdn.jsdelivr.net/npm/tableexport.jquery.plugin@1.30.0/tableExport.min.js"></script>
    <script>
      function doExport() {
        $('#mainTable').tableExport({
          type: 'excel',
          mso: {
            styles: ['background-color', 'color', 'font-family', 'font-size', 'font-weight', 'text-align']
          }
        });
      }
      function doExportList() {
        $('#mainTable2').tableExport({
          type: 'excel',
          mso: {
            styles: ['background-color', 'color', 'font-family', 'font-size', 'font-weight', 'text-align']
          }
        });
      }
      function getData() {
        var apiBaseUrl = "<%= process.env.apiBaseUrl %>";
        $.ajax({
          url: apiBaseUrl + "v1/admin/admin-guest-teacher-report",
          type: "POST",
          headers: {
            token: localStorage.getItem("access-token-new"),
            "Content-Type": "application/json"
          },
          dataType: "json",
          success: function (res) {
            if (res.status == "success") {
              let thead =
                "<tr><th colspan='5' style='text-align:center'>University Guest-Teacher Filling Summary Report</th></tr>" +
                "<tr><th>S No.</th><th>University Name</th><th>Teaching Guest-Teacher Filling</th><th>Non Teaching Guest-Teacher Filling</th></tr>";
              let tbody = "";
              let totalTeachingCount = 0;
              let totalNonTeachingCount = 0;
              res.data.guestTeacherCounts.forEach(function (item, index) {
                totalTeachingCount += item.teachingCount;
                totalNonTeachingCount += item.nonTeachingCount;

                tbody += "<tr>";
                tbody += "<td style='text-align: center;'>" + (index + 1) + "</td>";
                tbody += "<td>" + item.universityName + "</td>";
                tbody += "<td style='text-align: center;'><a href='#seatdetails' class='teaching-submit-link' data-university-name='" + item.universityName + "' data-type='final_submit'>" + item.teachingCount + "</a></td>";
                tbody += "<td style='text-align: center;'><a href='#seatdetails' class='nonteaching-submit-link' data-university-name='" + item.universityName + "' data-type='save'>" + item.nonTeachingCount + "</a></td>";
                tbody += "</tr>";
              });
              tbody += `<tr>
                    <td>#</td>
                    <td>Grand Total</td>
                    <td style='text-align: center;'>${totalTeachingCount}</td>
                    <td style='text-align: center;'>${totalNonTeachingCount}</td>
                </tr>`;

              $("#thead").html(thead);
              $("#tbody").html(tbody);
              $(".starting-form-link, .teaching-submit-link, .nonteaching-submit-link").on("click", function (e) {
                e.preventDefault();
                const universityName = $(this).data("university-name");
                const type = $(this).data("type");
                let dataPayload = { universityName: universityName };
                if (type === "final_submit") {
                  dataPayload.employeeType = "T";
                } else if (type === "save") {
                  dataPayload.employeeType = "NT";
                }
                fetchDetails(dataPayload, type);
              });
            }
          }
        });
      }
      function fetchDetails(dataPayload, type) {
        var apiBaseUrl = "<%= process.env.apiBaseUrl %>";
        $.ajax({
          url: apiBaseUrl + "v1/admin/admin-guest-teacher-report",
          type: "POST",
          headers: {
            token: localStorage.getItem("access-token-new"),
            "Content-Type": "application/json"
          },
          data: JSON.stringify(dataPayload),
          dataType: "json",
          success: function (res) {
            if (res.status == "success") {
              let details = res.data.universityData;
              let thead2, tbody2 = "";

              if (type === "starting_form_filling_college") {
                thead2 = "<tr><th>UniversityName</th><th>Starting Form Filling College</th></tr>";
                details.forEach(function (detail) {
                  tbody2 += "<tr>";
                  tbody2 += "<td>" + detail.university_name_retirement + "</td>";
                  tbody2 += "<td>" + detail.college_name_retirement + "</td>";
                  tbody2 += "</tr>";
                });
              } else if (type === "final_submit") {
                thead2 = "<tr><th>Serail No.</th><th>Full Name</th><th>PayID No</th><th>EmployeeType</th><th>College Name</th><th>University Name</th></tr>";
                details.forEach(function (detail, index) {
                  tbody2 += "<tr>";
                  tbody2 += "<td>" + (index + 1) + "</td>";
                  tbody2 += '<td>' + ' ' + detail.firstName + ' ' + detail.lastName + '</td>';
                  tbody2 += "<td>" + detail.payIdNumber + "</td>";
                  tbody2 += "<td>" + detail.employeeType + "</td>";
                  tbody2 += "<td>" + detail.collegeatthetimeofJoining + "</td>";
                  tbody2 += "<td>" + detail.universityNameAtJoining + "</td>";
                  tbody2 += "</tr>";
                });
              } else if (type === "save") {
                thead2 = "<tr><th>Serail No.</th><th>Full Name</th><th>PayID No</th>th>EmployeeType</th><th>College Name</th><th>University Name</th></tr>";
                details.forEach(function (detail, index) {
                  tbody2 += "<tr>";
                  tbody2 += "<td>" + (index + 1) + "</td>";
                  tbody2 += '<td>' + ' ' + detail.first_name + ' ' + detail.last_name + '</td>';
                  tbody2 += "<td>" + detail.pay_id_no + "</td>";
                  tbody2 += "<td>" + detail.employeeType + "</td>";
                  tbody2 += "<td>" + detail.collegeatthetimeofJoining + "</td>";
                  tbody2 += "<td>" + detail.universityNameAtJoining + "</td>";
                  tbody2 += "</tr>";
                });
              }
              $("#thead2").html(thead2);
              $("#tbody2").html(tbody2);
              $("#seatdetails").show();
              $('#seatdetails')[0].scrollIntoView();
            }
          }
        });
      }
      $(document).ready(function () {
        getData();
      });
    </script>
    <script>
      $(function () {
        if (localStorage.getItem("access-token-new") && localStorage.getItem("user-data")) {
          const usersData = JSON.parse(localStorage.getItem("user-data"));
          if (usersData.role != "Admin") {
            alert("You are not authorized to Access This Page");
            window.location = "/";
          }
        }
        else window.location = "/";
      });
    </script>