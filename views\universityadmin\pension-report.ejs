<!DOCTYPE html>
<html lang="en">
<head>
	<meta charset="UTF-8" />
	<meta http-equiv="X-UA-Compatible" content="IE=edge" />
	<meta name="viewport" content="width=device-width, initial-scale=1.0" />
	<title>
		<%= title %>
	</title>
	<!-- Google Font: Source Sans Pro -->
        <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,400i,700&display=fallback" />

        <!-- Font Awesome -->
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css" />

        <!-- Ionicons -->
        <link rel="stylesheet" href="https://code.ionicframework.com/ionicons/2.0.1/css/ionicons.min.css" />
        <!-- Tempusdominus Bootstrap 4 -->
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/tempusdominus-bootstrap-4/5.39.0/css/tempusdominus-bootstrap-4.min.css" />
        <!-- iCheck -->
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/icheck-bootstrap/3.0.1/icheck-bootstrap.min.css" />
        <!-- Select2 -->
        <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
        <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/select2@4.0.13/dist/css/select2.min.css" />
        <!-- Theme style -->
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/admin-lte/3.1.0/css/adminlte.min.css" />
        <!-- overlayScrollbars -->
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/overlayscrollbars/1.13.0/css/OverlayScrollbars.min.css" />
        <!-- Daterange picker -->
        <link rel="stylesheet" href="https://cdn.datatables.net/1.10.24/css/dataTables.bootstrap4.min.css">
        <!-- DataTables -->
        <link rel="stylesheet" href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap4.min.css">
    
        <link rel="stylesheet" href="https://cdn.datatables.net/responsive/2.2.9/css/responsive.bootstrap4.min.css">
    
        <link rel="stylesheet" href="https://cdn.datatables.net/buttons/2.0.1/css/buttons.bootstrap4.min.css">
        <!-- Baoxicon -->
	<link href='https://unpkg.com/boxicons@2.1.4/css/boxicons.min.css' rel='stylesheet'>
	<style type="text/css">
	</style>
</head>

<body class="hold-transition sidebar-mini layout-fixed sidebar-collapse">
	<div class="wrapper">
		<!-- Preloader -->
		<div class="preloader flex-column justify-content-center align-items-center">
			<img class="animation__shake" src="/images/loader.gif" alt="CodeBucket" height="100"
				width="100" />
		</div>
    <%- include('../partials/header'); %> <%- include('../partials/sidebar'); %>
                   <div class="content-wrapper">
                        <div class="content-header">
                          <div class="container-fluid">
                            <div class="row mb-2">
                              <div class="col-sm-6">
                                <h1 style="color: red; font-weight: bolder;" class="m-0" >Pension Entry /Bulk Upload Report (College Wise)</h1>
                              </div>
                              <div class="col-sm-6">
                                <ol class="breadcrumb float-sm-right">
                                  <li class="breadcrumb-item"><a href="/universityadmin">Home</a></li>
                                  <li class="breadcrumb-item"><a href="/logout">Signout</a></li>
                                </ol>
                              </div>
                            </div>
                          </div>
                        </div>
                        <section class="content">
                          <div class="container-fluid">
                            <div class="row">
                              <section class="col-12">
                                  <div class="card-header">
                                    <button
                                      type="button"
                                      id="btnexcel"
                                      class="btn btn-primary"
                                      onclick="doExport()"
                                    >
                                      Export to Excel
                                    </button>
                                  </div>
                                  <div class="card-body">
                                    <div class="col-md-12">
                                      <table id="mainTable" class="table table-bordered">
                                        <thead id="thead"></thead>
                                        <tbody id="tbody"></tbody>
                                      </table>
                                    </div>
                                  </div>
                                </div>
                              </section>
                              <div class="card card-default" id="seatdetails" style="display: none;">
                                <div class="card-header">
                                  <h3>Deatils</h3>
                                </div>
                                <div class="card-header">
                                  <button
                                    type="button"
                                    id="btnexcel"
                                    class="btn btn-primary"
                                    onclick="doExportList()"
                                  >
                                    Export to Excel
                                  </button>
                                </div>
                                <div class="card-body">
                                  <div class="col-md-12" style="overflow-x: scroll">
                                    <table
                                      class="table table-bordered"
                                      id="mainTable2"
                                    >
                                      <thead id="thead2"></thead>
                                      <tbody id="tbody2"></tbody>
                                    </table>
                                  </div>
                                </div>
                                  </div>
                              </div>
                            </div>
                          </div>
                        </section>
                      </div>
                      <%- include('../partials/footer'); %>
                      <aside class="control-sidebar control-sidebar-dark"></aside>
                    </div>
                <!-- jQuery -->
                <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
                <!-- jQuery UI -->
                <script src="https://code.jquery.com/ui/1.12.1/jquery-ui.min.js"></script>
                <!-- Bootstrap 4 -->
                <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.bundle.min.js"></script>
                <!-- Select2 -->
                <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
                
                <!-- Moment.js -->
                <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.1/moment.min.js"></script>
                
                <!-- Tempusdominus Bootstrap 4 -->
                <script src="https://cdnjs.cloudflare.com/ajax/libs/tempusdominus-bootstrap-4/5.1.2/js/tempusdominus-bootstrap-4.min.js"></script>
                
                <!-- OverlayScrollbars -->
                <script src="https://cdnjs.cloudflare.com/ajax/libs/overlayscrollbars/1.13.0/js/jquery.overlayScrollbars.min.js"></script>
                
                <!-- AdminLTE App -->
                <script src="https://cdnjs.cloudflare.com/ajax/libs/admin-lte/3.2.0/js/adminlte.min.js"></script>
                
                <!-- jQuery Validation -->
                <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-validate/1.19.3/jquery.validate.min.js"></script>
                <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-validate/1.19.3/additional-methods.min.js"></script>
                
                <!-- Toastr -->
                <script src="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/js/toastr.min.js"></script>
                
                <!-- SweetAlert2 -->
                <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
                
                <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
                <!-- boxicon -->
                <script src="https://unpkg.com/boxicons@2.1.4/dist/boxicons.js"></script>
                <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
                <!-- FileSaver.js -->
                <script src="https://cdnjs.cloudflare.com/ajax/libs/FileSaver.js/2.0.5/FileSaver.min.js"></script>
                <!-- jsPDF -->
                <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
                <!-- js-xlsx -->
                <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.core.min.js"></script>
                <!-- tableExport.js -->
                <script src="https://cdn.jsdelivr.net/npm/tableexport.jquery.plugin@1.30.0/tableExport.min.js"></script>
                <script>
          var apiBaseUrl = "<%= process.env.apiBaseUrl %>";
         const token = localStorage.getItem("access-token-new");
		     var userDetails = localStorage.getItem('user-data');
		    var userDetailsObj = JSON.parse(userDetails);
        var universityName= userDetailsObj.university
        $('#university1').append(universityName);
        $('#college1').append(universityName);
        console.log(universityName)
    function doExport() {
      $('#mainTable').tableExport({
        type: 'excel',
        mso: {
          styles: ['background-color', 'color', 'font-family', 'font-size', 'font-weight', 'text-align']
        }
      });
    }
    function doExportList() {
      $('#mainTable2').tableExport({
        type: 'excel',
        mso: {
          styles: ['background-color', 'color', 'font-family', 'font-size', 'font-weight', 'text-align']
        }
      });
    }
 
   function getData() {
    $.ajax({
       url: apiBaseUrl + "v1/stage-two/get-pension-data",
        type: "POST",
        headers: {
            token: localStorage.getItem("access-token-new"),
            "Content-Type": "application/json"
        },
        data: JSON.stringify({
                universityName: universityName
            }),
        dataType: "json",
        success: function(res) {
    if (res.status == "success") {
        let thead =
            "<tr><th colspan='4' style='text-align:center'>College Wise Pension Summary Report</th></tr>" +
            "<tr><th>S No.</th><th>College Name</th><th>Teaching</th><th>Non-Teaching</th></tr>";

        let tbody = "";
        let totalTeaching = 0;
        let totalNonTeaching = 0;

        // Iterate over the data array to populate the table
        res.data.data.forEach(function(item, index) {
            totalTeaching += item.Teaching_count;
            totalNonTeaching += item.Non_Teaching_count;

            tbody += "<tr>";
            tbody += "<td style='text-align: center;'>" + (index + 1) + "</td>";
            tbody += "<td>" + item.college_name_retirement + "</td>";
            tbody += "<td style='text-align: center;'><a href='#seatdetails' class='final-submit-link' data-university-name='" + item.college_name_retirement + "' data-type='T'>" + item.Teaching_count + "</a></td>";
            tbody += "<td style='text-align: center;'><a href='#seatdetails' class='save-link' data-university-name='" + item.college_name_retirement + "' data-type='NT'>" + item.Non_Teaching_count + "</a></td>";
            tbody += "</tr>";
        });

        // Append the grand total row
        tbody += `<tr>
                    <td>#</td>
                    <td>Grand Total</td>
                    <td style='text-align: center;'>${totalTeaching}</td>
                    <td style='text-align: center;'>${totalNonTeaching}</td>
                  </tr>`;

        // Update the table with the constructed HTML
        $("#thead").html(thead);
        $("#tbody").html(tbody);
    

            $(".final-submit-link, .save-link").on("click", function(e) {
                    e.preventDefault();
                    const collegeName = $(this).data("university-name");
                    const employeType = $(this).data("type");

                    let dataPayload = { collegeName: collegeName,universityName:universityName };

                    if (employeType === "T") {
                        dataPayload.employeType = "T";
                    } else if (employeType === "NT") {
                        dataPayload.employeType = "NT";
                    }

                    fetchDetails(dataPayload, employeType);
                });
            }
        }
    });
}



function fetchDetails(dataPayload, employeType) {
    $.ajax({
        url: apiBaseUrl + "v1/stage-two/get-pension-data",
        type: "POST",
        headers: {
            token: localStorage.getItem("access-token-new"),
            "Content-Type": "application/json"
        },
        data: JSON.stringify(dataPayload),
        dataType: "json",
        success: function(res) {
            if (res.status == "success") {
                let details =  res.data.pensionData;
                let thead2, tbody2 = "";
                
            
                 if (employeType === "T") {
                    thead2 = "<tr><th>Serial No.</th><th>Prefix</th><th>Full Name</th><th>Gender</th><th>Date of Birth</th><th>Father's Name</th><th>Mother's Name</th><th>Spouse Name</th><th>Email ID</th><th>Mobile No.</th><th>PPO No.</th><th>PRAN No.</th><th>PAN No.</th><th>Aadhar No.</th><th>PayID No</th><th>Employee Type</th><th>Appointment Letter Date</th><th>Effective Date of Joining</th><th>Date of Retirement</th><th>University Name at Retirement</th><th>College Name at Retirement</th><th>Bank Account No.</th><th>Bank Name</th><th>Branch Name</th><th>IFSC Code</th><th>Basic Pension</th><th>Dearness Relief Percentage</th><th>Medical Allowance</th><th>Gratuity</th><th>Leave Encashment</th><th>Arrears</th><th>All One-Time Benefits Received</th></tr>";

    details.forEach(function(detail, index) {
        tbody2 += "<tr>";
        tbody2 += "<td>" + (index + 1) + "</td>";
        tbody2 += "<td>" + detail.prefix + "</td>";
        tbody2 += "<td>" + detail.first_name + ' ' + detail.middle_name + ' ' + detail.last_name + "</td>";
        tbody2 += "<td>" + detail.gender + "</td>";
        tbody2 += "<td>" + detail.date_of_birth + "</td>";
        tbody2 += "<td>" + detail.fathers_name + "</td>";
        tbody2 += "<td>" + detail.mothers_name + "</td>";
        tbody2 += "<td>" + detail.spouse_name + "</td>";
        tbody2 += "<td>" + detail.email_id + "</td>";
        tbody2 += "<td>" + (detail.mobile_no ? detail.mobile_no.slice(-3).padStart(detail.mobile_no.length, 'x') : '') + "</td>";
        tbody2 += "<td>" + detail.ppo_no + "</td>";
        tbody2 += "<td>" + detail.pran_no + "</td>";
        tbody2 += "<td>" + detail.pan_no + "</td>";
        tbody2 += "<td>" + (detail.aadhar_no ? detail.aadhar_no.slice(-4).padStart(detail.aadhar_no.length, 'x') : '') + "</td>";
        tbody2 += "<td>" + detail.pay_id_no + "</td>";
        tbody2 += "<td>" + detail.employee_type + "</td>";
        tbody2 += "<td>" + detail.appointment_letter + "</td>";
        tbody2 += "<td>" + detail.effective_date_joining + "</td>";
        tbody2 += "<td>" + detail.date_retirement + "</td>";
        tbody2 += "<td>" + detail.university_name_retirement + "</td>";
        tbody2 += "<td>" + detail.college_name_retirement + "</td>";
        tbody2 += "<td>" + (detail.bank_account_no ? detail.bank_account_no.slice(-4).padStart(detail.bank_account_no.length, 'x') : '') + "</td>";
        tbody2 += "<td>" + detail.bank_name + "</td>";
        tbody2 += "<td>" + detail.branch_name + "</td>";
        tbody2 += "<td>" + detail.ifsc_code + "</td>";
        tbody2 += "<td>" + detail.basic_pension + "</td>";
        tbody2 += "<td>" + detail.dearness_relief_percentage + "</td>";
        tbody2 += "<td>" + detail.medical_allowance + "</td>";
        tbody2 += "<td>" + detail.gratuity + "</td>";
        tbody2 += "<td>" + detail.leave_encashment + "</td>";
        tbody2 += "<td>" + detail.arrears + "</td>";
        tbody2 += "<td>" + detail.all_one_time_benifit_recieved + "</td>";
        tbody2 += "</tr>";
                    });
                } else if (employeType === "NT") {
                    thead2 = "<tr><th>Serial No.</th><th>Prefix</th><th>Full Name</th><th>Gender</th><th>Date of Birth</th><th>Father's Name</th><th>Mother's Name</th><th>Spouse Name</th><th>Email ID</th><th>Mobile No.</th><th>PPO No.</th><th>PRAN No.</th><th>PAN No.</th><th>Aadhar No.</th><th>PayID No</th><th>Employee Type</th><th>Appointment Letter Date</th><th>Effective Date of Joining</th><th>Date of Retirement</th><th>University Name at Retirement</th><th>College Name at Retirement</th><th>Bank Account No.</th><th>Bank Name</th><th>Branch Name</th><th>IFSC Code</th><th>Basic Pension</th><th>Dearness Relief Percentage</th><th>Medical Allowance</th><th>Gratuity</th><th>Leave Encashment</th><th>Arrears</th><th>All One-Time Benefits Received</th></tr>";

details.forEach(function(detail, index) {
    tbody2 += "<tr>";
    tbody2 += "<td>" + (index + 1) + "</td>";
    tbody2 += "<td>" + detail.prefix + "</td>";
    tbody2 += "<td>" +  detail.first_name + ' ' + detail.middle_name + ' ' + detail.last_name + "</td>";
    tbody2 += "<td>" + detail.gender + "</td>";
    tbody2 += "<td>" + detail.date_of_birth + "</td>";
    tbody2 += "<td>" + detail.fathers_name + "</td>";
    tbody2 += "<td>" + detail.mothers_name + "</td>";
    tbody2 += "<td>" + detail.spouse_name + "</td>";
    tbody2 += "<td>" + detail.email_id + "</td>";
    tbody2 += "<td>" + (detail.mobile_no ? detail.mobile_no.slice(-3).padStart(detail.mobile_no.length, 'x') : '') + "</td>";
    tbody2 += "<td>" + detail.ppo_no + "</td>";
    tbody2 += "<td>" + detail.pran_no + "</td>";
    tbody2 += "<td>" + detail.pan_no + "</td>";
    tbody2 += "<td>" + (detail.aadhar_no ? detail.aadhar_no.slice(-4).padStart(detail.aadhar_no.length, 'x') : '') + "</td>";
    tbody2 += "<td>" + detail.pay_id_no + "</td>";
    tbody2 += "<td>" + detail.employee_type + "</td>";
    tbody2 += "<td>" + detail.appointment_letter + "</td>";
    tbody2 += "<td>" + detail.effective_date_joining + "</td>";
    tbody2 += "<td>" + detail.date_retirement + "</td>";
    tbody2 += "<td>" + detail.university_name_retirement + "</td>";
    tbody2 += "<td>" + detail.college_name_retirement + "</td>";
    tbody2 += "<td>" + (detail.bank_account_no ? detail.bank_account_no.slice(-4).padStart(detail.bank_account_no.length, 'x') : '') + "</td>";
    tbody2 += "<td>" + detail.bank_name + "</td>";
    tbody2 += "<td>" + detail.branch_name + "</td>";
    tbody2 += "<td>" + detail.ifsc_code + "</td>";
    tbody2 += "<td>" + detail.basic_pension + "</td>";
    tbody2 += "<td>" + detail.dearness_relief_percentage + "</td>";
    tbody2 += "<td>" + detail.medical_allowance + "</td>";
    tbody2 += "<td>" + detail.gratuity + "</td>";
    tbody2 += "<td>" + detail.leave_encashment + "</td>";
    tbody2 += "<td>" + detail.arrears + "</td>";
    tbody2 += "<td>" + detail.all_one_time_benifit_recieved + "</td>";
    tbody2 += "</tr>";
                });
                }

                $("#thead2").html(thead2);
                $("#tbody2").html(tbody2);
                $("#seatdetails").show();
                $('#seatdetails')[0].scrollIntoView();
            }
        }
    });
}
$(document).ready(function() {
    getData();
});
 </script>
  <script>
    function isTokenExpired(token) {
        if (!token) return true;
        try {
            // Decode JWT payload
            const payloadBase64 = token.split('.')[1];
            const payload = JSON.parse(atob(payloadBase64));
            // Check if current time is past the expiration time
            const isExpired = payload.exp * 1000 < Date.now();
            return isExpired;
        } catch (error) {
            console.error("Failed to decode token:", error);
            return true; 
        }
    }
    // Check token expiration on load
    $(function () {
       const token = localStorage.getItem("access-token-new");
        const userData = JSON.parse(userDetails);
        if (token && userData) {
            // Check role and token expiration
            if (usersData.role !== "University-Admin" || isTokenExpired(token)) {
                alert("Session expired or unauthorized access. Redirecting to login.");
                window.location = "/";
            } else {
                setInterval(() => {
                    if (isTokenExpired(token)) {
                        alert("Session expired. Please login again.");
                        window.location = "/";
                    }
                }, 60000);
            }
        } else {
            window.location = "/";
        }
    });
                    </script> 