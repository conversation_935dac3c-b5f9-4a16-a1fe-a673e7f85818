<!DOCTYPE html>
<html lang="en">
	<head>
		<meta charset="UTF-8" />
		<meta http-equiv="X-UA-Compatible" content="IE=edge" />
		<meta name="viewport" content="width=device-width, initial-scale=1.0" />
		<title><%= title %></title>
		<!-- Google Font: Source Sans Pro -->
		<link
			rel="stylesheet"
			href="https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,400i,700&display=fallback"
		/>
		<!-- Google Font: Source Sans Pro -->
	<link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,400i,700&display=fallback" />

	<!-- Font Awesome -->
	<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css" />

	<!-- Ionicons -->
	<link rel="stylesheet" href="https://code.ionicframework.com/ionicons/2.0.1/css/ionicons.min.css" />


	<!-- Tempusdominus Bootstrap 4 -->
	<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/tempusdominus-bootstrap-4/5.39.0/css/tempusdominus-bootstrap-4.min.css" />

	<!-- iCheck -->
	<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/icheck-bootstrap/3.0.1/icheck-bootstrap.min.css" />

	<!-- Select2 -->
	<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
	<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/select2@4.0.13/dist/css/select2.min.css" />
	
	<!-- Theme style -->
	<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/admin-lte/3.1.0/css/adminlte.min.css" />
		<style>
  
			.alert { 
				color: #3c763d;
				padding: 15px;
				margin-bottom: 20px;
				border: 1px solid transparent;
				border-radius: 4px;
			}
			.alert a {
					text-decoration: none;
				}
			#h1 {
				margin-top: 0;
				margin-bottom: 0;
			}
			@media only screen and (max-width: 600px) {
			   
				.alert {
					font-size: 14px;
					padding: 10px;
				}
				.alert a {
					text-decoration: none;
				}
			   
				#h1 {
					font-size: 13px;
				}
			}
			.input-group-text{
				color: red;
			}
			#btnlogin {
  font-size: x-large;
  letter-spacing: 3px;
  color: white;
  font-weight: 700;
  height: 18%;
  background: linear-gradient(144deg,#af40ff,
              #5b42f3 50%,#00ddeb);
  border-radius: 8px;
  border: none;
  box-shadow: inset 1px 3px 3px #ffffffbd,
               inset -4px -4px 3px #00000046;
  background-size: 150% 150%;
  animation: input 5s infinite;
  transition: all 900ms ease-in;
}

#btnlogin:hover {
  position: relative;
  bottom: 3px;
  background: linear-gradient(144deg,#9706ff,
              #2f0fff 50%,#18f0ff);
}
.card-footer {
	background-color:black;
	color: white;
}
.card-footer a {
	color: red;

}
</style>
</head>
	<nav class="header fixed-top">
		<div class="container-fluid no-padding d-flex justify-content-between align-items-center">
			<div class="logo-left">
				<img src="/images/BIHARSARKAR.png" alt="Right Logo" height="90px" width="100px">
				
			
		
			</div>
			<div class="logo-center">
				<div class="logo-center  mt-2">
					<div class="text-center">
						<h3 id="h1"><a href="/">EDUCATION DEPARTMENT</a></h3>
						<h3 id="h1"><a href="/">शिक्षा विभाग</a></h3>
					</div>
					<!-- <div class="alert alert-success text-center">
						<h3 id="h1"><a href="/grievance">आधारभूत संरचना एवं बेंच-डेस्क (फर्नीचर) से सम्बंधित शिकायत के लिए पोर्टल</a></h3>
					</div> -->
				</div>
			</div>
			
			<div class="logo-right">
				<img src="/images/gov1.png" alt="Left Logo" height="90px" width="80px">
				
			</div>
		</div>
	</nav>

	<body class="hold-transition login-page">
		<div class="login-box">
			<!-- /.login-logo -->
			<div class="card card-outline card-primary">
				<div class="card-header text-center">
					<a href="/" class="h1" style="color:  rgb(255, 0, 119);;"><b><%= productName %></b></a>
				</div>
				<div class="card-body">
                    <p class="login-box-msg" style="color: rgb(255, 0, 47); font-size: larger; font-weight: bold;" >University Login Or College Login</p>
					<p class="login-box-msg" style="color: blue;">Sign in to start your session</p>

					<form action="" id="frmlogin" name="frmlogin" method="post">
						<div class="input-group mb-3">
							<div class="input-group-append">
								<div class="input-group-text">
									<span class="fas fa-user"></span>
								</div>
							</div>
							<input
								type="text"
								class="form-control"
								id="username"
								name="username"
								placeholder="Enter Username"
							/>
						</div>
						<div class="input-group mb-3">
							<div class="input-group-append">
								<div class="input-group-text">
									<span class="fas fa-lock"></span>
								</div>
							</div>
							<input
								type="password"
								id="password"
								name="password"
								class="form-control"
								placeholder="Enter Password"
							/>
						</div>
						<div class="row">
							<div class="col-8"></div>
							<!-- /.col -->
							
								<button
									type="submit"
									id="btnlogin"
									name="btnlogin"
									
								>
									Sign In
								</button>
							</div>
							<!-- /.col -->
						</div>
					</form>
				</div>
				<!-- /.card-body -->
				<!-- <div class="card-footer"><center> New User?  <a href="/register">  Click Here</a> to register</center></div> -->
			</div>
			
			<!-- /.card -->
		</div>
		<!-- /.login-box -->
		<!-- jQuery -->
			<!-- jQuery -->
<!-- jQuery -->
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

<!-- jQuery UI -->
<script src="https://code.jquery.com/ui/1.12.1/jquery-ui.min.js"></script>

<!-- Bootstrap 4 -->
<script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.bundle.min.js"></script>
	
<!-- AdminLTE App -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/admin-lte/3.2.0/js/adminlte.min.js"></script>

<!-- jQuery Validation -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-validate/1.19.3/jquery.validate.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-validate/1.19.3/additional-methods.min.js"></script>

<!-- Toastr -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/js/toastr.min.js"></script>

<script>
$(function () {
    $("#frmlogin").validate({
        rules: {
            username: {
                required: true,
            },
            password: {
                required: true,
            },
        },
        messages: {
            username: {
                required: "Please enter username",
            },
            password: {
                required: "Please enter password",
            },
        },
        errorElement: "span",
        errorPlacement: function (error, element) {
            error.addClass("invalid-feedback");
            element.closest(".input-group").append(error);
        },
        highlight: function (element, errorClass, validClass) {
            $(element).addClass("is-invalid");
        },
        unhighlight: function (element, errorClass, validClass) {
            $(element).removeClass("is-invalid");
        },
    });

    $("#frmlogin").on("submit", function (event) {
        event.preventDefault();

        // Fetch user's IP address using ipify API
        $.get("https://api.ipify.org?format=json", function (data) {
            const userIp = data.ip; // Extract user's IP address
            const apiBaseUrl = "<%= process.env.apiBaseUrl %>";
            console.log(userIp)
            const reqdata = {
                userName: $("#username").val(),
                password: $("#password").val(),
                ip: userIp,
            };

            console.log("Request Data:", JSON.stringify(reqdata));

            $.ajax({
                url: apiBaseUrl + "v1/auth/login",
                method: "POST",
                headers: {
                    "Content-Type": "application/json",
                },
                data: JSON.stringify(reqdata),
                dataType: "JSON",
                beforeSend: function () {
                    $("#btnlogin").attr("disabled", "disabled");
                },
                success: function (res) {
                    $("#btnlogin").attr("disabled", false);
                    if (res.status == "success") {
                        localStorage.setItem("access-token-new", res.data.token);
                        localStorage.setItem("user-data", JSON.stringify(res.data.data));
                        $(document).Toasts("create", {
                            class: "bg-success",
                            autohide: true,
                            delay: 3000,
                            title: "Success",
                            body: "Login success.",
                        });

                        if (res.data.data.role === "Admin") {
                            window.location = "/adminreport";
                        } else if (res.data.data.role === "College-Admin") {
                            window.location = "/mapped-college-number";
                        }
                    } else {
                        $(document).Toasts("create", {
                            class: "bg-danger",
                            autohide: true,
                            delay: 2000,
                            title: "Error",
                            body: "Username or password is invalid.",
                        });
                    }
                },
                error: function (res) {
                    $("#btnlogin").attr("disabled", false);
                    $(document).Toasts("create", {
                        class: "bg-danger",
                        autohide: true,
                        delay: 2000,
                        title: "Error",
                        body: "Username or password is invalid.",
                    });
                },
            });
        }).fail(function () {
            $(document).Toasts("create", {
                class: "bg-danger",
                autohide: true,
                delay: 2000,
                title: "Error",
                body: "Unable to fetch IP address.",
            });
        });
    });
});
</script>
	</body>
</html>