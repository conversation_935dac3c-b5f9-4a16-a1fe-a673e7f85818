<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>
        <%= title %>
    </title>
    <!-- Google Font: Source Sans Pro -->
    <!-- Google Font: Source Sans Pro -->
    <link rel="stylesheet"
        href="https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,400i,700&display=fallback" />

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css" />

    <!-- Ionicons -->
    <link rel="stylesheet" href="https://code.ionicframework.com/ionicons/2.0.1/css/ionicons.min.css" />


    <!-- Tempusdominus Bootstrap 4 -->
    <link rel="stylesheet"
        href="https://cdnjs.cloudflare.com/ajax/libs/tempusdominus-bootstrap-4/5.39.0/css/tempusdominus-bootstrap-4.min.css" />

    <!-- iCheck -->
    <link rel="stylesheet"
        href="https://cdnjs.cloudflare.com/ajax/libs/icheck-bootstrap/3.0.1/icheck-bootstrap.min.css" />

    <!-- Select2 -->
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/select2@4.0.13/dist/css/select2.min.css" />

    <!-- Theme style -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/admin-lte/3.1.0/css/adminlte.min.css" />

    <!-- overlayScrollbars -->
    <link rel="stylesheet"
        href="https://cdnjs.cloudflare.com/ajax/libs/overlayscrollbars/1.13.0/css/OverlayScrollbars.min.css" />

    <!-- Daterange picker -->
    <link rel="stylesheet" href="https://cdn.datatables.net/1.10.24/css/dataTables.bootstrap4.min.css">


    <!-- DataTables -->
    <link rel="stylesheet" href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap4.min.css">

    <link rel="stylesheet" href="https://cdn.datatables.net/responsive/2.2.9/css/responsive.bootstrap4.min.css">

    <link rel="stylesheet" href="https://cdn.datatables.net/buttons/2.0.1/css/buttons.bootstrap4.min.css">
    <!-- Baoxicon -->
    <link href='https://unpkg.com/boxicons@2.1.4/css/boxicons.min.css' rel='stylesheet'>
    <style type="text/css">
        .seat {
            width: 50px;
        }

        input::-webkit-outer-spin-button,
        input::-webkit-inner-spin-button {
            -webkit-appearance: none;
            margin: 0;
        }

        .card-title {
            font-weight: bold !important;
        }

        em {
            color: red;
        }

        .card-primary1 {
            background-color: green;
        }
    </style>
</head>

<body class="hold-transition sidebar-mini layout-fixed sidebar-collapse">
    <div class="wrapper">
        <!-- Preloader -->
        <div class="preloader flex-column justify-content-center align-items-center">
            <img class="animation__shake" src="/images/loader.gif" alt="CodeBucket" height="100" width="100" />
        </div>
        <%- include('../partials/header'); %> <%- include('../partials/sidebar'); %>


                <!-- Content Wrapper. Contains page content -->
                <div class="content-wrapper">
                    <!-- Content Header (Page header) -->
                    <div class="content-header">
                        <div class="container-fluid">
                            <div class="row mb-2">
                                <div class="col-sm-6">
                                    <h1 class="m-0" style="color: red; font-weight: bolder;"> Edit Family-Pensioner
                                        Details</h1>
                                </div>
                                <!-- /.col -->
                                <div class="col-sm-6">
                                    <ol class="breadcrumb float-sm-right">
                                        <li class="breadcrumb-item"><a href="/">Home</a></li>
                                        <li class="breadcrumb-item active">Edit Family-Pensioner Details</li>
                                    </ol>
                                </div>
                                <!-- /.col -->
                            </div>
                            <!-- /.row -->
                        </div>
                        <!-- /.container-fluid -->
                    </div>
                    <section class="content">
                        <div class="container-fluid">
                            <div class="row">
                                <section class="col-12">
                                    <form id="frmsearch" name="frmsearch" action="" method="post">
                                        <div class="card card-warning">
                                            <!-- /.card-header -->
                                            <div class="card-header">
                                                <h3 class="card-title">Search Family-Pensioner and Edit Deatils</h3>
                                            </div>
                                            <div class="card-body">
                                                <div class="form-group d-flex align-items-center">
                                                    <label for="payeeid" class="mr-2" style="color: red;">PPO
                                                        Number</label>
                                                    <input type="text" class="form-control mr-2" name="ppoNo" id="ppoNo"
                                                        placeholder="Enter PPO Number to Fetch Deatils" style="flex: 1">
                                                </div>
                                            </div>
                                            <div class="card-footer">
                                                <button type="button" id="btnsearch"
                                                    class="btn btn-danger float-right mr-1">
                                                    Search Pensioner
                                                </button>
                                            </div>
                                        </div>
                                    </form>
                                    <div id="pensionDetails" >
                                        <form id="frmregister" name="frmregister" action="" method="post">
                                            <div class="card card-danger 1">
                                                <div class="card-header">
                                                    <h3 class="card-title">Family Pensioner Personal Details</h3>
                                                </div>
                                                <div class="card-body">
                                                    <div class="row">
                                                        <div class="col-md-6">
                                                            <div class="form-group">
                                                                <label>Prefix<em>*</em></label>
                                                                <select class="form-control select2" name="prefix"
                                                                    id="prefix" style="width: 100%">
                                                                    <option value="Mr.">Mr.</option>
                                                                    <option value="Ms.">Ms.</option>
                                                                    <option value="Mrs.">Mrs.</option>
                                                                    <option value="Dr.">Dr.</option>
                                                                    <option value="Prof.">Prof.</option>
                                                                    <option value="Shri">Shri</option>
                                                                </select>
                                                            </div>
                                                        </div>
                                                        <div class="col-md-6">
                                                            <div class="form-group">
                                                                <label>First Name<em>*</em></label>
                                                                <input type="text" class="form-control" name="firstName"
                                                                    id="firstName" placeholder="Enter First Name"
                                                                    onkeyup="this.value = this.value.toUpperCase();" />
                                                            </div>
                                                        </div>
                                                        <div class="col-md-6">
                                                            <div class="form-group">
                                                                <label>Middle Name</label>
                                                                <input type="text" class="form-control"
                                                                    name="middleName" id="middleName"
                                                                    placeholder="Enter Middle Name"
                                                                    onkeyup="this.value = this.value.toUpperCase();" />
                                                            </div>
                                                        </div>
                                                        <div class="col-md-6">
                                                            <div class="form-group">
                                                                <label>Last Name</label>
                                                                <input type="text" class="form-control" name="lastName"
                                                                    id="lastName" placeholder="Enter Last Name"
                                                                    onkeyup="this.value = this.value.toUpperCase();" />
                                                            </div>
                                                        </div>
                                                        <div class="col-md-6">
                                                            <div class="form-group">
                                                                <label>Gender<em>*</em></label>
                                                                <select class="form-control select2" name="gender"
                                                                    id="gender" style="width: 100%" required>
                                                                    <option value="">Select Gender</option>
                                                                    <option value="M">MALE</option>
                                                                    <option value="F">FEMALE</option>
                                                                    <option value="Other">Other</option>
                                                                </select>
                                                            </div>
                                                        </div>
                                                        <div class="col-md-6">
                                                            <div class="form-group">
                                                                <label>Date of Birth<em>*</em></label>
                                                                <input type="text" class="form-control" id="dateOfBirth"
                                                                    name="dateOfBirth" placeholder="DD/MM/YYYY">
                                                            </div>
                                                        </div>

                                                        <div class="col-md-6">
                                                            <div class="form-group">
                                                                <label>Father's Name<em>*</em></label>
                                                                <input type="text" class="form-control"
                                                                    name="fatherName" id="fatherName"
                                                                    placeholder="Enter Father's Name"
                                                                    onkeyup="this.value = this.value.toUpperCase();" />
                                                            </div>
                                                        </div>
                                                        <div class="col-md-6">
                                                            <div class="form-group">
                                                                <label>Mother's Name</label>
                                                                <input type="text" class="form-control"
                                                                    name="motherName" id="motherName"
                                                                    placeholder="Enter Mother's Name"
                                                                    onkeyup="this.value = this.value.toUpperCase();" />
                                                            </div>
                                                        </div>
                                                        <div class="col-md-6">
                                                            <div class="form-group">
                                                                <label>Spouse Name (Husband/Wife)<em>*</em></label>
                                                                <input type="text" class="form-control"
                                                                    name="spouseName" id="spouseName"
                                                                    placeholder="Enter Spouse Name"
                                                                    onkeyup="this.value = this.value.toUpperCase();" />
                                                            </div>
                                                        </div>
                                                        <div class="col-md-6">
                                                            <div class="form-group">
                                                                <label>Personal Mobile Number<em>*</em></label>
                                                                <!-- <input type="number" class="form-control" name="personalMobileNumber"
                                                    id="personalMobileNumber" placeholder="Enter Mobile Number"  minlength="10"   maxlength="10" required  /> -->
                                                                <input class="form-control" autocomplete="off"
                                                                    type="number" data-val="true"
                                                                    data-val-length="Invalid Mobile Number"
                                                                    data-val-length-max="10" data-val-length-min="10"
                                                                    data-val-regex="Entered Mobile Number format is not valid."
                                                                    data-val-regex-pattern="^\(?([6-9]{1})\)?[-. ]?([0-9]{5})[-. ]?([0-9]{4})$"
                                                                    data-val-required="Required"
                                                                    id="personalMobileNumber" maxlength="10"
                                                                    name="personalMobileNumber" value="" />
                                                                <span class="text-danger field-validation-valid"
                                                                    data-valmsg-for="personalMobileNumber"
                                                                    data-valmsg-replace="true"></span>
                                                            </div>
                                                        </div>
                                                        <div class="col-md-6">
                                                            <div class="form-group">
                                                                <label>Personal Email</label>
                                                                <input type="email" class="form-control"
                                                                    name="personalEmail" id="personalEmail"
                                                                    placeholder="Enter Email" />
                                                            </div>
                                                        </div>
                                                        <div class="col-md-6" >
                                                            <div class="form-group">
                                                                <label>PRAN No</label>
                                                                <input type="number" class="form-control" name="pranNo"
                                                                    id="pranNo" placeholder="Enter PRAN" />
                                                            </div>
                                                        </div>
                                                        <div class="col-md-6">
                                                            <div class="form-group">
                                                                <label>PAN No</label>
                                                                <input type="text" class="form-control" name="panNo"
                                                                    id="panNo" placeholder="Enter PAN Number"
                                                                    onkeyup="this.value = this.value.toUpperCase();" />
                                                            </div>
                                                        </div>
                                                        <div class="col-md-6">
                                                            <div class="form-group">
                                                                <label>Aadhar Card </label>
                                                                <!-- <input type="number" class="form-control" name="aadharCardNo"
                                                    id="aadharCardNo" placeholder="Enter Aadhar Number" required /> -->
                                                                <input maxlength="12" class="form-control"
                                                                    onkeyup="this.value = this.value.toUpperCase();"
                                                                    autocomplete="off" type="text" data-val="true"
                                                                    data-val-regex="Invalid Aadhar number."
                                                                    data-val-regex-pattern="^[0-9]{12}$"
                                                                    data-val-required="Required" id="aadharCardNo"
                                                                    name="aadharCardNo" value="" />
                                                                <span class="text-danger field-validation-valid"
                                                                    data-valmsg-for="aadharCardNo"
                                                                    data-valmsg-replace="true"></span>
                                                            </div>
                                                        </div>

                                                        <div class="col-md-6">
                                                            <div class="form-group">
                                                                <label>Employee Type<em>*</em></label>
                                                                <select class="form-control select2" name="employeeType"
                                                                    id="employeeType" style="width: 100%">
                                                                    <option value="">Select Employee Type</option>
                                                                    <option value="T">Teaching</option>
                                                                    <option value="NT">Non-Teaching</option>
                                                                </select>
                                                            </div>
                                                        </div>
                                                        <div class="col-md-6">
                                                            <div class="form-group">
                                                                <label>PPO No.<em>*</em></label>
                                                                <input type="text" class="form-control" name="ppo"
                                                                   id="ppo" placeholder="Enter PPO NO" />
                                                            </div>
                                                        </div>
                                                        <div class="col-md-6">
                                                            <div class="form-group">
                                                                <label>Payee ID.<em>*</em></label>
                                                                <input type="text" class="form-control" name="payeeId"
                                                                    id="payeeId" placeholder="Enter Payee ID NO"
                                                                    onkeyup="this.value = this.value.toUpperCase();" />
                                                            </div>
                                                        </div>
                                                  
                                                    <div class="col-md-6">
                                                        <div class="form-group">
                                                            <label>Enter PVC No.<em>*</em></label>
                                                            <input type="text" class="form-control" name="pvcNo"
                                                                id="pvcNo" placeholder="Enter PVC NO"
                                                                onkeyup="this.value = this.value.toUpperCase();" />
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>


                                            <!-- New Fields -->
                                            <div class="card card-primary">
                                                <div class="card-header">
                                                    <h3 class="card-title">Employee Official Details</h3>
                                                </div>
                                                <div class="card-body">
                                                    <div class="row">


                                                        <div class="col-md-6">
                                                            <div class="form-group">
                                                                <label>Effective date of joining</label>
                                                                <input type="text" class="form-control"
                                                                    id="effectiveDateOfJoining"
                                                                    name="effectiveDateOfJoining"
                                                                    placeholder="Enter Effective Date Of Joining"
                                                                    onkeyup="this.value = this.value.toUpperCase();">
                                                            </div>
                                                        </div>

                                                        <div class="col-md-6">
                                                            <div class="form-group">
                                                                <label>Date of Retirement<em>*</em></label>
                                                                <input type="text" class="form-control"
                                                                    name="dateOfRetirement" id="dateOfRetirement"
                                                                    placeholder="Enter Date of Retirement(DD/MM/YYYY)" />
                                                            </div>
                                                        </div>
                                                        <div class="col-md-6">
                                                            <div class="form-group">
                                                                <label>University Name at the time of retirement
                                                                    *<em>*</em></label>
                                                                <select class="form-control select2"
                                                                    name="universityName" id="universityName"
                                                                    style="width: 100%">
                                                                    <option value="">Select University Name</option>
                                                                </select>
                                                            </div>
                                                        </div>
                                                        <div class="col-md-6">
                                                            <div class="form-group">
                                                                <label>Designation At the Retirement *<em>*</em></label>
                                                                <input type="text" class="form-control"
                                                                    name="deginationAtRetirement"
                                                                    id="deginationAtRetirement"
                                                                    placeholder="Enter Designation At the Retirement" />

                                                            </div>
                                                        </div>
                                                        <div class="col-md-6">
                                                            <div class="form-group">
                                                                <label>Length Of Service *<em>*</em></label>
                                                                <input type="text" class="form-control"
                                                                    id="lengthOfService" name="lengthOfService"
                                                                    placeholder="Enter Length Of Service"
                                                                    onkeyup="this.value = this.value.toUpperCase();">
                                                            </div>
                                                        </div>
                                                        <div class="col-md-6">
                                                            <div class="form-group">
                                                                <label>Age at the time of Death *<em>*</em></label>
                                                                <input type="text" class="form-control" id="ageAtDeath"
                                                                    name="ageAtDeath"
                                                                    placeholder="Enter Age at the time of Death"
                                                                    onkeyup="this.value = this.value.toUpperCase();">
                                                            </div>
                                                        </div>
                                                        <div class="col-md-6">
                                                            <div class="form-group">
                                                                <label>Date of Death*<em>*</em></label>
                                                                <input type="text" class="form-control" id="dateOfDeath"
                                                                    name="dateOfDeath" placeholder="Enter Date Of Death"
                                                                    onkeyup="this.value = this.value.toUpperCase();">
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
     
                                              <!-- <form id="frmregister" name="frmregister" action="" method="post"> -->
                                                    <div class="card card-primary">
                                                        <!-- /.card-header -->
                                                        <div class="card-header">
                                                            <h3 class="card-title">Spouse Bank Details</h3>
                                                        </div>
                                                        <div class="card-body">
                                                            <div class="row">
                                                                    <div class="col-md-6">
                                                                        <div class="form-group">
                                                                            <label>Spouse Bank Number*<em>*</em></label>
                                                                            <input type="text" class="form-control"
                                                                                name="spouseBankNumber"
                                                                                id="spouseBankNumber"
                                                                                placeholder="SB12345678" />
                                                                        </div>
                                                                    </div>
                                                                    <div class="col-md-6">
                                                                        <div class="form-group">
                                                                            <label>Bank Name*<em>*</em></label>
                                                                            <input type="text" class="form-control"
                                                                                name="bankName" id="bankName"
                                                                                placeholder="State Bank of India" />
                                                                        </div>
                                                                    </div>
                                                                    <div class="col-md-6">
                                                                        <div class="form-group">
                                                                            <label>Branch Name*<em>*</em></label>
                                                                            <input type="text" class="form-control"
                                                                                name="branchName" id="branchName"
                                                                                placeholder="Main Branch" />
                                                                        </div>
                                                                    </div>
                                                                    <div class="col-md-6">
                                                                        <div class="form-group">
                                                                            <label>IFSC Code*<em>*</em></label>
                                                                            <input type="text" class="form-control"
                                                                                name="ifscCode" id="ifscCode"
                                                                                placeholder="SBIN0000123" />
                                                                            </div>
                                              
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                                </div>
                    
                                                                <div class="card card-primary">
                                                                    <!-- /.card-header -->
                                                                    <div class="card-header">
                                                                        <h3 class="card-title">Family Pensioner Bank Details</h3>
                                                                    </div>
                                                                    <div class="card-body">
                                                                        <div class="row">
                                                                            <div class="col-md-6">
                                                                                <div class="form-group">
                                                                                    <label>Family Pensioner Account No.*<em>*</em></label>
                                                                                    <input type="text" class="form-control" name="family_pensionar_account_no" id="family_pensionar_account_no" placeholder="Enter Family Pensioner Account No." />
                                                                                </div>
                                                                            </div>
                                                                            <div class="col-md-6">
                                                                                <div class="form-group">
                                                                                    <label>Family Pensioner Branch No.*<em>*</em></label>
                                                                                    <input type="text" class="form-control" name="family_pensionar_branch_no" id="family_pensionar_branch_no" placeholder="Enter Family Pensioner Branch Code" />
                                                                                </div>
                                                                            </div>
                                                                            <div class="col-md-6">
                                                                                <div class="form-group">
                                                                                    <label>Family Pensioner Bank Name*<em>*</em></label>
                                                                                    <input type="text" class="form-control" name="family_pensionar_bank_name" id="family_pensionar_bank_name" placeholder="Enter Bank Name" />
                                                                                </div>
                                                                            </div>
                                                                            <div class="col-md-6">
                                                                                <div class="form-group">
                                                                                    <label>Family Pensioner IFSC Code*<em>*</em></label>
                                                                                    <input type="text" class="form-control" name="family_pensionar_ifsc_code" id="family_pensionar_ifsc_code" placeholder="Enter IFSC Code" />
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                                
                                                                <div class="card card-primary">
                                                                    <!-- /.card-header -->
                                                                    <div class="card-header">
                                                                        <h3 class="card-title">Pay Entitlement Details</h3>
                                                                    </div>
                                                                    <div class="card-body">
                                                                        <div class="row">
                                                                            <div class="col-md-6">
                                                                                <div class="form-group">
                                                                                    <label>Pay Level*<em>*</em></label>
                                                                                    <input type="text" class="form-control" name="payLevel" id="payLevel" placeholder="Enter Pay Level (e.g. 12)" />
                                                                                </div>
                                                                            </div>
                                                                            <div class="col-md-6">
                                                                                <div class="form-group">
                                                                                    <label>Basic Salary*<em>*</em></label>
                                                                                    <input type="number" class="form-control" name="basicSalary" id="basicSalary" placeholder="Enter Basic Salary (e.g. 95000)" />
                                                                                </div>
                                                                            </div>
                                                                            <div class="col-md-6">
                                                                                <div class="form-group">
                                                                                    <label>Last Basic Pay*<em>*</em></label>
                                                                                    <input type="number" class="form-control" name="lastBasicPay" id="lastBasicPay" placeholder="Enter Last Basic Pay (e.g. 100000)" />
                                                                                </div>
                                                                            </div>
                                                                            <div class="col-md-6">
                                                                                <div class="form-group">
                                                                                    <label>Basic Pension*<em>*</em></label>
                                                                                    <input type="number" class="form-control" name="basicPension" id="basicPension" placeholder="Enter Basic Pension (e.g. 50000)" />
                                                                                </div>
                                                                            </div>
                                                                            <div class="col-md-6">
                                                                                <div class="form-group">
                                                                                    <label>Last Month Gross Family Pension.*<em>*</em></label>
                                                                                    <input type="text" class="form-control" name="LastMonthGrossFamilyPension" id="LastMonthGrossFamilyPension" placeholder="Enter Last Month Gross Family Pension (e.g. 48000)" />
                                                                                </div>
                                                                            </div>
                                                                            <div class="col-md-6">
                                                                                <div class="form-group">
                                                                                    <label>Last Month Withdrawn Family Pension.*<em>*</em></label>
                                                                                    <input type="text" class="form-control" name="LastMonthWithdrawnFamilyPension" id="LastMonthWithdrawnFamilyPension" placeholder="Enter Last Month Withdrawn Family Pension (e.g. 47000)" />
                                                                                </div>
                                                                            </div>
                                                                            <div class="col-md-6">
                                                                                <div class="form-group">
                                                                                    <label>Basic Family Pension.*<em>*</em></label>
                                                                                    <input type="text" class="form-control" name="BasicFamilyPension" id="BasicFamilyPension" placeholder="Enter Basic Family Pension (e.g. 30000)" />
                                                                                </div>
                                                                            </div>
                                                                            <div class="col-md-6">
                                                                                <div class="form-group">
                                                                                    <label>Dearness Allowance (DA)*<em>*</em></label>
                                                                                    <input type="number" class="form-control" name="DA" id="DA" placeholder="Enter DA (e.g. 15000)" />
                                                                                </div>
                                                                            </div>
                                                                            <div class="col-md-6">
                                                                                <div class="form-group">
                                                                                    <label>Medical Allowance*<em>*</em></label>
                                                                                    <input type="number" class="form-control" name="MedicalAllowance" id="MedicalAllowance" placeholder="Enter Medical Allowance (e.g. 2000)" />
                                                                                </div>
                                                                            </div>
                                                                            <div class="col-md-6">
                                                                                <div class="form-group">
                                                                                    <label>Gross Family Pension.*<em>*</em></label>
                                                                                    <input type="number" class="form-control" name="GrossFamilyPension" id="GrossFamilyPension" placeholder="Enter Gross Family Pension (e.g. 47000)" />
                                                                                </div>
                                                                            </div>
                                                                            <div class="col-md-6">
                                                                                <div class="form-group">
                                                                                    <label>Income Tax.*<em>*</em></label>
                                                                                    <input type="number" class="form-control" name="IncomeTax" id="IncomeTax" placeholder="Enter Income Tax (e.g. 3000)" />
                                                                                </div>
                                                                            </div>
                                                                            <div class="col-md-6">
                                                                                <div class="form-group">
                                                                                    <label>Payable Family Pension*<em>*</em></label>
                                                                                    <input type="number" class="form-control" name="PayableFamilyPension" id="PayableFamilyPension" placeholder="Enter Payable Family Pension (e.g. 44000)" />
                                                                                </div>
                                                                            </div>
                                                                            <div class="col-md-6">
                                                                                <div class="form-group">
                                                                                    <label>Initial Pension.*<em>*</em></label>
                                                                                    <input type="text" class="form-control" name="initialPension" id="initialPension" placeholder="Enter Initial Pension (e.g. 48000)" />
                                                                                </div>
                                                                            </div>
                                                                            <div class="col-md-6">
                                                                                <div class="form-group">
                                                                                    <label>Pension End Date*<em>*</em></label>
                                                                                    <input type="text" class="form-control" name="pensionEndDate" id="pensionEndDate" placeholder="Enter Pension End Date (e.g. 2030-05-31)" />
                                                                                </div>
                                                                            </div>
                                                                            <div class="col-md-6">
                                                                                <div class="form-group">
                                                                                    <label>Reduced Pension*<em>*</em></label>
                                                                                    <input type="number" class="form-control" name="reducedPension" id="reducedPension" placeholder="Enter Reduced Pension (e.g. 24000)" />
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                                
                                                            <div class="card-footer">
                                                                <button type="button" id="btnsubmit"
                                                                    class="btn btn-success float-right mr-1">
                                                                    Submit Pension
                                                                </button>
                                                            </div>
                                        </form>
                                    </div>
                                </section>
                            </div>
                            <!-- Main row -->
                        </div>
                        <!-- /.container-fluid -->
                    </section>
                    <!-- /.content -->
                </div>
                <!-- /.content-wrapper -->

                <%- include('../partials/footer'); %>
                    <!-- Control Sidebar -->
                    <aside class="control-sidebar control-sidebar-dark">
                        <!-- Control sidebar content goes here -->
                    </aside>
                    <!-- /.control-sidebar -->
    </div>
    <!-- ./wrapper -->
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- jQuery UI -->
    <script src="https://code.jquery.com/ui/1.12.1/jquery-ui.min.js"></script>

    <!-- Bootstrap 4 -->
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.bundle.min.js"></script>
    <!-- Sparkline -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-sparklines/2.1.2/jquery.sparkline.min.js"></script>
    <!-- Select2 -->
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>

    <!-- Moment.js -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.1/moment.min.js"></script>

    <!-- Date Range Picker -->
    <script src="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.min.js"></script>

    <!-- Tempusdominus Bootstrap 4 -->
    <script
        src="https://cdnjs.cloudflare.com/ajax/libs/tempusdominus-bootstrap-4/5.1.2/js/tempusdominus-bootstrap-4.min.js"></script>

    <!-- OverlayScrollbars -->
    <script
        src="https://cdnjs.cloudflare.com/ajax/libs/overlayscrollbars/1.13.0/js/jquery.overlayScrollbars.min.js"></script>

    <!-- AdminLTE App -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/admin-lte/3.2.0/js/adminlte.min.js"></script>

    <!-- jQuery Validation -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-validate/1.19.3/jquery.validate.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-validate/1.19.3/additional-methods.min.js"></script>

    <!-- Toastr -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/js/toastr.min.js"></script>

    <!-- SweetAlert2 -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>

    <script src="https://unpkg.com/boxicons@2.1.4/dist/boxicons.js"></script>
    <script>
        const apiBaseUrl = "<%= process.env.apiBaseUrl %>";
        const token = localStorage.getItem("access-token-new");
        var userDetails = localStorage.getItem('user-data');
        var userDetailsObj = JSON.parse(userDetails);
        var universityName = userDetailsObj.university
        $('#university1').append(universityName);
        $('#college1').append(universityName);
        console.log(universityName)
    </script>
    <script>
        $("#btnsearch").on("click", function (e) {
            e.preventDefault();
            const ppoNO = $("#ppoNo").val(); 

            if (!ppoNO) { // Use ppoNO here to match the declared variable
                alert('Please enter a PPO number.');
                return;
            }

            $.ajax({
                url: apiBaseUrl + "v1/stage-two/family-pension-data",
                method: 'POST',
                data: { ppoNO: ppoNO },
                success: function (response) {
                    let data = response.data[0];
                    $('#prefix').val(data.prefix);
                    $('#firstName').val(data.firstName);
                    $('#middleName').val(data.MiddleName);
                    $('#lastName').val(data.lastName);
                    $('#gender').val(data.gender).trigger('change');
                    $('#dateOfBirth').val(data.dateOfBirth);
                    $('#fatherName').val(data.fathersName);
                    $('#motherName').val(data.mothersName);
                    $('#spouseName').val(data.spouseName);
                    $('#personalMobileNumber').val(data.personalMobileNumber);
                    $('#personalEmail').val(data.email);
                    $('#pranNo').val(data.pranNo);
                    $('#ppo').val(data.ppoNO);
                    $('#panNo').val(data.panNo);
                    $('#aadharCardNo').val(data.adharCardNumber);
                    $('#payeeId').val(data.payeeId);
                    $('#pvcNo').val(data.pvcNo);
                    $('#employeeType').val(data.employeeType).trigger('change');
                    $('#lengthOfService').val(data.lengthOfService);
                    $('#effectiveDateOfJoining').val(data.effectiveDateOfJoining);
                    $('#dateOfRetirement').val(data.dateOfRetirement);
                    $('#universityName').val(data.universityName);
                    $('#deginationAtRetirement').val(data.deginationAtRetirement);
                    $('#ageAtDeath').val(data.ageAtDeath);
                    $('#dateOfDeath').val(data.dateOfDeath);
                    $('#bankAccountNo').val(data.bankAccountNo);
                    $('#bankName').val(data.bankName);
                    $('#branchName').val(data.branchName);
                    $('#ifscCode').val(data.ifscCode);
                    $('#basicPension').val(data.basicPension);
                    $('#dearnessReliefPercentage').val(data.dearnessReliefPercentage);
                    $('#medicalAllowance').val(data.medicalAllowance);
                    $('#gratuity').val(data.gratuity);
                    $('#leaveEncashment').val(data.leaveEncashment);
                    $('#arrears').val(data.arrears);
                    $('#allOneTimeBenifit').val(data.allOneTimeBenefitReceived);
                    $('#spouseBankNumber').val(data.spouseBankNumber);
                    $('#family_pensionar_account_no').val(data.family_pensionar_account_no);
                    $('#family_pensionar_branch_no').val(data.family_pensionar_branch_no);
                    $('#family_pensionar_bank_name').val(data.family_pensionar_bank_name);
                    $('#family_pensionar_ifsc_code').val(data.family_pensionar_ifsc_code);
                    $('#payLevel').val(data.payLevel);
                    $('#basicSalary').val(data.basicSalary);
                    $('#basicPension').val(data.basicPension);
                    $('#LastMonthGrossFamilyPension').val(data.LastMonthGrossFamilyPension);
                    $('#LastMonthWithdrawnFamilyPension').val(data.LastMonthWithdrawnFamilyPension);
                    $('#BasicFamilyPension').val(data.BasicFamilyPension);
                    $('#DA').val(data.DA);
                    $('#MedicalAllowance').val(data.MedicalAllowance);
                    $('#GrossFamilyPension').val(data.GrossFamilyPension);
                    $('#IncomeTax').val(data.IncomeTax);
                    $('#PayableFamilyPension').val(data.PayableFamilyPension);
                    $('#initialPension').val(data.initialPension);
                    $('#pensionEndDate').val(data.pensionEndDate);
                    $('#reducedPension').val(data.reducedPension);
                    $('#updatedBy').val(data.updatedBy);
                    $('#lastBasicPay').val(data.lastBasicPay);

               
                },
                error: function (xhr, status, error) {
                    console.error('An error occurred:', status, error);
                    alert('Failed to fetch data. Please try again.');
                }
            });
        });
    </script>

    <script>
        $(document).ready(function () {
            $.ajax({
                url: apiBaseUrl + "v1/stage-two/get-university-data",
                type: 'get',
                dataType: 'json',
                success: function (response) {

                    if (response.status === "success") {

                        var universities = response.data;
                        $('#universityName').append($('<option>', {
                            value: '',
                            text: 'Select a university'
                        }));
                        $.each(universities, function (index, item) {
                            $('#universityName').append($('<option>', {
                                value: item.university_name,
                                text: item.university_name
                            }));
                        });

                    } else {
                        console.error('API request failed with status:', response.status);
                    }
                },
                error: function (xhr, status, error) {
                    console.error('Error fetching data from API:', error);
                }
            });

        })

    </script>



    <script>
        $(document).ready(function () {
            $('#btnsubmit').on('click', function (event) {
                event.preventDefault();
                let formData = {
                    ppoNO: $('#ppo').val(),
                    payeeId: $('#payeeId').val(),
                    prefix: $('#prefix').val(),
                    firstName: $('#firstName').val(),
                    MiddleName: $('#middleName').val(),
                    lastName: $('#lastName').val(),
                    gender: $('#gender').val(),
                    dateOfBirth: $('#dateOfBirth').val(),
                    fathersName: $('#fatherName').val(),
                    mothersName: $('#motherName').val(),
                    effectiveDateOfJoining: $('#effectiveDateOfJoining').val(),
                    deginationAtRetirement: $('#deginationAtRetirement').val(),
                    universityName: $('#universityName').val(),
                    pranNo: $('#pranNo').val(),
                    personalMobileNumber: $('#personalMobileNumber').val(),
                    email: $('#personalEmail').val(),
                    panNo: $('#panNo').val(),
                    adharCardNumber: $('#aadharCardNo').val(),
                    spouseName: $('#spouseName').val(),
                    dateOfRetirement: $('#dateOfRetirement').val(),
                    lengthOfService: $('#lengthOfService').val(),
                    dateOfDeath: $('#dateOfDeath').val(),
                    ageAtDeath: $('#ageAtDeath').val(),
                    payLevel: $('#payLevel').val(),
                    basicSalary: $('#basicSalary').val(),
                    lastBasicPay: $('#lastBasicPay').val(),
                    basicPension: $('#basicPension').val(),
                    pvcNo: $('#pvcNo').val(),
                    employeeType: $('#employeeType').val(),
                    spouseBankNumber: $('#spouseBankNumber').val(),
                    bankName: $('#bankName').val(),
                    branchName: $('#branchName').val(),
                    ifscCode: $('#ifscCode').val(),
                    family_pensionar_account_no: $('#family_pensionar_account_no').val(),
                    family_pensionar_branch_no: $('#family_pensionar_branch_no').val(),
                    family_pensionar_bank_name: $('#family_pensionar_bank_name').val(),
                    family_pensionar_ifsc_code: $('#family_pensionar_ifsc_code').val(),
                    LastMonthGrossFamilyPension: $('#LastMonthGrossFamilyPension').val(),
                    LastMonthWithdrawnFamilyPension: $('#LastMonthWithdrawnFamilyPension').val(),
                    BasicFamilyPension: $('#BasicFamilyPension').val(),
                    DA: $('#DA').val(),
                    MedicalAllowance: $('#MedicalAllowance').val(),
                    GrossFamilyPension: $('#GrossFamilyPension').val(),
                    IncomeTax: $('#IncomeTax').val(),
                    PayableFamilyPension: $('#PayableFamilyPension').val(),
                    initialPension: $('#initialPension').val(),
                    pensionEndDate: $('#pensionEndDate').val(),
                    reducedPension: $('#reducedPension').val(),
                    updatedBy: universityName
                };

                $.ajax({
                    url: apiBaseUrl + "v1/stage-two/update-family-pension",
                    type: 'POST',
                    contentType: 'application/json',
                    data: JSON.stringify(formData),  headers: {
                    token: localStorage.getItem("access-token-new"),
                    "Content-Type": "application/json"
                },
                    success: function (response) {
                        Swal.fire({
                            icon: 'success',
                            title: 'Family-Pensioner Details Edited/Updated Successfully',
                            text: 'Family Pensioner details  Updated successfully!',
                        });
                        setTimeout(() => {
                               location.reload();
                            }, 3000);
                              },
                    error: function (xhr, status, error) {
                        let errorMessage = 'An error occurred while submitting the form. Please try again.';
                        if (xhr.responseJSON && xhr.responseJSON.message) {
                            errorMessage = xhr.responseJSON.message;
                        }
                        Swal.fire({
                            icon: 'error',
                            title: 'Error',
                            text: errorMessage,
                        });
                    }

                });
            });
        });

    </script>
 <script>
    function isTokenExpired(token) {
        if (!token) return true;
        try {
            // Decode JWT payload
            const payloadBase64 = token.split('.')[1];
            const payload = JSON.parse(atob(payloadBase64));
            // Check if current time is past the expiration time
            const isExpired = payload.exp * 1000 < Date.now();
            return isExpired;
        } catch (error) {
            console.error("Failed to decode token:", error);
            return true; 
        }
    }
    // Check token expiration on load
    $(function () {
       const token = localStorage.getItem("access-token-new");
        const userData = JSON.parse(userDetails);
        if (token && userData) {
            // Check role and token expiration
            if (usersData.role !== "University-Admin" || isTokenExpired(token)) {
                alert("Session expired or unauthorized access. Redirecting to login.");
                window.location = "/";
            } else {
                setInterval(() => {
                    if (isTokenExpired(token)) {
                        alert("Session expired. Please login again.");
                        window.location = "/";
                    }
                }, 60000);
            }
        } else {
            window.location = "/";
        }
    });
                    </script> 