<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8" />
        <meta http-equiv="X-UA-Compatible" content="IE=edge" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title><%= title %> </title>
        <!-- Google Font: Source Sans Pro -->
        <!-- Google Font: Source Sans Pro -->
        <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,400i,700&display=fallback" />
        <!-- Font Awesome -->
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css" />
        <!-- Ionicons -->
        <link rel="stylesheet" href="https://code.ionicframework.com/ionicons/2.0.1/css/ionicons.min.css" />
        <!-- Tempusdominus Bootstrap 4 -->
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/tempusdominus-bootstrap-4/5.39.0/css/tempusdominus-bootstrap-4.min.css" />
        <!-- iCheck -->
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/icheck-bootstrap/3.0.1/icheck-bootstrap.min.css" />
        <!-- Select2 -->
        <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
        <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/select2@4.0.13/dist/css/select2.min.css" />
        <!-- Theme style -->
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/admin-lte/3.1.0/css/adminlte.min.css" />
        <!-- overlayScrollbars -->
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/overlayscrollbars/1.13.0/css/OverlayScrollbars.min.css" />
        <!-- Daterange picker -->
        <link rel="stylesheet" href="https://cdn.datatables.net/1.10.24/css/dataTables.bootstrap4.min.css">
        <!-- DataTables -->
        <link rel="stylesheet" href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap4.min.css">
        <link rel="stylesheet" href="https://cdn.datatables.net/responsive/2.2.9/css/responsive.bootstrap4.min.css">
        <link rel="stylesheet" href="https://cdn.datatables.net/buttons/2.0.1/css/buttons.bootstrap4.min.css">
        <!-- Baoxicon -->
        <link href='https://unpkg.com/boxicons@2.1.4/css/boxicons.min.css' rel='stylesheet'>
        <style type="text/css">
            .seat {
                width: 50px;
            }

            input::-webkit-outer-spin-button,
            input::-webkit-inner-spin-button {
                -webkit-appearance: none;
                margin: 0;
            }

            .card-title {
                font-weight: bold !important;
            }

            em {
                color: red;
            }

            .card-primary1 {
                background-color: green;
            }
        </style>
    </head>
    <body class="hold-transition sidebar-mini layout-fixed sidebar-collapse">
        <div class="wrapper">
            <!-- Preloader -->
            <div class="preloader flex-column justify-content-center align-items-center">
                <img class="animation__shake" src="/images/loader.gif" alt="CodeBucket" height="100" width="100" />
            </div><%- include('../partials/header'); %><%- include('../partials/sidebar'); %>
            <!-- Content Wrapper. Contains page content -->
            <div class="content-wrapper">
                <!-- Content Header (Page header) -->
                <div class="content-header">
                    <div class="container-fluid">
                        <div class="row mb-2">
                            <div class="col-sm-6">
                                <h1 class="m-0" style="color: red; font-weight: bolder;"> Delete Guest-teacher Data:</h1>
                            </div>
                            <!-- /.col -->
                            <div class="col-sm-6">
                                <ol class="breadcrumb float-sm-right">
                                    <li class="breadcrumb-item">
                                        <a href="/">Home</a>
                                    </li>
                                    <li class="breadcrumb-item active">Remove Guest-teacher Data</li>
                                </ol>
                            </div>
                            <!-- /.col -->
                        </div>
                        <!-- /.row -->
                    </div>
                    <!-- /.container-fluid -->
                </div>
                <section class="content">
                    <div class="container-fluid">
                        <div class="row">
                            <section class="col-12">
                                <form id="frmsearch" name="frmsearch" action="" method="post">
                                    <div class="card card-warning">
                                        <!-- /.card-header -->
                                        <div class="card-header">
                                            <h3 class="card-title">Search Guest-teacher and Remove Data</h3>
                                        </div>
                                        <div class="card-body">
                                            <div class="form-group d-flex align-items-center">
                                                <label for="id" class="mr-2">Payee ID</label>
                                                <input type="text" class="form-control mr-2" name="id" id="id" onkeyup="this.value = this.value.toUpperCase();" placeholder="Enter Payee ID For Search" style="flex: 1">
                                                <button type="button" id="btnsearch" class="btn btn-danger float-right mr-1">Search By PayID</button>
                                            </div>
                                        </div>
                                </form>
                                <div id="pensionDetails" style="display: none;">
                                    <form id="frmregister" name="frmregister" action="" method="post">
                                        <div class="card card-primary 1">
                                            <div class="card-header">
                                                <h3 class="card-title">Verify  Guest-teacher Details And Process For Remove Data</h3>
                                            </div>
                                            <div class="card-body">
                                                <div class="row">
                                                    <div class="col-md-6">
                                                        <div class="form-group">
                                                            <label>First Name <em>*</em>
                                                            </label>
                                                            <input type="text" class="form-control" name="firstName" id="firstName" placeholder="Enter First Name" onkeyup="this.value = this.value.toUpperCase();" />
                                                        </div>
                                                    </div>
                                                    <div class="col-md-6">
                                                        <div class="form-group">
                                                            <label>Last Name</label>
                                                            <input type="text" class="form-control" name="lastName" id="lastName" placeholder="Enter Last Name" onkeyup="this.value = this.value.toUpperCase();" />
                                                        </div>
                                                    </div>
                                                    <div class="col-md-6">
                                                        <div class="form-group">
                                                            <label>Employee Type <em>*</em>
                                                            </label>
                                                            <input type="text" class="form-control" name="employeeType" id="employeeType" style="width: 100%">
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="card-footer">
                                            <!-- <button type="button" id="btnsubmitedit"
                                    class="btn btn-danger float-right mr-1">
                                 Edit Details 
                                </button> -->
                                            <button type="button" id="btnsubmit" class="btn btn-danger float-right mr-1">Delete Data</button>
                                        </div>
                                    </form>
                                </div>
                            </section>
                        </div>
                        <!-- Main row -->
                    </div>
                    <!-- /.container-fluid -->
                </section>
                <!-- /.content -->
            </div>
            <!-- /.content-wrapper --><%- include('../partials/footer'); %>
            <!-- Control Sidebar -->
            <aside class="control-sidebar control-sidebar-dark">
                <!-- Control sidebar content goes here -->
            </aside>
            <!-- /.control-sidebar -->
        </div>
        <!-- jQuery -->
        <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
        <!-- jQuery UI -->
        <script src="https://code.jquery.com/ui/1.12.1/jquery-ui.min.js"></script>
        <!-- Bootstrap 4 -->
        <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.bundle.min.js"></script>
        <!-- Sparkline -->
        <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-sparklines/2.1.2/jquery.sparkline.min.js"></script>
        <!-- Select2 -->
        <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
        <!-- Moment.js -->
        <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.1/moment.min.js"></script>
        <!-- Date Range Picker -->
        <script src="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.min.js"></script>
        <!-- Tempusdominus Bootstrap 4 -->
        <script src="https://cdnjs.cloudflare.com/ajax/libs/tempusdominus-bootstrap-4/5.1.2/js/tempusdominus-bootstrap-4.min.js"></script>
        <!-- OverlayScrollbars -->
        <script src="https://cdnjs.cloudflare.com/ajax/libs/overlayscrollbars/1.13.0/js/jquery.overlayScrollbars.min.js"></script>
        <!-- AdminLTE App -->
        <script src="https://cdnjs.cloudflare.com/ajax/libs/admin-lte/3.2.0/js/adminlte.min.js"></script>
        <!-- jQuery Validation -->
        <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-validate/1.19.3/jquery.validate.min.js"></script>
        <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-validate/1.19.3/additional-methods.min.js"></script>
        <!-- Toastr -->
        <script src="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/js/toastr.min.js"></script>
        <!-- SweetAlert2 -->
        <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
        <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
        <script src="https://unpkg.com/boxicons@2.1.4/dist/boxicons.js"></script>
        <script>
            const apiBaseUrl = "<%= process.env.apiBaseUrl %>";
            const token = localStorage.getItem("access-token-new");
            var userDetails = localStorage.getItem('user-data');
            var userDetailsObj = JSON.parse(userDetails);
            var universityName = userDetailsObj.university
            $('#university1').append(universityName);
            $('#college1').append(universityName);
            console.log(universityName)
        </script>
        <script>
            $("#btnsearch").on("click", function(e) {
                e.preventDefault();
                var payeeId = $('#id').val();
                 if (!payeeId) {
                   Swal.fire('Error', 'Please enter a Payee ID!', 'error');
                     return;
                       }
                $.ajax({
                    url: apiBaseUrl + "v1/stage-two/inactive-guest-teacher-data",
                    method: 'POST',
                    data: {
                        payIdNo: payeeId
                    },
                    success: function(response) {
                        let data = response.data.data[0];
                        $('#prefix').val(data.prefix).trigger('change');
                        $('#firstName').val(data.firstName);
                        $('#lastName').val(data.lastName);
                        $('#employeeType').val(data.employeeType).trigger('change');
                        $("#pensionDetails").show();
                    },
                    error: function(xhr, status, error) {
                        console.error('An error occurred:', status, error);
                        try {
                            const response = JSON.parse(xhr.responseText);
                            if (response.message) {
                                alert(`Error: ${response.message.message}`);
                            } else {
                                alert('An unexpected error occurred. Please try again.');
                            }
                        } catch (e) {
                            alert('Failed to fetch data. Please try again.');
                        }
                    }
                });
            });
        </script>
        <script>
    $(document).ready(function() {
    $('#btnsubmit').click(function() {
        var payeeId = $('#id').val();
        if (!payeeId) {
            Swal.fire('Error', 'Please enter a Payee ID!', 'error');
            return;
        }
        Swal.fire({
            title: 'Are you sure you want to delete data?',
            text: 'You won\'t be able to revert this!',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#3085d6',
            cancelButtonColor: '#d33',
            confirmButtonText: 'Yes, delete it!'
        }).then((result) => {
            if (result.isConfirmed) {
                $.ajax({
                    url: apiBaseUrl + "v1/stage-two/inactive-guest-teacher-data",
                    type: 'POST',
                    headers: {
                          token: localStorage.getItem("access-token-new"),
                         "Content-Type": "application/json"
                             },
                    data: JSON.stringify({
                        payIdNo: payeeId,
                        status: 'INACTIVE'
                    }),
                    success: function(response) {
                        Swal.fire('Deleted!', 'Your data has been deleted.', 'success');
                    },
                    error: function() {
                        Swal.fire('Error!', 'There was a problem deleting your data.', 'error');
                    }
                });
            }
        });
    })
})
    
    </script>
        <script>
            function isTokenExpired(token) {
                if (!token) return true;
                try {
                    // Decode JWT payload
                    const payloadBase64 = token.split('.')[1];
                    const payload = JSON.parse(atob(payloadBase64));
                    // Check if current time is past the expiration time
                    const isExpired = payload.exp * 1000 < Date.now();
                    return isExpired;
                } catch (error) {
                    console.error("Failed to decode token:", error);
                    return true;
                }
            }
            // Check token expiration on load
            $(function() {
                const token = localStorage.getItem("access-token-new");
                const userData = JSON.parse(userDetails);
                if (token && userData) {
                    // Check role and token expiration
                    if (usersData.role !== "University-Admin" || isTokenExpired(token)) {
                        alert("Session expired or unauthorized access. Redirecting to login.");
                        window.location = "/";
                    } else {
                        setInterval(() => {
                            if (isTokenExpired(token)) {
                                alert("Session expired. Please login again.");
                                window.location = "/";
                            }
                        }, 60000);
                    }
                } else {
                    window.location = "/";
                }
            });
        </script>