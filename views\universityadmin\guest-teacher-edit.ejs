<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>
        <%= title %>
    </title>

   
    <link rel="stylesheet"
        href="https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,400i,700&display=fallback" />

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css" />

    <!-- Ionicons -->
    <link rel="stylesheet" href="https://code.ionicframework.com/ionicons/2.0.1/css/ionicons.min.css" />


    <!-- Tempusdominus Bootstrap 4 -->
    <link rel="stylesheet"
        href="https://cdnjs.cloudflare.com/ajax/libs/tempusdominus-bootstrap-4/5.39.0/css/tempusdominus-bootstrap-4.min.css" />

    <!-- iCheck -->
    <link rel="stylesheet"
        href="https://cdnjs.cloudflare.com/ajax/libs/icheck-bootstrap/3.0.1/icheck-bootstrap.min.css" />

    <!-- Select2 -->
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/select2@4.0.13/dist/css/select2.min.css" />

    <!-- Theme style -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/admin-lte/3.1.0/css/adminlte.min.css" />

    <!-- overlayScrollbars -->
    <link rel="stylesheet"
        href="https://cdnjs.cloudflare.com/ajax/libs/overlayscrollbars/1.13.0/css/OverlayScrollbars.min.css" />

    <!-- Daterange picker -->
    <link rel="stylesheet" href="https://cdn.datatables.net/1.10.24/css/dataTables.bootstrap4.min.css">


    <!-- DataTables -->
    <link rel="stylesheet" href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap4.min.css">

    <link rel="stylesheet" href="https://cdn.datatables.net/responsive/2.2.9/css/responsive.bootstrap4.min.css">

    <link rel="stylesheet" href="https://cdn.datatables.net/buttons/2.0.1/css/buttons.bootstrap4.min.css">
    <!-- Baoxicon -->
    <link href='https://unpkg.com/boxicons@2.1.4/css/boxicons.min.css' rel='stylesheet'>
    <style type="text/css">
        .seat {
            width: 50px;
        }

        input::-webkit-outer-spin-button,
        input::-webkit-inner-spin-button {
            -webkit-appearance: none;
            margin: 0;
        }

        .card-title {
            font-weight: bold !important;
        }

        em {
            color: red;
        }

        .card-primary1 {
            background-color: green;
        }
    </style>
</head>

<body class="hold-transition sidebar-mini layout-fixed sidebar-collapse">
    <div class="wrapper">
        <!-- Preloader -->
        <div class="preloader flex-column justify-content-center align-items-center">
            <img class="animation__shake" src="/images/loader.gif" alt="CodeBucket" height="100" width="100" />
        </div>
        <%- include('../partials/header'); %> <%- include('../partials/sidebar'); %>


                <!-- Content Wrapper. Contains page content -->
                <div class="content-wrapper">
                    <!-- Content Header (Page header) -->
                    <div class="content-header">
                        <div class="container-fluid">
                            <div class="row mb-2">
                                <div class="col-sm-6">
                                    <h1 class="m-0" style="color: green; font-weight: bolder;">Edit  Guest Teacher Details:
                                    </h1>
                                </div>
                                <!-- /.col -->
                                <div class="col-sm-6">
                                    <ol class="breadcrumb float-sm-right">
                                        <li class="breadcrumb-item"><a href="/">Home</a></li>
                                        <li class="breadcrumb-item active">Guest Teacher Filling Details
                                            individual</li>
                                    </ol>
                                </div>
                                <!-- /.col -->
                            </div>
                            <!-- /.row -->
                        </div>
                        <!-- /.container-fluid -->
                    </div>

                    <section class="content">
                        <div class="container-fluid">
                            <div class="row">
                                <section class="col-12">
                                    <form id="frmsearch" name="frmsearch" action="" method="post">
                                        <div class="card card-primary">
                                            <!-- /.card-header -->
                                            <div class="card-header">
                                                <h3 class="card-title">Search Guset Teacher Deatils</h3>
                                            </div>
                                            <div class="card-body">
                                                <div class="form-group d-flex align-items-center">
                                                    <label for="payeeid" class="mr-2" style="color: red;">Payee
                                                        ID</label>
                                                    <input type="text" class="form-control mr-2" name="ppoNo" id="ppoNo"
                                                        placeholder="Enter payeeId to Fetch Deatils" style="flex: 1">
                                                </div>
                                            </div>
                                            <div class="card-footer">
                                                <button type="submit" id="btnsearch"
                                                    class="btn btn-info float-right mr-1">
                                                    Search Deatils
                                                </button>
                                            </div>
                                        </div>
                                    </form>
                                    <div id="pensionDetails" >
                                        <form id="frmregister" name="frmregister" action="" method="post">
                                            <div class="card card-primary 1">
                                                <div class="card-header">
                                                    <h3 class="card-title">Guset Teacher Personal Details</h3>
                                                </div>
                                                <div class="card-body">
                                                    <div class="row">
                                                        <div class="col-md-6">
                                                            <div class="form-group">
                                                                <label>Prefix</label>
                                                                <select class="form-control select2" name="prefix"
                                                                    id="prefix" style="width: 100%">
                                                                    <option value="Mr.">Mr.</option>
                                                                    <option value="Ms.">Ms.</option>
                                                                    <option value="Mrs.">Mrs.</option>
                                                                    <option value="Dr.">Dr.</option>
                                                                    <option value="Prof.">Prof.</option>
                                                                    <option value="Shri">Shri</option>
                                                                </select>
                                                            </div>
                                                        </div>
                                                        <div class="col-md-6">
                                                            <div class="form-group">
                                                                <label>First Name<em>*</em></label>
                                                                <input type="text" class="form-control" name="firstName"
                                                                    id="firstName" placeholder="Enter First Name"
                                                                    onkeyup="this.value = this.value.toUpperCase();" />
                                                            </div>
                                                        </div>
                                                        <div class="col-md-6">
                                                            <div class="form-group">
                                                                <label>Middle Name</label>
                                                                <input type="text" class="form-control"
                                                                    name="middleName" id="middleName"
                                                                    placeholder="Enter Middle Name"
                                                                    onkeyup="this.value = this.value.toUpperCase();" />
                                                            </div>
                                                        </div>
                                                        <div class="col-md-6">
                                                            <div class="form-group">
                                                                <label>Last Name</label>
                                                                <input type="text" class="form-control" name="lastName"
                                                                    id="lastName" placeholder="Enter Last Name"
                                                                    onkeyup="this.value = this.value.toUpperCase();" />
                                                            </div>
                                                        </div>
                                                        <div class="col-md-6">
                                                            <div class="form-group">
                                                                <label>Gender<em>*</em></label>
                                                                <input type="text" class="form-control" name="gender"
                                                                    id="gender" placeholder="Enter Gender" />
                                                            </div>
                                                        </div>
                                                        <div class="col-md-6">
                                                            <div class="form-group">
                                                                <label>Date of Birth<em>*</em></label>
                                                                <input type="text" class="form-control"
                                                                    name="dateOfBirth" id="dateOfBirth"
                                                                    placeholder="Enter Date of Birth(DD/MM/YYYY)" />
                                                            </div>
                                                        </div>

                                                        <div class="col-md-6">
                                                            <div class="form-group">
                                                                <label>Father's Name<em>*</em></label>
                                                                <input type="text" class="form-control"
                                                                    name="fathersName" id="fathersName"
                                                                    placeholder="Enter Father's Name"
                                                                    onkeyup="this.value = this.value.toUpperCase();" />
                                                            </div>
                                                        </div>
                                                        <div class="col-md-6">
                                                            <div class="form-group">
                                                                <label>Mother's Name</label>
                                                                <input type="text" class="form-control"
                                                                    name="mothersName" id="mothersName"
                                                                    placeholder="Enter Mother's Name"
                                                                    onkeyup="this.value = this.value.toUpperCase();" />
                                                            </div>
                                                        </div>

                                                        <div class="col-md-6">
                                                            <div class="form-group">
                                                                <label>Spouse Name</label>
                                                                <input type="text" class="form-control"
                                                                    name="spouseName" id="spouseName"
                                                                    placeholder="Enter Spouse Name"
                                                                    onkeyup="this.value = this.value.toUpperCase();" />
                                                            </div>
                                                        </div>
                                                        <div class="col-md-6">
                                                            <div class="form-group">
                                                                <label>Email ID</label>
                                                                <input type="email" class="form-control" name="emailId"
                                                                    id="emailId" placeholder="Enter Email ID" />
                                                            </div>
                                                        </div>
                                                        <div class="col-md-6">
                                                            <div class="form-group">
                                                                <label>Mobile Number<em>*</em></label>
                                                                <input type="number" class="form-control"
                                                                    name="mobileNo" id="mobileNo"
                                                                    placeholder="Enter Mobile Number" />
                                                            </div>
                                                        </div>
                                                        <div class="col-md-6">
                                                            <div class="form-group">
                                                                <label>PRAN Number</label>
                                                                <input type="text" class="form-control" name="pranNo"
                                                                    id="pranNo" placeholder="Enter PRAN Number" />
                                                            </div>
                                                        </div>
                                                        <div class="col-md-6">
                                                            <div class="form-group">
                                                                <label>PAN Number<em>*</em></label>
                                                                <input type="text" class="form-control" name="panNo"
                                                                    id="panNo" placeholder="Enter PAN Number"
                                                                    onkeyup="this.value = this.value.toUpperCase();" />
                                                            </div>
                                                        </div>
                                                        <div class="col-md-6">
                                                            <div class="form-group">
                                                                <label>Aadhaar Card Number</label>
                                                                <input type="text" class="form-control"
                                                                    name="adharCardNumber" id="adharCardNumber"
                                                                    placeholder="Enter Aadhaar Card Number" />
                                                            </div>
                                                        </div>
                                                        <div class="col-md-6">
                                                            <div class="form-group">
                                                                <label>Pay ID Number<em>*</em></label>
                                                                <input type="text" class="form-control"
                                                                    name="payIdNumber" id="payIdNumber"
                                                                    placeholder="Enter Pay ID Number" />
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>


                                                <!-- New Fields -->
                                                <div class="card card-primary">
                                                    <div class="card-header">
                                                        <h3 class="card-title">Employee Official Details</h3>
                                                    </div>
                                                    <div class="card-body">
                                                        <div class="row">

                                                            <div class="col-md-6">
                                                                <div class="form-group">
                                                                    <label>Employee Type<em>*</em></label>
                                                                    <select class="form-control select2"
                                                                        name="employeeType" id="employeeType"
                                                                     >
                                                                        <option value="">Select Employee Type</option>
                                                                        <option value="T">Teaching</option>
                                                                        <option value="NT">Non-Teaching</option>
                                                                    </select>
                                                                </div>
                                                            </div>

                                                            <div class="col-md-6">
                                                                <div class="form-group">
                                                                    <label>University Name at Joining<em>*</em></label>
                                                                    <select class="form-control select2"
                                                                        name="universityNameAtJoining"
                                                                        id="universityNameAtJoining"
                                                                        placeholder="Enter University Name at Joining">
                                                                        <option value="">Select University Name at
                                                                            Joining</option>
                                                                    </select>
                                                                </div>
                                                            </div>
                                                            <div class="col-md-6">
                                                                <div class="form-group">
                                                                    <label>College at the Time of
                                                                        Joining<em>*</em></label>
                                                                    <select class="form-control select2"
                                                                        name="collegeatthetimeofJoining"
                                                                        id="collegeatthetimeofJoining">
                                                                        <option value="">Select College at the Time of
                                                                            Joining</option>
                                                                    </select>
                                                                </div>
                                                            </div>
                                                            <div class="col-md-6">
                                                                <div class="form-group">
                                                                    <label>College Currently Deputed<em>*</em></label>
                                                                    <select class="form-control select2"
                                                                        name="collegeCurrentlyDeputed"
                                                                        id="collegeCurrentlyDeputed">
                                                                        <option value="">Select College Currently
                                                                            Deputed</option>
                                                                    </select>
                                                                </div>
                                                            </div>

                                                            <div class="col-md-6">
                                                                <div class="form-group">
                                                                    <label>Appointment Letter Date</label>
                                                                    <input type="text" class="form-control"
                                                                        name="appointedLetterDate"
                                                                        id="appointedLetterDate"
                                                                        placeholder="Enter Appointment Letter Date" />
                                                                </div>
                                                            </div>
                                                            <div class="col-md-6">
                                                                <div class="form-group">
                                                                    <label>Effective Date of Joining<em>*</em></label>
                                                                    <input type="text" class="form-control"
                                                                        name="effectiveDateOfJoining"
                                                                        id="effectiveDateOfJoining"
                                                                        placeholder="Enter Effective Date of Joining" />
                                                                </div>
                                                            </div>
                                                            <div class="col-md-6">
                                                                <div class="form-group">
                                                                    <label>Appointment Letter Order Number</label>
                                                                    <input type="text" class="form-control"
                                                                        name="appointedLetterOrderNumber"
                                                                        id="appointedLetterOrderNumber"
                                                                        placeholder="Enter Appointment Letter Order Number" />
                                                                </div>
                                                            </div>
                                                            <div class="col-md-6">
                                                                <div class="form-group">
                                                                    <label>Is Appointed Against Sanctioned
                                                                        Post<em>*</em></label>
                                                                    <input type="text" class="form-control"
                                                                        name="isAppointedAgainstSanctionedPost"
                                                                        id="isAppointedAgainstSanctionedPost"
                                                                        placeholder="Enter if Appointed Against Sanctioned Post"
                                                                        onkeyup="this.value = this.value.toUpperCase();" />
                                                                </div>
                                                            </div>
                                                            <div class="col-md-6">
                                                                <div class="form-group">
                                                                    <label>Fresh or Renewal
                                                                        Appointment<em>*</em></label>
                                                                    <input type="text" class="form-control"
                                                                        name="freshorrenewalAppointment"
                                                                        id="freshorrenewalAppointment"
                                                                        placeholder="Enter Fresh or Renewal Appointment"
                                                                        onkeyup="this.value = this.value.toUpperCase();" />
                                                                </div>
                                                            </div>
                                                            <div class="col-md-6">
                                                                <div class="form-group">
                                                                    <label>Subject for the Appointment
                                                                        Made<em>*</em></label>
                                                                    <select class="form-control select2"
                                                                        name="subjectForTheAppointmentMade"
                                                                        id="subjectForTheAppointmentMade"
                                                                        placeholder="Enter Subject for the Appointment Made">
                                                                        <option value="">Select Subject for the
                                                                            Appointment Made</option>
                                                                    </select>
                                                                </div>
                                                            </div>
                                                        </div>

                                                    </div>
                                                    <section class="col-12">
                                                        <!-- <form id="frmregister" name="frmregister" action="" method="post"> -->
                                                        <div class="card card-primary">
                                                            <!-- /.card-header -->
                                                            <div class="card-header">
                                                                <h3 class="card-title">General Information</h3>
                                                            </div>
                                                            <div class="card-body">
                                                                <div class="row">
                                                                    <div class="col-md-6">
                                                                        <div class="form-group">
                                                                            <label>UG Degree Subject
                                                                                Code<em>*</em></label>
                                                                            <select class="form-control select2"
                                                                                name="ugdegreeSubjectCode"
                                                                                id="ugdegreeSubjectCode"
                                                                                placeholder="Enter UG Degree Subject Code">
                                                                                <option value="">Select UG Degree
                                                                                    Subject Code</option>
                                                                            </select>
                                                                        </div>
                                                                    </div>
                                                                    <div class="col-md-6">
                                                                        <div class="form-group">
                                                                            <label>UG Degree Subject Passing
                                                                                Year<em>*</em></label>
                                                                            <input type="text" class="form-control"
                                                                                name="ugdegreeSubjectPassingYear"
                                                                                id="ugdegreeSubjectPassingYear"
                                                                                placeholder="Enter UG Degree Subject Passing Year" />
                                                                        </div>
                                                                    </div>
                                                                    <div class="col-md-6">
                                                                        <div class="form-group">
                                                                            <label>PG Degree Subject
                                                                                Code<em>*</em></label>
                                                                            <select class="form-control select2"
                                                                                name="pgdegreeSubjectCode"
                                                                                id="pgdegreeSubjectCode"
                                                                                placeholder="Enter PG Degree Subject Code">
                                                                                <option value="">Select PG Degree
                                                                                    Subject Code</option>
                                                                            </select>

                                                                        </div>
                                                                    </div>
                                                                    <div class="col-md-6">
                                                                        <div class="form-group">
                                                                            <label>PG Degree Subject Passing
                                                                                Year<em>*</em></label>
                                                                            <input type="text" class="form-control"
                                                                                name="pgdegreeSubjectPassingYear"
                                                                                id="pgdegreeSubjectPassingYear"
                                                                                placeholder="Enter PG Degree Subject Passing Year" />
                                                                        </div>
                                                                    </div>
                                                                    <div class="col-md-6">
                                                                        <div class="form-group">
                                                                            <label>PhD Degree Subject
                                                                                Code<em>*</em></label>
                                                                            <select class="form-control select2"
                                                                                name="phddegreeSubjectCode"
                                                                                id="phddegreeSubjectCode"
                                                                                placeholder="Enter PhD Degree Subject Code">
                                                                                <option value="">Select PhD Degree
                                                                                    Subject Code</option>
                                                                            </select>
                                                                        </div>
                                                                    </div>
                                                                    <div class="col-md-6">
                                                                        <div class="form-group">
                                                                            <label>PhD Degree Subject Passing
                                                                                Year<em>*</em></label>
                                                                            <input type="text" class="form-control"
                                                                                name="phddegreeSubjectPassingYear"
                                                                                id="phddegreeSubjectPassingYear"
                                                                                placeholder="Enter PhD Degree Subject Passing Year" />
                                                                        </div>
                                                                    </div>
                                                                    <div class="col-md-6">
                                                                        <div class="form-group">
                                                                            <label>UGC NET Qualifying Stage(SUBJECT
                                                                                CODE)<em>*</em></label>
                                                                            <select class="form-control select2"
                                                                                name="ugcNetQualifyingStage"
                                                                                id="ugcNetQualifyingStage"
                                                                                placeholder="Enter UGC NET Qualifying Stage">
                                                                                <option value=""> Select UGC NET
                                                                                    Qualifying Stage </option>
                                                                            </select>
                                                                        </div>
                                                                    </div>
                                                                    <div class="col-md-6">
                                                                        <div class="form-group">
                                                                            <label>UGC NET Qualifying
                                                                                Year<em>*</em></label>
                                                                            <input type="text" class="form-control"
                                                                                name="ugcNetQualifyingYear"
                                                                                id="ugcNetQualifyingYear"
                                                                                placeholder="Enter UGC NET Qualifying Year" />
                                                                        </div>
                                                                    </div>
                                                                    <div class="col-md-6">
                                                                        <div class="form-group">
                                                                            <label>Total Number of Students in All Years
                                                                                in the Subject at UG Level</label>
                                                                            <input type="text" class="form-control"
                                                                                name="totalnumberofstudentsinallyearsinthesubjectatUGlevel"
                                                                                id="totalnumberofstudentsinallyearsinthesubjectatUGlevel"
                                                                                placeholder="Enter Total Number of Students in All Years in the Subject at UG Level" />
                                                                        </div>
                                                                    </div>
                                                                    <div class="col-md-6">
                                                                        <div class="form-group">
                                                                            <label>Total Number of Students in All Years
                                                                                in the Subject at PG Level</label>
                                                                            <input type="text" class="form-control"
                                                                                name="totalnumberofstudentsinallyearsinthesubjectatPGlevel"
                                                                                id="totalnumberofstudentsinallyearsinthesubjectatPGlevel"
                                                                                placeholder="Enter Total Number of Students in All Years in the Subject at PG Level" />
                                                                        </div>
                                                                    </div>
                                                                    <div class="col-md-6">
                                                                        <div class="form-group">
                                                                            <label>Total Number of Permanent Teachers in
                                                                                All<em>*</em></label>
                                                                            <input type="text" class="form-control"
                                                                                name="TotalnumberofPermanentAll"
                                                                                id="TotalnumberofPermanentAll"
                                                                                placeholder="Enter Total Number of Permanent Teachers in All" />
                                                                        </div>
                                                                    </div>

                                                                    <div class="col-md-6">
                                                                        <div class="form-group">
                                                                            <label>Total Number of Guest Teachers in the
                                                                                Subject</label>
                                                                            <input type="text" class="form-control"
                                                                                name="Totalnumberofguestteachersinthesubject"
                                                                                id="Totalnumberofguestteachersinthesubject"
                                                                                placeholder="Enter Total Number of Guest Teachers in the Subject" />
                                                                        </div>
                                                                    </div>

                                                                    <div class="col-md-6">
                                                                        <div class="form-group">
                                                                            <label>Total Number of Lectures Taken by One
                                                                                Guest Teacher<em>*</em></label>
                                                                            <input type="text" class="form-control"
                                                                                name="totalnumberoflecturestakenbyoneguestteacher"
                                                                                id="totalnumberoflecturestakenbyoneguestteacher"
                                                                                placeholder="Enter Total Number of Lectures Taken by One Guest Teacher" />
                                                                        </div>
                                                                    </div>

                                                                    <div class="col-md-6">
                                                                        <div class="form-group">
                                                                            <label>Total Number of Lectures Taken by All
                                                                                Guest Teachers</label>
                                                                            <input type="text" class="form-control"
                                                                                name="totalNumberOfLectureTakenByAllGuestTeachers"
                                                                                id="totalNumberOfLectureTakenByAllGuestTeachers"
                                                                                placeholder="Enter Total Number of Lectures Taken by All Guest Teachers" />
                                                                        </div>
                                                                    </div>

                                                                    <div class="col-md-6">
                                                                        <div class="form-group">
                                                                            <label>Total Lectures in the Subject in the
                                                                                Month by All Teachers</label>
                                                                            <input type="text" class="form-control"
                                                                                name="totalLecturesInTheSubjectInMonthByAllTeachers"
                                                                                id="totalLecturesInTheSubjectInMonthByAllTeachers"
                                                                                placeholder="Enter Total Lectures in the Subject in the Month by All Teachers" />
                                                                        </div>
                                                                    </div>

                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div class="card card-primary">
                                                            <!-- /.card-header -->
                                                            <div class="card-header">
                                                                <h3 class="card-title">Honorarium Details</h3>
                                                            </div>
                                                            <div class="card-body">
                                                                <div class="row">
                                                                  
                                                                    <div class="col-md-6">
                                                                        <div class="form-group">
                                                                            <label>Enter Honorarium</label>
                                                                            <input type="text" class="form-control"
                                                                                name="honourararium"
                                                                                id="honourararium"
                                                                                placeholder="Enter  Honorarium " />
                                                                        </div>
                                                                    </div>
                                                                    <div class="col-md-6">
                                                                        <div class="form-group">
                                                                            <label>Enter Gross Honorarium
                                                                                payable<em>*</em></label>
                                                                            <input type="text" class="form-control"
                                                                                name="GrossHonorariumpayable"
                                                                                id="GrossHonorariumpayable"
                                                                                placeholder="Enter Gross Honorarium payable" />
                                                                        </div>
                                                                    </div>
                                                                    <div class="col-md-6">
                                                                        <div class="form-group">
                                                                            <label>Deduction If Any<em>*</em></label>
                                                                            <input type="text" class="form-control"
                                                                                name="deductionIfAny"
                                                                                id="deductionIfAny"
                                                                                placeholder="Enter Deduction If Any" />
                                                                        </div>
                                                                    </div>
                                                                    <div class="col-md-6">
                                                                        <div class="form-group">
                                                                            <label>Net Pay for the Last
                                                                                Month<em>*</em></label>
                                                                            <input type="text" class="form-control"
                                                                                name="netpayforthelastmonth"
                                                                                id="netpayforthelastmonth"
                                                                                placeholder="Enter Net Pay for the Last Month" />
                                                                        </div>
                                                                    </div>
                                                                    <div class="col-md-6">
                                                                        <div class="form-group">
                                                                            <label>Name of the Month Year Last Salary
                                                                                Paid<em>*</em></label>
                                                                            <input type="text" class="form-control"
                                                                                name="nameoftheMonthYearLastsalarypaid"
                                                                                id="nameoftheMonthYearLastsalarypaid"
                                                                                placeholder="Enter Name of the Month Year Last Salary Paid" />
                                                                        </div>
                                                                    </div>
                                                                    <div class="col-md-6">
                                                                        <div class="form-group">
                                                                            <label>Date of Uploading Information</label>
                                                                            <input type="text" class="form-control"
                                                                                name="dateofuploadinginformation"
                                                                                id="dateofuploadinginformation"
                                                                                placeholder="Enter Date of Uploading Information" />
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                            <div class="card-footer">
                                                                <button type="button" id="btnsubmit"
                                                                    class="btn btn-success float-right mr-1">
                                                                    Submit Details
                                                                </button>
                                                            </div>
                                                        </div>

                                        </form>
                                    </div>
                                </section>
                            </div>
                            <!-- Main row -->
                        </div>
                        <!-- /.container-fluid -->
                    </section>
                    <!-- /.content -->
                </div>
                <!-- /.content-wrapper -->

                <%- include('../partials/footer'); %>
                    <!-- Control Sidebar -->
                    <aside class="control-sidebar control-sidebar-dark">
                        <!-- Control sidebar content goes here -->
                    </aside>
                    <!-- /.control-sidebar -->
    </div>
    <!-- ./wrapper -->


 
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

    <!-- jQuery UI -->
    <script src="https://code.jquery.com/ui/1.12.1/jquery-ui.min.js"></script>

    <!-- Bootstrap 4 -->
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.bundle.min.js"></script>
    <!-- Sparkline -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-sparklines/2.1.2/jquery.sparkline.min.js"></script>

    <!-- Select2 -->
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>

    <!-- Moment.js -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.1/moment.min.js"></script>

    <!-- Date Range Picker -->
    <script src="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.min.js"></script>

    <!-- Tempusdominus Bootstrap 4 -->
    <script
        src="https://cdnjs.cloudflare.com/ajax/libs/tempusdominus-bootstrap-4/5.1.2/js/tempusdominus-bootstrap-4.min.js"></script>

    <!-- OverlayScrollbars -->
    <script
        src="https://cdnjs.cloudflare.com/ajax/libs/overlayscrollbars/1.13.0/js/jquery.overlayScrollbars.min.js"></script>

    <!-- AdminLTE App -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/admin-lte/3.2.0/js/adminlte.min.js"></script>

    <!-- jQuery Validation -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-validate/1.19.3/jquery.validate.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-validate/1.19.3/additional-methods.min.js"></script>

    <!-- Toastr -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/js/toastr.min.js"></script>

    <!-- SweetAlert2 -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>

    <script src="https://unpkg.com/boxicons@2.1.4/dist/boxicons.js"></script>

    <script>
        const apiBaseUrl = "<%= process.env.apiBaseUrl %>";
        const token = localStorage.getItem("access-token-new");
        var userDetails = localStorage.getItem('user-data');
        var userDetailsObj = JSON.parse(userDetails);
        var universityName = userDetailsObj.university
        $('#university1').append(universityName);
        $('#college1').append(universityName);
        console.log(universityName)
    </script>
 <script>
    $("#btnsearch").on("click", function (e) {
        e.preventDefault();
        const ppoNo = $("#ppoNo").val();
        if (!ppoNo) {
            alert('Please enter a PPO number.');
            return;
        }
        $.ajax({
            url: apiBaseUrl + "v1/stage-two/get-guest-teacher-details",
            method: 'POST',
            data: { payIdNo: ppoNo },
            success: function(response) {
    let data = response.data[0]; 
    $('#prefix').val(data.prefix || '');
    $('#firstName').val(data.firstName || '');
    $('#middleName').val(data.middleName || '');
    $('#lastName').val(data.lastName || '');
    $('#gender').val(data.gender || '');
    $('#dateOfBirth').val(data.dateOfBirth || '');
    $('#ugdegreeSubjectCode').val(data.ugdegreeSubjectCode).trigger('change');
    $('#ugdegreeSubjectPassingYear').val(data.ugdegreeSubjectPassingYear).trigger('change');
    $('#pgdegreeSubjectCode').val(data.pgdegreeSubjectCode).trigger('change');;
    $('#pgdegreeSubjectPassingYear').val(data.pgdegreeSubjectPassingYear).trigger('change');;
    $('#phddegreeSubjectCode').val(data.phddegreeSubjectCode).trigger('change');;
    $('#phddegreeSubjectPassingYear').val(data.phddegreeSubjectPassingYear).trigger('change');;
    $('#ugcNetQualifyingStage').val(data.ugcNetQualifyingStage).trigger('change');
    $('#ugcNetQualifyingYear').val(data.ugcNetQualifyingYear || '');
    $('#fathersName').val(data.fathersName || '');
    $('#mothersName').val(data.mothersName || '');
    $('#spouseName').val(data.spouseName || '');
    $('#emailId').val(data.emailId || '');
    $('#mobileNo').val(data.mobileNo || '');
    $('#pranNo').val(data.pranNo || '');
    $('#panNo').val(data.panNo || '');
    $('#adharCardNumber').val(data.adharCardNumber || '');
    $('#payIdNumber').val(data.payIdNumber || '');
    $('#employeeType').val(data.employeeType).trigger('change');
    $('#universityNameAtJoining').val(data.universityNameAtJoining).trigger('change');
    $('#collegeatthetimeofJoining').val(data.collegeatthetimeofJoining).trigger('change');
    $('#collegeCurrentlyDeputed').val(data.collegeCurrentlyDeputed).trigger('change');
    $('#appointedLetterDate').val(data.appointedLetterDate || '');
    $('#effectiveDateOfJoining').val(data.effectiveDateOfJoining || '');
    $('#appointedLetterOrderNumber').val(data.appointedLetterOrderNumber || '');
    $('#isAppointedAgainstSanctionedPost').val(data.isAppointedAgainstSanctionedPost || '');
    $('#freshorrenewalAppointment').val(data.freshorrenewalAppointment || '');
    $('#subjectForTheAppointmentMade').val(data.subjectForTheAppointmentMade).trigger('change');
    $('#totalnumberofstudentsinallyearsinthesubjectatUGlevel').val(data.totalnumberofstudentsinallyearsinthesubjectatUGlevel || '');
    $('#totalnumberofstudentsinallyearsinthesubjectatPGlevel').val(data.totalnumberofstudentsinallyearsinthesubjectatPGlevel || '');
    $('#TotalnumberofPermanentAll').val(data.TotalnumberofPermanentAll || '');
    $('#Totalnumberofguestteachersinthesubject').val(data.Totalnumberofguestteachersinthesubject || '');
    $('#totalnumberoflecturestakenbyoneguestteacher').val(data.totalnumberoflecturestakenbyoneguestteacher || '');
    $('#totalLecturesInTheSubjectInMonthByAllTeachers').val(data.totalLecturesInTheSubjectMonth || '');
    $('#totalNumberOfLectureTakenByAllGuestTeachers').val(data.lectureTakenByAllGuestTeacher || '');
    $('#netpayforthelastmonth').val(data.netPayForTheLastMonth || '');
    $('#nameoftheMonthYearLastsalarypaid').val(data.nameOrThemonthYearLastSallaryPaid || '');
    $('#dateofuploadinginformation').val(data.dateOfUploadingInformation || '');
    $('#GrossHonorariumpayable').val(data.grosshonorarium || '');
    $('#honourararium').val(data.honourarium || '');
    $('#deductionIfAny').val(data.deductionIfAny || '');
               
                $("#pensionDetails").show();
            },
            error: function(xhr, status, error) {
                console.error('An error occurred:', status, error);
                alert('Failed to fetch data. Please try again.');
            }
        });
    });
    $(function () {
        $('.select2').select2()
    })
    </script>
    <script>
        $(document).ready(function () {
            var apiBaseUrl = "<%= process.env.apiBaseUrl %>";
            $.ajax({
                url: apiBaseUrl + "v1/stage-two/get-university-data",
                type: 'get',
                dataType: 'json',
                success: function (response) {
                    if (response.status === "success") {
                        var universities = response.data;
                        $('#universityNameAtJoining').append($('<option>', {
                            value: '',
                            text: 'Select a university'
                        }));
                        $.each(universities, function (index, item) {
                            $('#universityNameAtJoining').append($('<option>', {
                                value: item.university_name,
                                text: item.university_name
                            }));
                        });

                    } else {
                        console.error('API request failed with status:', response.status);
                    }
                },
                error: function (xhr, status, error) {
                    console.error('Error fetching data from API:', error);
                }
            });
        })
    </script>

    <script>
        $(document).ready(function () {
            $.ajax({
                url: apiBaseUrl + "v1/stage-two/get-college-data",
                type: 'get',
                dataType: 'json',
                success: function (response) {
                    if (response.status === "success") {
                        var universities = response.data;
                        $('#collegeatthetimeofJoining, #collegeCurrentlyDeputed').empty().append($('<option>', {
                            value: '',
                            text: 'Select a College'
                        }));
                        $.each(universities, function (index, item) {
                            $('#collegeatthetimeofJoining').append($('<option>', {
                                value: item.college_name,
                                text: item.college_name
                            }));
                            $('#collegeCurrentlyDeputed').append($('<option>', {
                                value:item.college_name,
                                text: item.college_name
                            }));
                        });
                    } else {
                        console.error('API request failed with status:', response.status);
                    }
                },
                error: function (xhr, status, error) {
                    console.error('Error fetching data from API:', error);
                }
            });
        })
    </script>
    <script>
        $(document).ready(function () {
            $.ajax({
                url: apiBaseUrl + "v1/stage-two/get-subject-data",
                type: 'get',
                dataType: 'json',
                success: function (response) {
                    if (response.status === "success") {
                        var subjects = response.data;
                        $('#ugdegreeSubjectCode, #pgdegreeSubjectCode, #phddegreeSubjectCode, #ugcNetQualifyingStage', '#subjectForTheAppointmentMade')
                            .empty()
                            .append($('<option>', {
                                value: '',
                                text: 'Select Subject Code'
                            }));
                        $.each(subjects, function (index, item) {
                            var option = $('<option>', {
                                value: item.subject_name,
                                text: item.subject_name
                            });
                            $('#ugdegreeSubjectCode').append(option.clone());
                            $('#pgdegreeSubjectCode').append(option.clone());
                            $('#phddegreeSubjectCode').append(option.clone());
                            $('#ugcNetQualifyingStage').append(option.clone());
                            $('#subjectForTheAppointmentMade').append(option.clone());
                        });
                    } else {
                        console.error('API request failed with status:', response.status);
                    }
                },
                error: function (xhr, status, error) {
                    console.error('Error fetching data from API:', error);
                }
            });
        });
    </script>

    <script>
        $(document).ready(function () {
            $('#btnsubmit').on('click', function (event) {
                event.preventDefault();
                var requiredFields = [

                    '#firstName',
                    '#gender',
                    '#dateOfBirth',
                    '#ugdegreeSubjectPassingYear',
                    '#pgdegreeSubjectPassingYear',
                    '#phddegreeSubjectPassingYear',
                    '#ugcNetQualifyingYear',
                    '#fathersName',
                    '#mobileNo',
                    '#panNo',
                    '#payIdNumber',
                    '#employeeType',
                    '#collegeCurrentlyDeputed',
                    '#effectiveDateOfJoining',
                    '#universityNameAtJoining',
                    '#isAppointedAgainstSanctionedPost',
                    '#TotalnumberofPermanentAll',
                    '#Totalnumberofguestteachersinthesubject',
                    '#totalnumberoflecturestakenbyoneguestteacher',
                    '#netpayforthelastmonth',
                ];
                var allFilled = true;
                var emptyFields = [];
                requiredFields.forEach(function (selector) {
                    var value = $(selector).val();
                    if (value === undefined ) {
                        allFilled = false;
                        emptyFields.push(selector);
                    }
                });
                if (!allFilled) {
                    var errorMessage = 'Please fill out the following fields: \n' + emptyFields.map(function (selector) {
                        return $(selector).attr('name') || selector;
                    }).join(', ');
                    alert(errorMessage);
                    return;
                }
                var formData = {
                    prefix: $('#prefix').val() || '',
                    firstName: $('#firstName').val() || '',
                    middleName: $('#middleName').val() || '',
                    lastName: $('#lastName').val() || '',
                    gender: $('#gender').val() || '',
                    dateOfBirth: $('#dateOfBirth').val() || '',
                    ugdegreeSubjectCode: $('#ugdegreeSubjectCode').val() || '',
                    ugdegreeSubjectPassingYear: $('#ugdegreeSubjectPassingYear').val() || '',
                    pgdegreeSubjectCode: $('#pgdegreeSubjectCode').val() || '',
                    pgdegreeSubjectPassingYear: $('#pgdegreeSubjectPassingYear').val() || '',
                    phddegreeSubjectCode: $('#phddegreeSubjectCode').val() || '',
                    phddegreeSubjectPassingYear: $('#phddegreeSubjectPassingYear').val() || '',
                    ugcNetQualifyingStage: $('#ugcNetQualifyingStage').val() || '',
                    ugcNetQualifyingYear: $('#ugcNetQualifyingYear').val() || '',
                    fathersName: $('#fathersName').val() || '',
                    mothersName: $('#mothersName').val() || '',
                    spouseName: $('#spouseName').val() || '',
                    emailId: $('#emailId').val() || '',
                    mobileNo: $('#mobileNo').val() || '',
                    pranNo: $('#pranNo').val() || '',
                    panNo: $('#panNo').val() || '',
                    adharCardNumber: $('#adharCardNumber').val() || '',
                    payIdNumber: $('#payIdNumber').val() || '',
                    employeeType: $('#employeeType').val(),
                    universityNameAtJoining: $('#appointedLetversityNameAtJoining').val() || '',
                    collegeatthetimeofJoining: $('#collegeatthetimeofJoining').val() || '',
                    collegeCurrentlyDeputed: $('#collegeCurrentlyDeputed').val() || '',
                    appointedLetterDate: $('#appointedLetterDate').val() || '',
                    effectiveDateOfJoining: $('#effectiveDateOfJoining').val() || '',
                    appointedLetterOrderNumber: $('#appointedLetterOrderNumber').val() || '',
                    universityNameAtJoining: $('#universityNameAtJoining').val() || '',
                    isAppointedAgainstSanctionedPost: $('#isAppointedAgainstSanctionedPost').val() || '',
                    freshorrenewalAppointment: $('#freshorrenewalAppointment').val() || '',
                    subjectForTheAppointmentMade: $('#subjectForTheAppointmentMade').val() || '',
                    totalnumberofstudentsinallyearsinthesubjectatUGlevel: $('#totalnumberofstudentsinallyearsinthesubjectatUGlevel').val() || '',
                    totalnumberofstudentsinallyearsinthesubjectatPGlevel: $('#totalnumberofstudentsinallyearsinthesubjectatPGlevel').val() || '',
                    TotalnumberofPermanentAll: $('#TotalnumberofPermanentAll').val() || '',
                    Totalnumberofguestteachersinthesubject: $('#Totalnumberofguestteachersinthesubject').val() || '',
                    totalnumberoflecturestakenbyoneguestteacher: $('#totalnumberoflecturestakenbyoneguestteacher').val() || '',
                    netPayForTheLastMonth: $('#netpayforthelastmonth').val() || '',
                    // monthlyPayable: $('#monthlyPayable').val() || '',
                    nameOrThemonthYearLastSallaryPaid: $('#nameoftheMonthYearLastsalarypaid').val() || '',
                    dateOfUploadingInformation: $('#dateofuploadinginformation').val() || '',
                    grosshonorarium: $('#GrossHonorariumpayable').val() || '',
                    honourarium: $('#honourararium').val() || '',
                    deductionIfAny: $('#deductionIfAny').val() || '',
                    lectureTakenByAllGuestTeacher: $('#totalNumberOfLectureTakenByAllGuestTeachers').val() || '',
                    totalLecturesInTheSubjectMonth: $('#totalLecturesInTheSubjectInMonthByAllTeachers').val() || '',
                    updatedBy: universityName,
                    finalSubmit:"Yes",
                };
                $.ajax({
                    url: apiBaseUrl + "v1/stage-two/update-guest-teacher",
                    type: 'POST',
                    contentType: 'application/json',
                    data: JSON.stringify(formData),
                    headers: {
                    token: localStorage.getItem("access-token-new"),
                    "Content-Type": "application/json"
                },
                    success: function (response) {
                        Swal.fire({
                            icon: 'success',
                            title: 'Success',
                            text: ' Guest Teacher details Edited & Updated successfully!',
                        });
                        generatePDF(formData); 
                        setTimeout(() => {
                            location.reload();
                             }, 3000);
                           },
                    error: function (xhr, status, error) {
                        let errorMessage = 'An error occurred while submitting the form. Please try again.';

                        if (xhr.responseJSON && xhr.responseJSON.message) {
                            errorMessage = xhr.responseJSON.message;
                        }
                        Swal.fire({
                            icon: 'error',
                            title: 'Error',
                            text: errorMessage,
                        });
                    }
                });
            });
            function generatePDF(formData) {
                const { jsPDF } = window.jspdf;
                const doc = new jsPDF();

                const titleFont = "helvetica";
                const titleFontSize = 14;
                const titleColor = [0, 0, 0]; // Black

                const fieldFont = "helvetica";
                const fieldFontSize = 10;
                const fieldLabelColor = [0, 0, 0]; // Black
                const fieldValueColor = [50, 50, 50]; // Dark gray
                const lineHeight = 7;
                const pageWidth = doc.internal.pageSize.getWidth();
                const borderMargin = 15;
                // Set up title styles
                doc.setFont(titleFont, "bold");
                doc.setFontSize(titleFontSize);
                doc.setTextColor(...titleColor);
                const additionalText = "(Guest Teacher Details)";
                const titleText = (formData.updatedBy || "University Name") + additionalText;
                const titleX = (pageWidth - doc.getTextWidth(titleText)) / 2;
                doc.text(titleText, titleX, 10); // Y position set to 10 for top margin
                // Sub-title
                doc.text("Employee Personal Details", 10, 20); // Adjust Y position to 20
                // Set up field styles
                doc.setFont(fieldFont, "bold");
                doc.setFontSize(fieldFontSize);
                doc.setTextColor(...fieldLabelColor);
                let y = 30;
                function addTwoFieldsOnSameLineWithBorder(key1, value1, key2, value2, x, y) {
                    const totalWidth = pageWidth - borderMargin * 2;
                    const separatorX = x + totalWidth / 2;
                    // Draw the full-width border
                    doc.setDrawColor(0, 0, 0); // Black border
                    doc.setLineWidth(0.5); // Border thickness
                    doc.rect(x - 2, y - lineHeight, totalWidth, lineHeight + 4);
                    // Draw the vertical separator
                    doc.line(separatorX, y - lineHeight, separatorX, y + 4);
                    // Set text color and write the content
                    doc.setTextColor(...fieldLabelColor);
                    doc.text(`${key1}:`, x + 2, y);
                    doc.setTextColor(...fieldValueColor);
                    doc.text(value1, x + 2 + doc.getTextWidth(`${key1}: `), y);

                    doc.setTextColor(...fieldLabelColor);
                    doc.text(`${key2}:`, separatorX + 2, y);
                    doc.setTextColor(...fieldValueColor);
                    doc.text(value2, separatorX + 2 + doc.getTextWidth(`${key2}: `), y);
                }
                function addContentWithBorder(key, value, x, y, valueXOffset = 50) {
                    const totalWidth = pageWidth - borderMargin * 2;
                    // Draw the full-width border
                    doc.setDrawColor(0, 0, 0); // Black border
                    doc.setLineWidth(0.5); // Border thickness
                    doc.rect(x - 2, y - lineHeight, totalWidth, lineHeight + 4);
                    // Set text color and write the content
                    doc.setTextColor(...fieldLabelColor);
                    doc.text(`${key}:`, x + 2, y);
                    doc.setTextColor(...fieldValueColor);
                    doc.text(value, x + valueXOffset, y);
                }
                addTwoFieldsOnSameLineWithBorder("Prefix", formData.prefix, "First Name", formData.firstName, borderMargin, y);
                y += lineHeight + 5;

                addTwoFieldsOnSameLineWithBorder("Middle Name", formData.middleName, "Last Name", formData.lastName, borderMargin, y);
                y += lineHeight + 5;

                addTwoFieldsOnSameLineWithBorder("Gender", formData.gender, "Date of Birth", formData.dateOfBirth, borderMargin, y);
                y += lineHeight + 5;

                addTwoFieldsOnSameLineWithBorder("Father's Name", formData.fathersName, "Mother's Name", formData.mothersName, borderMargin, y);
                y += lineHeight + 5;

                addTwoFieldsOnSameLineWithBorder("PRAN No", formData.pranNo, "PAN No", formData.panNo, borderMargin, y);
                y += lineHeight + 5;

                addTwoFieldsOnSameLineWithBorder("Aadhar Card No", formData.adharCardNumber, " Mobile Number", formData.mobileNo, borderMargin, y);
                y += lineHeight + 5;

                addTwoFieldsOnSameLineWithBorder("Email", formData.emailId, "UG  Subject Code", formData.ugdegreeSubjectCode, borderMargin, y);
                y += lineHeight + 5;

                addTwoFieldsOnSameLineWithBorder("UG Passing Year", formData.ugdegreeSubjectPassingYear, "PG  Subject Code", formData.pgdegreeSubjectCode, borderMargin, y);
                y += lineHeight + 5;

                addTwoFieldsOnSameLineWithBorder("PG Passing Year", formData.pgdegreeSubjectPassingYear, "PhD  Subject Code", formData.phddegreeSubjectCode, borderMargin, y);
                y += lineHeight + 5;

                addTwoFieldsOnSameLineWithBorder("PhD Passing Year", formData.phddegreeSubjectPassingYear, "UGC NET Qualifying Stage", formData.ugcNetQualifyingStage, borderMargin, y);
                y += lineHeight + 5;

                addTwoFieldsOnSameLineWithBorder("UGC NET Qualifying Year", formData.ugcNetQualifyingYear, "Spouse Name", formData.spouseName, borderMargin, y);
                y += lineHeight + 5;

                if (y >= 250) {
                    doc.addPage();
                    y = 20;
                } else {
                    y += lineHeight * 2;
                }
                doc.setFontSize(titleFontSize);
                doc.setTextColor(...titleColor);
                doc.text("Guest Teacher Official Details", 10, y);
                y += lineHeight + 5;
                doc.setFont(fieldFont, "bold");
                doc.setFontSize(fieldFontSize);
                doc.setTextColor(...fieldLabelColor);


                addContentWithBorder("College at the time of Joining", formData.collegeatthetimeofJoining, borderMargin, y, 100);
                y += lineHeight + 5;
                addContentWithBorder("College Currently Deputed", formData.collegeCurrentlyDeputed, borderMargin, y, 100);
                y += lineHeight + 5;
                addTwoFieldsOnSameLineWithBorder("Effective Date of Joining", formData.effectiveDateOfJoining, "Appointed Letter Order Number", formData.appointedLetterOrderNumber, borderMargin, y);
                y += lineHeight + 5;
                addContentWithBorder("University Name at Joining", formData.universityNameAtJoining, borderMargin, y, 100);
                y += lineHeight + 5;
                addContentWithBorder("Is Appointed Against Sanctioned Post", formData.isAppointedAgainstSanctionedPost, borderMargin, y, 100);
                y += lineHeight + 5;
                if (y >= 250) {
                    doc.addPage();
                    y = 20;
                } else {
                    y += lineHeight * 2;
                }
                doc.setFontSize(titleFontSize);
                doc.setTextColor(...titleColor);
                doc.text("Guest Teacher Official Details", 10, y);
                y += lineHeight + 5;
                doc.setFont(fieldFont, "bold");
                doc.setFontSize(fieldFontSize);
                doc.setTextColor(...fieldLabelColor);

                addContentWithBorder("Fresh or Renewal Appointment", formData.freshorrenewalAppointment, borderMargin, y, 100);
                y += lineHeight + 5;
                addContentWithBorder("Total Number of Permanent All", formData.TotalnumberofPermanentAll, borderMargin, y, 100);
                y += lineHeight + 5;
                addContentWithBorder("Total Number of Lectures Taken by One Guest Teacher", formData.totalnumberoflecturestakenbyoneguestteacher, borderMargin, y, 100);
                y += lineHeight + 5;
                addContentWithBorder("Net Pay for the Last Month", formData.netPayForTheLastMonth, borderMargin, y, 100);
                y += lineHeight + 5;
                addContentWithBorder("Date of Uploading Information", formData.dateOfUploadingInformation, borderMargin, y, 100);
                y += lineHeight + 5;
                // Save the PDF
                doc.save('guestteacher.pdf');
            }
            const formData = {
            };
        });
    </script>
   <script>
    function isTokenExpired(token) {
        if (!token) return true;
        try {
            // Decode JWT payload
            const payloadBase64 = token.split('.')[1];
            const payload = JSON.parse(atob(payloadBase64));
            // Check if current time is past the expiration time
            const isExpired = payload.exp * 1000 < Date.now();
            return isExpired;
        } catch (error) {
            console.error("Failed to decode token:", error);
            return true; 
        }
    }
    // Check token expiration on load
    $(function () {
       const token = localStorage.getItem("access-token-new");
        const userData = JSON.parse(userDetails);
        if (token && userData) {
            // Check role and token expiration
            if (usersData.role !== "University-Admin" || isTokenExpired(token)) {
                alert("Session expired or unauthorized access. Redirecting to login.");
                window.location = "/";
            } else {
                setInterval(() => {
                    if (isTokenExpired(token)) {
                        alert("Session expired. Please login again.");
                        window.location = "/";
                    }
                }, 60000);
            }
        } else {
            window.location = "/";
        }
    });
                    </script> 