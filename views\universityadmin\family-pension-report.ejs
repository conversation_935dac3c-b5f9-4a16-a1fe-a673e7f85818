<!DOCTYPE html>
<html lang="en">
<head>
	<meta charset="UTF-8" />
	<meta http-equiv="X-UA-Compatible" content="IE=edge" />
	<meta name="viewport" content="width=device-width, initial-scale=1.0" />
	<title>
		<%= title %>
	</title>
	<!-- Google Font: Source Sans Pro -->
        <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,400i,700&display=fallback" />

        <!-- Font Awesome -->
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css" />

        <!-- Ionicons -->
        <link rel="stylesheet" href="https://code.ionicframework.com/ionicons/2.0.1/css/ionicons.min.css" />
        <!-- Tempusdominus Bootstrap 4 -->
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/tempusdominus-bootstrap-4/5.39.0/css/tempusdominus-bootstrap-4.min.css" />
        <!-- iCheck -->
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/icheck-bootstrap/3.0.1/icheck-bootstrap.min.css" />
        <!-- Select2 -->
        <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
        <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/select2@4.0.13/dist/css/select2.min.css" />
        <!-- Theme style -->
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/admin-lte/3.1.0/css/adminlte.min.css" />
        <!-- overlayScrollbars -->
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/overlayscrollbars/1.13.0/css/OverlayScrollbars.min.css" />
        <!-- Daterange picker -->
        <link rel="stylesheet" href="https://cdn.datatables.net/1.10.24/css/dataTables.bootstrap4.min.css">
        <!-- DataTables -->
        <link rel="stylesheet" href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap4.min.css">
    
        <link rel="stylesheet" href="https://cdn.datatables.net/responsive/2.2.9/css/responsive.bootstrap4.min.css">
    
        <link rel="stylesheet" href="https://cdn.datatables.net/buttons/2.0.1/css/buttons.bootstrap4.min.css">
        <!-- Baoxicon -->
	<link href='https://unpkg.com/boxicons@2.1.4/css/boxicons.min.css' rel='stylesheet'>
	<style type="text/css">
	</style>
</head>

<body class="hold-transition sidebar-mini layout-fixed sidebar-collapse">
	<div class="wrapper">
		<!-- Preloader -->
		<div class="preloader flex-column justify-content-center align-items-center">
			<img class="animation__shake" src="/images/loader.gif" alt="CodeBucket" height="100"
				width="100" />
		</div>
    <%- include('../partials/header'); %> <%- include('../partials/sidebar'); %>
                   <div class="content-wrapper">
                        <div class="content-header">
                          <div class="container-fluid">
                            <div class="row mb-2">
                              <div class="col-sm-6">
                                <h1 style="color:blue; font-weight: bolder;" class="m-0" >Family Pension Entry /Bulk Upload Report (College Wise)</h1>
                              </div>
                              <div class="col-sm-6">
                                <ol class="breadcrumb float-sm-right">
                                  <li class="breadcrumb-item"><a href="/universityadmin">Home</a></li>
                                  <li class="breadcrumb-item"><a href="/logout">Signout</a></li>
                                </ol>
                              </div>
                            </div>
                          </div>
                        </div>
                        <section class="content">
                          <div class="container-fluid">
                            <div class="row">
                              <section class="col-12">
                                  <div class="card-header">
                                    <button
                                      type="button"
                                      id="btnexcel"
                                      class="btn btn-primary"
                                      onclick="doExport()"
                                    >
                                      Export to Excel
                                    </button>
                                  </div>
                                  <div class="card-body">
                                    <div class="col-md-12">
                                      <table id="mainTable" class="table table-bordered">
                                        <thead id="thead"></thead>
                                        <tbody id="tbody"></tbody>
                                      </table>
                                    </div>
                                  </div>
                                </div>
                              </section>
                              <div class="card card-default" id="seatdetails" style="display: none;">
                                <div class="card-header">
                                  <h3>Deatils</h3>
                                </div>
                                <div class="card-header">
                                  <button
                                    type="button"
                                    id="btnexcel"
                                    class="btn btn-primary"
                                    onclick="doExportList()"
                                  >
                                    Export to Excel
                                  </button>
                                </div>
                                <div class="card-body">
                                  <div class="col-md-12" style="overflow-x: scroll">
                                    <table
                                      class="table table-bordered"
                                      id="mainTable2"
                                    >
                                      <thead id="thead2"></thead>
                                      <tbody id="tbody2"></tbody>
                                    </table>
                                  </div>
                                </div>
                                  </div>
                              </div>
                            </div>
                          </div>
                        </section>
                      </div>
                      <%- include('../partials/footer'); %>
                      <aside class="control-sidebar control-sidebar-dark"></aside>
                    </div>
                <!-- jQuery -->
                <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
                <!-- jQuery UI -->
                <script src="https://code.jquery.com/ui/1.12.1/jquery-ui.min.js"></script>
                <!-- Bootstrap 4 -->
                <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.bundle.min.js"></script>
                <!-- Select2 -->
                <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
                
                <!-- Moment.js -->
                <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.1/moment.min.js"></script>
                
                <!-- Tempusdominus Bootstrap 4 -->
                <script src="https://cdnjs.cloudflare.com/ajax/libs/tempusdominus-bootstrap-4/5.1.2/js/tempusdominus-bootstrap-4.min.js"></script>
                
                <!-- OverlayScrollbars -->
                <script src="https://cdnjs.cloudflare.com/ajax/libs/overlayscrollbars/1.13.0/js/jquery.overlayScrollbars.min.js"></script>
                
                <!-- AdminLTE App -->
                <script src="https://cdnjs.cloudflare.com/ajax/libs/admin-lte/3.2.0/js/adminlte.min.js"></script>
                
                <!-- jQuery Validation -->
                <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-validate/1.19.3/jquery.validate.min.js"></script>
                <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-validate/1.19.3/additional-methods.min.js"></script>
                
                <!-- Toastr -->
                <script src="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/js/toastr.min.js"></script>
                
                <!-- SweetAlert2 -->
                <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
                
                <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
                <!-- boxicon -->
                <script src="https://unpkg.com/boxicons@2.1.4/dist/boxicons.js"></script>
                <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
                <!-- FileSaver.js -->
                <script src="https://cdnjs.cloudflare.com/ajax/libs/FileSaver.js/2.0.5/FileSaver.min.js"></script>
                <!-- jsPDF -->
                <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
                <!-- js-xlsx -->
                <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.core.min.js"></script>
                <!-- tableExport.js -->
                <script src="https://cdn.jsdelivr.net/npm/tableexport.jquery.plugin@1.30.0/tableExport.min.js"></script>
                <script>
          var apiBaseUrl = "<%= process.env.apiBaseUrl %>";
         const token = localStorage.getItem("access-token-new");
		     var userDetails = localStorage.getItem('user-data');
		    var userDetailsObj = JSON.parse(userDetails);
        var universityName= userDetailsObj.university
        $('#university1').append(universityName);
        $('#college1').append(universityName);
        console.log(universityName)
    function doExport() {
      $('#mainTable').tableExport({
        type: 'excel',
        mso: {
          styles: ['background-color', 'color', 'font-family', 'font-size', 'font-weight', 'text-align']
        }
      });
    }
    function doExportList() {
      $('#mainTable2').tableExport({
        type: 'excel',
        mso: {
          styles: ['background-color', 'color', 'font-family', 'font-size', 'font-weight', 'text-align']
        }
      });
    }
 
   function getData() {
    $.ajax({
       url: apiBaseUrl + "v1/stage-two/get-family-pension-data",
        type: "POST",
        headers: {
            token: localStorage.getItem("access-token-new"),
            "Content-Type": "application/json"
        },
        data: JSON.stringify({
                universityName: universityName
            }),
        dataType: "json",
        success: function(res) {
    if (res.status == "success") {
        let thead =
            "<tr><th colspan='4' style='text-align:center'>College Wise Family-Pension Summary Report</th></tr>" +
            "<tr><th>S No.</th><th>College/University Name</th><th>Teaching</th><th>Non-Teaching</th></tr>";

        let tbody = "";
        let totalTeaching = 0;
        let totalNonTeaching = 0;

        // Iterate over the data array to populate the table
        res.data.data.forEach(function(item, index) {
            totalTeaching += item.Teaching_count;
            totalNonTeaching += item.Non_Teaching_count;

            tbody += "<tr>";
            tbody += "<td style='text-align: center;'>" + (index + 1) + "</td>";
            tbody += "<td>" + item.universityName + "</td>";
            tbody += "<td style='text-align: center;'><a href='#seatdetails' class='final-submit-link' data-university-name='" + item.college_name_retirement + "' data-type='T'>" + item.Teaching_count + "</a></td>";
            tbody += "<td style='text-align: center;'><a href='#seatdetails' class='save-link' data-university-name='" + item.college_name_retirement + "' data-type='NT'>" + item.Non_Teaching_count + "</a></td>";
            tbody += "</tr>";
        });

        // Append the grand total row
        tbody += `<tr>
                    <td>#</td>
                    <td>Grand Total</td>
                    <td style='text-align: center;'>${totalTeaching}</td>
                    <td style='text-align: center;'>${totalNonTeaching}</td>
                  </tr>`;

        // Update the table with the constructed HTML
        $("#thead").html(thead);
        $("#tbody").html(tbody);
    

            $(".final-submit-link, .save-link").on("click", function(e) {
                    e.preventDefault();
                    const collegeName = $(this).data("university-name");
                    const employeType = $(this).data("type");

                    let dataPayload = { collegeName: collegeName,universityName:universityName };

                    if (employeType === "T") {
                        dataPayload.employeType = "T";
                    } else if (employeType === "NT") {
                        dataPayload.employeType = "NT";
                    }

                    fetchDetails(dataPayload, employeType);
                });
            }
        }
    });
}



function fetchDetails(dataPayload, employeType) {
    $.ajax({
        url: apiBaseUrl + "v1/stage-two/get-family-pension-data",
        type: "POST",
        headers: {
            token: localStorage.getItem("access-token-new"),
            "Content-Type": "application/json"
        },
        data: JSON.stringify(dataPayload),
        dataType: "json",
        success: function(res) {
            if (res.status == "success") {
                let details =  res.data.familyPension;
                let thead2, tbody2 = "";
                
            
                 if (employeType === "T") {
                  thead2 = `<tr>
    <th>Serial No.</th>
    <th>Prefix</th>
    <th>Full Name</th>
    <th>Gender</th>
    <th>Date of Birth</th>
    <th>Father's Name</th>
    <th>Mother's Name</th>
    <th>Spouse Name</th>
    <th>Email ID</th>
    <th>Mobile No.</th>
    <th>PPO No.</th>
    <th>PRAN No.</th>
    <th>PAN No.</th>
    <th>Aadhar No.</th>
    <th>PayID No</th>
    <th>Employee Type</th>
    <th>Effective Date of Joining</th>
    <th>Date of Retirement</th>
    <th>University Name at Retirement</th>
    <th>College Name at Retirement</th>
   <th> Spouse Bank Account No.</th>
    <th>Spouse Bank Name</th>
    <th>Spouse Branch Name</th>
    <th>Spuse IFSC Code</th>
    <th>Family Pensioner Bank Account No.</th>
    <th>Bank Name</th>
    <th> Branch Name</th>
    <th>IFSC Code</th>
    <th>Basic Pension</th>
    <th>Dearness Relief Percentage</th>
    <th>Medical Allowance</th>
   
    <th>Length of Service</th>
    <th>Date of Death</th>
    <th>Age at Death</th>
    <th>Pay Level</th>
    <th>Last Basic Pay</th>
    <th>Basic Family Pension</th>
    <th>Gross Family Pension</th>
    <th>Income Tax</th>
    <th>Initial Pension</th>
    <th>Reduced Pension</th>
    <th>Pension End Date</th>
    <th>Last Month Gross Family Pension</th>
    <th>Last Month Withdrawn Family Pension</th>
</tr>`;

details.forEach(function(detail, index) {
    tbody2 += "<tr>";
    tbody2 += "<td>" + (index + 1) + "</td>";
    tbody2 += "<td>" + detail.prefix + "</td>";
    tbody2 += "<td>" + detail.firstName + ' ' + detail.MiddleName + ' ' + detail.lastName + "</td>";
    tbody2 += "<td>" + detail.gender + "</td>";
    tbody2 += "<td>" + detail.dateOfBirth + "</td>";
    tbody2 += "<td>" + detail.fathersName + "</td>";
    tbody2 += "<td>" + detail.mothersName + "</td>";
    tbody2 += "<td>" + detail.spouseName + "</td>";
    tbody2 += "<td>" + detail.email + "</td>";
    tbody2 += "<td>" + (detail.personalMobileNumber ? detail.personalMobileNumber.slice(-3).padStart(detail.personalMobileNumber.length, 'x') : '') + "</td>";
    tbody2 += "<td>" + detail.ppoNO + "</td>";
    tbody2 += "<td>" + detail.pranNo + "</td>";
    tbody2 += "<td>" + detail.panNo + "</td>";
    tbody2 += "<td>" + (detail.adharCardNumber ? detail.adharCardNumber.slice(-4).padStart(detail.adharCardNumber.length, 'x') : '') + "</td>";
    tbody2 += "<td>" + detail.payeeId + "</td>";
    tbody2 += "<td>" + detail.employeeType + "</td>";
    tbody2 += "<td>" + detail.effectiveDateOfJoining + "</td>";
    tbody2 += "<td>" + detail.dateOfRetirement + "</td>";
    tbody2 += "<td>" + detail.universityName + "</td>";
    tbody2 += "<td>" + detail.collegeNameRetirement + "</td>";
    tbody2 += "<td>" + (detail.spouseBankNumber ? detail.spouseBankNumber.slice(-4).padStart(detail.spouseBankNumber.length, 'x') : '') + "</td>";
    tbody2 += "<td>" + detail.bankName + "</td>";
    tbody2 += "<td>" + detail.branchName + "</td>";
    tbody2 += "<td>" + detail.ifscCode + "</td>";
    tbody2 += "<td>" + (detail.family_pensionar_account_no ? detail.family_pensionar_account_no.slice(-4).padStart(detail.family_pensionar_account_no.length, 'x') : '') + "</td>";
    tbody2 += "<td>" + detail.family_pensionar_bank_name + "</td>";
    tbody2 += "<td>" + detail.family_pensionar_branch_no + "</td>";
    tbody2 += "<td>" + detail.family_pensionar_ifsc_code + "</td>";
    tbody2 += "<td>" + detail.BasicFamilyPension + "</td>";
    tbody2 += "<td>" + detail.DA + "</td>";
    tbody2 += "<td>" + detail.MedicalAllowance + "</td>";
    tbody2 += "<td>" + detail.lengthOfService + "</td>";
    tbody2 += "<td>" + detail.dateOfDeath + "</td>";
    tbody2 += "<td>" + detail.ageAtDeath + "</td>";
    tbody2 += "<td>" + detail.payLevel + "</td>";
    tbody2 += "<td>" + detail.lastBasicPay + "</td>";
    tbody2 += "<td>" + detail.BasicFamilyPension + "</td>";
    tbody2 += "<td>" + detail.GrossFamilyPension + "</td>";
    tbody2 += "<td>" + detail.IncomeTax + "</td>";
    tbody2 += "<td>" + detail.initialPension + "</td>";
    tbody2 += "<td>" + detail.reducedPension + "</td>";
    tbody2 += "<td>" + detail.pensionEndDate + "</td>";
    tbody2 += "<td>" + detail.LastMonthGrossFamilyPension + "</td>";
    tbody2 += "<td>" + detail.LastMonthWithdrawnFamilyPension + "</td>";
    tbody2 += "</tr>";
});
                } else if (employeType === "NT") {
                  thead2 = `<tr>
    <th>Serial No.</th>
    <th>Prefix</th>
    <th>Full Name</th>
    <th>Gender</th>
    <th>Date of Birth</th>
    <th>Father's Name</th>
    <th>Mother's Name</th>
    <th>Spouse Name</th>
    <th>Email ID</th>
    <th>Mobile No.</th>
    <th>PPO No.</th>
    <th>PRAN No.</th>
    <th>PAN No.</th>
    <th>Aadhar No.</th>
    <th>PayID No</th>
    <th>Employee Type</th>
    <th>Effective Date of Joining</th>
    <th>Date of Retirement</th>
    <th>University Name at Retirement</th>
    <th>College Name at Retirement</th>
    <th>Bank Account No.</th>
    <th>Bank Name</th>
    <th>Branch Name</th>
    <th>IFSC Code</th>
    <th>Basic Pension</th>
    <th>Dearness Relief Percentage</th>
    <th>Medical Allowance</th>
   
    <th>Length of Service</th>
    <th>Date of Death</th>
    <th>Age at Death</th>
    <th>Pay Level</th>
    <th>Last Basic Pay</th>
    <th>Basic Family Pension</th>
    <th>Gross Family Pension</th>
    <th>Income Tax</th>
    <th>Initial Pension</th>
    <th>Reduced Pension</th>
    <th>Pension End Date</th>
    <th>Last Month Gross Family Pension</th>
    <th>Last Month Withdrawn Family Pension</th>
</tr>`;


details.forEach(function(detail, index) {
    tbody2 += "<tr>";
    tbody2 += "<td>" + (index + 1) + "</td>";
    tbody2 += "<td>" + detail.prefix + "</td>";
    tbody2 += "<td>" + detail.firstName + ' ' + detail.MiddleName + ' ' + detail.lastName + "</td>";
    tbody2 += "<td>" + detail.gender + "</td>";
    tbody2 += "<td>" + detail.dateOfBirth + "</td>";
    tbody2 += "<td>" + detail.fathersName + "</td>";
    tbody2 += "<td>" + detail.mothersName + "</td>";
    tbody2 += "<td>" + detail.spouseName + "</td>";
    tbody2 += "<td>" + detail.email + "</td>";
    tbody2 += "<td>" + (detail.personalMobileNumber ? detail.personalMobileNumber.slice(-3).padStart(detail.personalMobileNumber.length, 'x') : '') + "</td>";
    tbody2 += "<td>" + detail.ppoNO + "</td>";
    tbody2 += "<td>" + detail.pranNo + "</td>";
    tbody2 += "<td>" + detail.panNo + "</td>";
    tbody2 += "<td>" + (detail.adharCardNumber ? detail.adharCardNumber.slice(-4).padStart(detail.adharCardNumber.length, 'x') : '') + "</td>";
    tbody2 += "<td>" + detail.payeeId + "</td>";
    tbody2 += "<td>" + detail.employeeType + "</td>";
    tbody2 += "<td>" + detail.effectiveDateOfJoining + "</td>";
    tbody2 += "<td>" + detail.dateOfRetirement + "</td>";
    tbody2 += "<td>" + detail.universityName + "</td>";
    tbody2 += "<td>" + detail.collegeNameRetirement + "</td>";
    tbody2 += "<td>" + detail.family_pensionar_account_no + "</td>";
    tbody2 += "<td>" + detail.family_pensionar_bank_name + "</td>";
    tbody2 += "<td>" + detail.family_pensionar_branch_no + "</td>";
    tbody2 += "<td>" + detail.family_pensionar_ifsc_code + "</td>";
    tbody2 += "<td>" + detail.BasicFamilyPension + "</td>";
    tbody2 += "<td>" + detail.DA + "</td>";
    tbody2 += "<td>" + detail.MedicalAllowance + "</td>";
  
    tbody2 += "<td>" + detail.lengthOfService + "</td>";
    tbody2 += "<td>" + detail.dateOfDeath + "</td>";
    tbody2 += "<td>" + detail.ageAtDeath + "</td>";
    tbody2 += "<td>" + detail.payLevel + "</td>";
    tbody2 += "<td>" + detail.lastBasicPay + "</td>";
    tbody2 += "<td>" + detail.BasicFamilyPension + "</td>";
    tbody2 += "<td>" + detail.GrossFamilyPension + "</td>";
    tbody2 += "<td>" + detail.IncomeTax + "</td>";
    tbody2 += "<td>" + detail.initialPension + "</td>";
    tbody2 += "<td>" + detail.reducedPension + "</td>";
    tbody2 += "<td>" + detail.pensionEndDate + "</td>";
    tbody2 += "<td>" + detail.LastMonthGrossFamilyPension + "</td>";
    tbody2 += "<td>" + detail.LastMonthWithdrawnFamilyPension + "</td>";
    tbody2 += "</tr>";
});
                }

                $("#thead2").html(thead2);
                $("#tbody2").html(tbody2);
                $("#seatdetails").show();
                $('#seatdetails')[0].scrollIntoView();
            }
        }
    });
}
$(document).ready(function() {
    getData();
});
 </script>
 <script>
  function isTokenExpired(token) {
      if (!token) return true;
      try {
          // Decode JWT payload
          const payloadBase64 = token.split('.')[1];
          const payload = JSON.parse(atob(payloadBase64));
          // Check if current time is past the expiration time
          const isExpired = payload.exp * 1000 < Date.now();
          return isExpired;
      } catch (error) {
          console.error("Failed to decode token:", error);
          return true; 
      }
  }
  // Check token expiration on load
  $(function () {
     const token = localStorage.getItem("access-token-new");
      const userData = JSON.parse(userDetails);
      if (token && userData) {
          // Check role and token expiration
          if (usersData.role !== "University-Admin" || isTokenExpired(token)) {
              alert("Session expired or unauthorized access. Redirecting to login.");
              window.location = "/";
          } else {
              setInterval(() => {
                  if (isTokenExpired(token)) {
                      alert("Session expired. Please login again.");
                      window.location = "/";
                  }
              }, 60000);
          }
      } else {
          window.location = "/";
      }
  });
                  </script> 