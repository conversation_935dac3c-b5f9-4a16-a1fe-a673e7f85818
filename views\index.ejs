<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8" />
        <meta http-equiv="X-UA-Compatible" content="IE=edge" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title><%= title %> </title>
        <link rel="icon" type="image/png" sizes="32x32" href="/images/gov1.png">
        <link rel="stylesheet" href="/stylesheets/index.css" />
        <!-- Google Font: Source Sans Pro -->
        <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,400i,700&display=fallback" />
        <!-- Font Awesome -->
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css" />
        <!-- Ionicons -->
        <link rel="stylesheet" href="https://code.ionicframework.com/ionicons/2.0.1/css/ionicons.min.css" />
        <!-- Tempusdominus Bootstrap 4 -->
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/tempusdominus-bootstrap-4/5.39.0/css/tempusdominus-bootstrap-4.min.css" />
        <!-- iCheck -->
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/icheck-bootstrap/3.0.1/icheck-bootstrap.min.css" />
        <!-- Select2 -->
        <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
        <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/select2@4.0.13/dist/css/select2.min.css" />
        <!-- Theme style -->
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/admin-lte/3.1.0/css/adminlte.min.css" />
    </head>
    <nav class="header sticky-header ">
        <div class="container-fluid no-padding d-flex justify-content-between align-items-center">
            <div class="logo-left">
                <img src="https://i.ibb.co/x3BgV1Z/sealbihar.png" alt="sealbihar" height="110px" width="130px">

            </div>
            <div class="logo-center">
                <div class="logo-center mt-2">
                    <div class="text-center">
                        <h3 id="h1">
                            <a href="/" style="font-size: larger; font-weight: bolder; color: #5BBD70;">EDUCATION DEPARTMENT</a>
                        </h3>
                        <h3 id="h1">
                            <a href="/" style="font-size: larger; font-weight: bolder; color:#5BBD70;">शिक्षा विभाग</a>
                        </h3>
                    </div>
                </div>
            </div>
            <div class="logo-right">
                <img src="/images/gov1.png" alt="Left Logo" height="100px" width="90px">
            </div>
        </div>
    </nav>
    <body class="hold-transition login-page">
        <div class="login-box" style="width: 580px;">
            <!-- /.login-logo -->
            <div id="step1">
                <div class="card card-outline card-primary">
                    <div class="card-header text-center">
                        <img src="/images/gov1.png" width="80px" height="10px" style="margin-top: -10px;">
                        <a href="/" class="h1" style="color: black;;">
                            <b><%= productName %> </b>
                        </a>
                        <h6 style="color: seagreen; font-weight: bolder;"> Bihar University Systematic Payroll&Pension Management System</h6>
                    </div>
                    <div class="card-body" style="background-image: url('https://i.ibb.co/7S6cKYN/image2.png'); background-size: cover; background-position: center; ">
                        <p class="login-box-msg" style="color: rgb(255, 0, 13); font-size: larger; font-weight: bold;"> University Admin Login Or </p>
                        <p class="login-box-msg" style="color:blueviolet; font-size: large; font-weight: bold;"> Payment Maker/Checker Login <img src="/images/new.gif" alt="height: auto;">
                        </p>
                        <form action="" id="frmlogin" name="frmlogin" method="post">
                            <div class="input-group mb-3">
                                <div class="input-group-append">
                                    <div class="input-group-text">
                                        <span class="fas fa-user"></span>
                                    </div>
                                </div>
                                <input type="text" class="form-control" id="username" name="username" placeholder="Enter Username" />
                            </div>
                            <div class="input-group mb-3">
                                <div class="input-group-append">
                                    <div class="input-group-text">
                                        <span class="fas fa-lock"></span>
                                    </div>
                                </div>
                                <input type="password" id="password" name="password" class="form-control" placeholder="Enter Password" />
                            </div>
                            <div class="row">
                                <div class="col-3"></div>
                                <button type="submit" id="btnlogin" name="btnlogin"> LogIn With OTP </button>
                            </div>
                        </form>
                        <div class="row">
                            <div class="col-3"></div>
                            <button class="button" id="collegeLoginButton">
                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 36 24">
                                    <path d="m18 0 8 12 10-8-4 20H4L0 4l10 8 8-12z"></path>
                                </svg> College Login/Admin Login Without OTP </button>
                        </div>
                    </div>
                </div>
                <!-- /.card-body -->
                <div class="card-footer " style="background-color: black;">
                    <center> Reset/Change Password: <a href="/reset-password"> Click Here</a> to Reset </center>
                </div>
            </div>
            <div id="step2" style="display: none;">
                <form action="" id="frmotp" name="frmotp" method="post">
                    <span class="mainHeading">Enter OTP For Verification</span>
                    <p class="otpSubheading">We have sent a 6 digit otp code to your mobile number</p>
                    <div class="input-wrapper">
                        <input type="text" class="form-control" id="otpbox" name="otpbox" placeholder="Enter 6 digit otp" pattern="^[0-9]{1,6}$" onkeypress="if(this.value.length==6) return false;" />
                        <input type="hidden" name="university" id="university" />
                        <input type="hidden" name="husername" id="husername" />
                    </div>
                    <button type="submit" id="btnverify" name="btnverify" class="btn btn-primary btn-block"> Verify OTP </button>
                    <p class="resendNote">Didn't receive the code? <button type="button" class="resendBtn">Resend Code</button>
                    </p>
                </form>
            </div>
        </div>
        <div class="card1">
            <div class="wrap">
                <div class="terminal">
                    <hgroup class="head">
                        <p class="title">IP Address: <span id="ip-address">Capturing IP Address...</span></p>
                    </hgroup>
                    <div class="body">
                        <pre class="pre">
											<code>Support Email:</code>
											<code class="cmd" data-cmd="<EMAIL>"></code>
										</pre>
                        <code> Please Reset Your Password within 30 Days.</code>

                    </div>
                </div>
            </div>
        </div>
        <div class="page-wrapper">
            <div id="waterdrop"></div>
            <footer>
              <div class="footer-top">
                <div class="pt-exebar">
                </div>
              <div class="footer-bottom">
                <div class="container">
                  <div class="row">
                    <div class="col-md-3">
                      <div class="footer-site-info">  <img src="https://i.ibb.co/R2y2KNf/image.png" width="30px" height="3px"><a href="" target="_blank">Copyright © 2024 Education Department, Government of Bihar. .
                       .</a></div>
                    </div>
                    <div class="col-md-6">
                      <nav id="footer-navigation" class="site-navigation footer-navigation centered-box" role="navigation">
                        <ul id="footer-menu" class="nav-menu styled clearfix inline-inside">
                          <li id="menu-item-26" class="menu-item menu-item-type-custom menu-item-object-custom menu-item-26"><a href="https://codebuckets.in/" target="_blank"> Design & Developed By:  <img src="/images/BrandLogo1.png" width="30px" height="10px"> Codebucket Solutions Pvt. Ltd</a></li>
                        
                        </ul>
                      </nav>
                    </div>
                    <div class="col-md-3">
                      <div id="footer-socials">
                        <div class="socials inline-inside socials-colored">
        
                          <a href="https://www.facebook.com/EducationDepartmentBihar/?locale=hi_IN" target="_blank" title="Facebook" class="socials-item">
                            <i class="fab fa-facebook-f facebook"></i>
                          </a>
                          <a href="https://x.com/BiharEducation_?ref_src=twsrc%5Egoogle%7Ctwcamp%5Eserp%7Ctwgr%5Eauthor" target="_blank" title="Twitter" class="socials-item">
                            <i class="fab fa-twitter twitter"></i>
        
                          </a>
                          <a href="https://www.instagram.com/bihar_education_dept/?hl=en" target="_blank" title="Instagram" class="socials-item">
                            <i class="fab fa-instagram instagram"></i>
                          </a>
                          <a href="#" target="_blank" title="YouTube" class="socials-item">
                            <i class="fab fa-youtube youtube"></i>
                          </a>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </footer>
</div>
        <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
        <script src="https://cdnjs.cloudflare.com/ajax/libs/twitter-bootstrap/4.6.0/js/bootstrap.bundle.min.js"></script>
        <script src="https://cdnjs.cloudflare.com/ajax/libs/admin-lte/3.1.0/js/adminlte.min.js"></script>
        <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-validate/1.19.3/jquery.validate.min.js"></script>
        <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-validate/1.19.3/additional-methods.min.js"></script>
        <script src="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.js"></script>
        <script>
            $(document).ready(function () {
              $.getJSON('https://api.ipify.org?format=json', function (data) {
                // Update the span with the fetched IP address
                $('#ip-address').text(data.ip);
              }).fail(function () {
                $('#ip-address').text('Unable to fetch IP address.');
                console.error('Error fetching IP address');
              });
            });
          </script>
        <script>
            $(document).ready(function() {
                $("#collegeLoginButton").click(function() {
                    window.location.href = "/collegelogin";
                });
                var apiBaseUrl = "<%= process.env.apiBaseUrl %>";
                // Resend OTP Function
                function resendotp() {
                    $.ajax({
                        url: apiBaseUrl + "v1/auth/login-otp",
                        method: "POST",
                        headers: {
                            "Content-Type": "application/json",
                        },
                        data: JSON.stringify({
                            userId: $("#username").val(),
                        }),
                        dataType: "JSON",
                        beforeSend: function() {
                            // $("#btnlogin").attr("disabled", "disabled");
                        },
                        success: function(res) {
                            if (res.status === "success") {
                                $("#step1").hide();
                                $("#step2").show();
                                $("#husername").val($("#username").val());
                                $("#district").val(res.data.district);
                                $(document).Toasts("create", {
                                    class: "bg-success",
                                    autohide: true,
                                    delay: 2000,
                                    title: "Success",
                                    body: "OTP sent successfully",
                                });
                            }
                        },
                        error: function(res) {
                            $("#step1").show();
                            $("#step2").hide();
                            $(document).Toasts("create", {
                                class: "bg-danger",
                                autohide: true,
                                delay: 2000,
                                title: "Error",
                                body: res.responseJSON.message,
                            });
                        },
                    });
                }
                // Login Form Validation and Submission
                $("#frmlogin").validate({
                    rules: {
                        username: {
                            required: true,
                        },
                    },
                    messages: {
                        username: {
                            required: "Please enter username",
                        },
                    },
                    errorElement: "span",
                    errorPlacement: function(error, element) {
                        error.addClass("invalid-feedback");
                        element.closest(".input-group").append(error);
                    },
                    highlight: function(element) {
                        $(element).addClass("is-invalid");
                    },
                    unhighlight: function(element) {
                        $(element).removeClass("is-invalid");
                    },
                });
                $("#frmlogin").on("submit", function(event) {
                    event.preventDefault();
                    const username = $("#username").val();
                    const password = $("#password").val();
                    if ($("#frmlogin").valid()) {
                        $.ajax({
                            url: apiBaseUrl + "v1/auth/login-otp",
                            method: "POST",
                            headers: {
                                "Content-Type": "application/json",
                            },
                            data: JSON.stringify({
                                userId: username,
                                orignalPassword: password,
                            }),
                            dataType: "JSON",
                            beforeSend: function() {
                                // $("#btnlogin").attr("disabled", "disabled");
                            },
                            success: function(res) {
                                if (res.status === "success") {
                                    $("#step1").hide();
                                    $("#step2").show();
                                    $("#university").val(res.data.university);
                                    $(document).Toasts("create", {
                                        class: "bg-success",
                                        autohide: true,
                                        delay: 2000,
                                        title: "Success",
                                        body: "OTP sent successfully",
                                    });
                                }
                            },
                            error: function(res) {
                                $("#step1").show();
                                $("#step2").hide();
                                $(document).Toasts("create", {
                                    class: "bg-danger",
                                    autohide: true,
                                    delay: 2000,
                                    title: "Error",
                                    body: res.responseJSON.message,
                                });
                            },
                        });
                    }
                });
                // OTP Verification Form
                $("#frmotp").validate({
                    rules: {
                        otpbox: {
                            required: true,
                            digits: true,
                            minlength: 6,
                            maxlength: 6,
                        },
                    },
                    messages: {
                        otpbox: {
                            required: "Please enter a six-digit OTP",
                            digits: "Please enter only digits",
                        },
                    },
                    errorElement: "span",
                    errorPlacement: function(error, element) {
                        error.addClass("invalid-feedback");
                        element.closest(".input-group").append(error);
                    },
                    highlight: function(element) {
                        $(element).addClass("is-invalid");
                    },
                    unhighlight: function(element) {
                        $(element).removeClass("is-invalid");
                    },
                });
                $("#frmotp").on("submit", function(event) {
                    event.preventDefault();
                    const otp = $("#otpbox").val();
                    if ($("#frmotp").valid()) {
                        $.get("https://api.ipify.org?format=json", function(data) {
                            const userIp = data.ip;
                            $.ajax({
                                url: apiBaseUrl + "v1/auth/verify-login-otp",
                                method: "POST",
                                headers: {
                                    "Content-Type": "application/json",
                                },
                                data: JSON.stringify({
                                    otp: otp,
                                    university: $("#username").val(),
                                    ip: userIp,
                                }),
                                dataType: "JSON",
                                success: function(res) {
                                    if (res.status === "success" && res.data) {
                                        localStorage.setItem("access-token-new", res.data.token);
                                        localStorage.setItem("user-data", JSON.stringify(res.data.data));
                                        const role = res.data.data.role;
                                        const roleRedirects = {
                                            "Maker-Login": "/maker-salary-upload",
                                            "Checker-Login": "/checkerview",
                                            "University-Admin": "/universityadmin",
                                            "Approver-Login": "/approvalview",
                                            "College-Admin": "/dashboard",
                                        };
                                        if (roleRedirects[role]) {
                                            window.location = roleRedirects[role];
                                        } else {
                                            $(document).Toasts("create", {
                                                class: "bg-danger",
                                                autohide: true,
                                                delay: 2000,
                                                title: "Error",
                                                body: "Invalid role. Please contact Technical support.",
                                            });
                                        }
                                        $(document).Toasts("create", {
                                            class: "bg-success",
                                            autohide: true,
                                            delay: 2000,
                                            title: "Success",
                                            body: "Login successful.",
                                        });
                                    } else {
                                        $(document).Toasts("create", {
                                            class: "bg-danger",
                                            autohide: true,
                                            delay: 2000,
                                            title: "Error",
                                            body: "Invalid OTP. Please try again.",
                                        });
                                    }
                                },
                            });
                        });
                    }
                });
            });
        </script>
    </body>
</html>