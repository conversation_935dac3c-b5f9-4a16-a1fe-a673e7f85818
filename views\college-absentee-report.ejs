<!DOCTYPE html>
<html lang="en">

<head>
	<meta charset="UTF-8" />
	<meta http-equiv="X-UA-Compatible" content="IE=edge" />
	<meta name="viewport" content="width=device-width, initial-scale=1.0" />
	<title>
		<%= title %>
	</title>
	<!-- Google Font: Source Sans Pro -->
	<link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,400i,700&display=fallback" />

	<!-- Font Awesome -->
	<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css" />

	<!-- Ionicons -->
	<link rel="stylesheet" href="https://code.ionicframework.com/ionicons/2.0.1/css/ionicons.min.css" />


	<!-- Tempusdominus Bootstrap 4 -->
	<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/tempusdominus-bootstrap-4/5.39.0/css/tempusdominus-bootstrap-4.min.css" />

	<!-- iCheck -->
	<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/icheck-bootstrap/3.0.1/icheck-bootstrap.min.css" />

	<!-- Select2 -->
	<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
	<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/select2@4.0.13/dist/css/select2.min.css" />
	
	<!-- Theme style -->
	<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/admin-lte/3.1.0/css/adminlte.min.css" />

	<!-- overlayScrollbars -->
	<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/overlayscrollbars/1.13.0/css/OverlayScrollbars.min.css" />

	<!-- Daterange picker -->
	<link rel="stylesheet" href="https://cdn.datatables.net/1.10.24/css/dataTables.bootstrap4.min.css">


	<!-- DataTables -->
	<link rel="stylesheet" href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap4.min.css">

	<link rel="stylesheet" href="https://cdn.datatables.net/responsive/2.2.9/css/responsive.bootstrap4.min.css">

	<link rel="stylesheet" href="https://cdn.datatables.net/buttons/2.0.1/css/buttons.bootstrap4.min.css">
<!-- Baoxicon -->
	<link href='https://unpkg.com/boxicons@2.1.4/css/boxicons.min.css' rel='stylesheet'>
	<style type="text/css">
		.seat {
			width: 50px;
		}

		input::-webkit-outer-spin-button,
		input::-webkit-inner-spin-button {
			-webkit-appearance: none;
			margin: 0;
		}
        .card-title{
            font-weight: bold !important;
        }
        em{
            color: red;
        }
		.custom-checkbox {
        display: inline-block;
        width: 25px;
        height: 25px;
        background-color: white;
        border: 2px solid #ddd;
        border-radius: 7px;
        margin-right: 15px;
        vertical-align: middle;
        position: relative;
    }

    .form-check-input:checked + .form-check-label .custom-checkbox {
        background-color: blue;
      
    }

    .form-check-input:checked + .form-check-label .custom-checkbox::after {
        content: '';
        position: absolute;
        top: 5px;
        left: 9px;
        width: 6px;
        height: 12px;
        border: solid white;
        border-width: 0 2px 2px 0;
        transform: rotate(45deg);
    }

    .form-check-label {
        cursor: pointer;
    }
	.is-invalid {
    border-color: red;
}

	</style>
</head>

<body class="hold-transition sidebar-mini layout-fixed sidebar-collapse">
	<div class="wrapper">
		<!-- Preloader -->
		<div class="preloader flex-column justify-content-center align-items-center">
			<img class="animation__shake" src="/images/loader.gif" alt="CodeBucket" height="100"
				width="100" />
		</div>

		<!-- Navbar -->
		<%- include('./partials/header'); %>
			<!-- /.navbar -->

			<!-- Main Sidebar Container -->
			<%- include('./partials/sidebar'); %>

				<!-- Content Wrapper. Contains page content -->
				<div class="content-wrapper">
					<!-- Content Header (Page header) -->
					<div class="content-header">
						<div class="container-fluid">
							<div class="row mb-2">
								<div class="col-sm-6">
									<h1 class="m-0" style="color:blue; font-size: xx-large;">College Absentee Report (Month Wise )</h1>
								</div>
								<!-- /.col -->
								<div class="col-sm-6">
									<ol class="breadcrumb float-sm-right">
										<li class="breadcrumb-item"><a href="/logout">Signout</a></li>
									
									</ol>
								</div>
								<!-- /.col -->
							</div>
							<!-- /.row -->
						</div>
						<!-- /.container-fluid -->
					</div>

                    <section class="content">
                        <div class="container-fluid">
                            <div class="row">
                                <section class="col-12">
                                    <div class="card card-success">
                                        <div class="card-header">
                                            <h3 class="card-title" style="font-weight: bolder;">Attendance Month Wise Report</h3>
                                        </div>
                                        <div class="card-body">
                                            <div class="row">
                                                <div class="col-12">
                                                    <div class="card card-primary">
                                                        <div class="card-body">
                                                            <div class="form-group row">
                                                                <!-- First dropdown -->
                                                                <div class="col-md-12">
                                                                    <label for="monthYearDropdown" class="form-title" style="color: red; font-weight: bolder;">Choose your month:</label>
                                                                    <select id="monthYearDropdown" class="form-control" required>
                                                                        <option value="">Select a month</option>
                                                                      
                                                                    </select>
                                                                </div>
                                                            </div>
                                                                </div>
                                                            </div>
                                                            <button type="button" id="btnsearch" class="btn btn-info float-right mr-1">Submit</button>
                                                        </div>
                                                  
                                                </div>
                                            </div>
                                        </div>
                                  
                                </section>
                         
                    
                                            <section class="content">
                                                <div class="container-fluid">
                                                  <div class="row">
                                                    <section class="col-12">
                                                        <div class="card-header">
                                                          <button
                                                            type="button"
                                                            id="btnexcel"
                                                            class="btn btn-primary"
                                                            onclick="doExport()"
                                                          >
                                                            Export to Excel
                                                          </button>
                                                        </div>
                                                        <div class="card-body">
                                                          <div class="col-md-12">
                                                            <table id="mainTable" class="table table-bordered">
                                                              <thead id="thead"></thead>
                                                              <tbody id="tbody"></tbody>
                                                            </table>
                                                          </div>
                                                        </div>
                                                      </div>
                                                    </section>
                                            </form>
                                            </section>
                                            </div>
                                            <!-- Main row -->
                                            </div>
                                            <!-- /.container-fluid -->
                                            </section>
                                            <!-- /.content -->
                                            </div>
                    
                    <%- include('./partials/footer'); %>
                        <!-- Control Sidebar -->
                        <aside class="control-sidebar control-sidebar-dark">
                            <!-- Control sidebar content goes here -->
                        </aside>
                        <!-- /.control-sidebar -->
        </div>
        <!-- ./wrapper -->
        <script>
            $.widget.bridge("uibutton", $.ui.button);
        </script>
           <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    
           <!-- jQuery UI -->
           <script src="https://code.jquery.com/ui/1.12.1/jquery-ui.min.js"></script>
    
           <!-- Bootstrap 4 -->
           <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.bundle.min.js"></script>
           <!-- Select2 -->
           <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
    
           <!-- Moment.js -->
           <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.1/moment.min.js"></script>
    
           <!-- Date Range Picker -->
           <script src="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.min.js"></script>
    
           <!-- Tempusdominus Bootstrap 4 -->
           <script
               src="https://cdnjs.cloudflare.com/ajax/libs/tempusdominus-bootstrap-4/5.1.2/js/tempusdominus-bootstrap-4.min.js"></script>
    
           <!-- OverlayScrollbars -->
           <script
               src="https://cdnjs.cloudflare.com/ajax/libs/overlayscrollbars/1.13.0/js/jquery.overlayScrollbars.min.js"></script>
    
           <!-- AdminLTE App -->
           <script src="https://cdnjs.cloudflare.com/ajax/libs/admin-lte/3.2.0/js/adminlte.min.js"></script>
           <!-- jQuery Validation -->
           <script
               src="https://cdnjs.cloudflare.com/ajax/libs/jquery-validate/1.19.3/jquery.validate.min.js"></script>
           <script
               src="https://cdnjs.cloudflare.com/ajax/libs/jquery-validate/1.19.3/additional-methods.min.js"></script>
           <!-- Toastr -->
           <script src="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/js/toastr.min.js"></script>
    
           <!-- SweetAlert2 -->
           <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    
           <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    
           <script src="https://unpkg.com/boxicons@2.1.4/dist/boxicons.js"></script>
    
    
           <!-- FileSaver.js -->
           <script src="https://cdnjs.cloudflare.com/ajax/libs/FileSaver.js/2.0.5/FileSaver.min.js"></script>
           <!-- jsPDF -->
           <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
           <!-- js-xlsx -->
           <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.core.min.js"></script>
           <!-- tableExport.js -->
           <script src="https://cdn.jsdelivr.net/npm/tableexport.jquery.plugin@1.30.0/tableExport.min.js"></script>
    <script>   

                    const token = localStorage.getItem("access-token-new");
		             var userDetails = localStorage.getItem('user-data');
		              var userDetailsObj = JSON.parse(userDetails);
                        const college = usersData.college;
                        const university = usersData.university;
		                 $('#college').text(college);
                         $('#college1').text(college);
         function doExport() {
      $('#mainTable').tableExport({
        type: 'excel',
        mso: {
          styles: ['background-color', 'color', 'font-family', 'font-size', 'font-weight', 'text-align']
        }
      });
    }

    function doExportList() {
      $('#mainTable2').tableExport({
        type: 'excel',
        mso: {
          styles: ['background-color', 'color', 'font-family', 'font-size', 'font-weight', 'text-align']
        }
      });
    }




        $(document).ready(function() {
            const apiBaseUrl = "<%= process.env.apiBaseUrl %>";
            $.ajax({
                
            url: apiBaseUrl + "v1/absentee/get-month",
            type: 'get',
            dataType: 'json',
            headers: {
            token: localStorage.getItem("access-token-new"),
            "Content-Type": "application/json"
        },
        success: function(response) {
        if (response.status === "success") {
           
            var months = response.data;

            $.each(months, function(index, monthData) {
                var monthYearText = monthData.month_name + " " + monthData.year;
                
                $('#monthYearDropdown').append($('<option>', {
                    value: monthYearText,
                    text: monthYearText  
                }));
            });
            } else {
                console.error('API request failed with status:', response.status);
            }
        },
        error: function(xhr, status, error) {
            console.error('Error fetching data from API:', error);
        }
        });
        
        })
        
        </script>
    <script>

        $(document).ready(function () {
         $('#btnsearch').click(function () {
        const apiBaseUrl = "<%= process.env.apiBaseUrl %>";
        var date = $('#monthYearDropdown').val();

        $.ajax({
            url: apiBaseUrl + "v1/absentee/get-absentee-report",
            // url: "http://**************:5000/v1/absentee/get-absentee-report",
            method: "POST",
            contentType: "application/json",
            data: JSON.stringify({
                "date": date,
            }),
            headers: {
                "token": localStorage.getItem("access-token-new"),
            },
            success: function (response) {
                if (response.status === "success") {
                    var absenteeData = response.data.individualAbsenteeData;

                    // Clear existing table data
                    $('#thead').empty();
                    $('#tbody').empty();

                    // Append table headers
                    $('#thead').append(`
                        <tr>
                            <th>ID</th>
                            <th>Pay ID No</th>
                            <th>Name of Employee</th>
                            <th>Designation</th>
                            <th>University Name</th>
                            <th>College Name</th>
                            <th>Department/Office</th>
                            <th>Total CL</th>
                            <th>Total EL</th>
                            <th>Total Medical</th>
                            <th>Total SL</th>
                            <th>Availed Month</th>
                            <th>Availed CL</th>
                            <th>Availed EL</th>
                            <th>Leave Type</th>
                            <th>Availed Medical</th>
                            <th>Availed SL</th>
                            <th>Balance CL</th>
                            <th>Balance EL</th>
                            <th>Balance Medical</th>
                            <th>Total Working in Month</th>
                            <th>Total Presence in Month</th>
                            <th>Biometric Report Attach</th>
                            <th>Remarks</th>
                            <th>Absentee Date</th>
                          
                        </tr>
                    `);

                    // Iterate over absenteeData and append rows to tbody
                    absenteeData.forEach(function (group) {
                        group.forEach(function (employee) {
                            $('#tbody').append(`
                                <tr>
                                    <td>${employee.id}</td>
                                    <td>${employee.pay_id_no}</td>
                                    <td>${employee.name_of_employee}</td>
                                    <td>${employee.designation}</td>
                                    <td>${employee.university_name}</td>
                                    <td>${employee.college_name}</td>
                                    <td>${employee.department_office || 'N/A'}</td>
                                    <td>${employee.total_cl || 'N/A'}</td>
                                    <td>${employee.total_el || 'N/A'}</td>
                                    <td>${employee.total_medical || 'N/A'}</td>
                                    <td>${employee.total_sl || 'N/A'}</td>
                                    <td>${employee.availed_month || 'N/A'}</td>
                                    <td>${employee.availed_cl || 'N/A'}</td>
                                    <td>${employee.availed_el || 'N/A'}</td>
                                    <td>${employee.leave_type || 'N/A'}</td>
                                    <td>${employee.availed_medical || 'N/A'}</td>
                                    <td>${employee.availed_sl || 'N/A'}</td>
                                    <td>${employee.balance_cl || 'N/A'}</td>
                                    <td>${employee.balance_el || 'N/A'}</td>
                                    <td>${employee.balance_medical || 'N/A'}</td>
                                    <td>${employee.total_working_in_month || 'N/A'}</td>
                                    <td>${employee.total_presence_in_the_month || 'N/A'}</td>
                                    <td>${employee.biometric_report_attach || 'N/A'}</td>
                                    <td>${employee.remarks_if_any || 'N/A'}</td>
                                    <td>${employee.absentee_date || 'N/A'}</td>
                                    
                                </tr>
                            `);
                        });
                    });
                } else {
                    alert("Error fetching data");
                }
            },
            error: function (error) {
                alert("An error occurred while fetching data");
            }
        });
    });
});
</script>
<script>
    function isTokenExpired(token) {
        if (!token) return true;
        try {
            // Decode JWT payload
            const payloadBase64 = token.split('.')[1];
            const payload = JSON.parse(atob(payloadBase64));
            // Check if current time is past the expiration time
            const isExpired = payload.exp * 1000 < Date.now();
            return isExpired;
        } catch (error) {
            console.error("Failed to decode token:", error);
            return true; 
        }
    }
    // Check token expiration on load
    $(function () {
       const token = localStorage.getItem("access-token-new");
       var userDetails = localStorage.getItem('user-data');
       const userData = JSON.parse(userDetails);
        if (token && userData) {
            // Check role and token expiration
            if (userData.role !== "College-Admin" || isTokenExpired(token)) {
                alert("Session expired or unauthorized access. Redirecting to login.");
                window.location = "/";
            } else {
                setInterval(() => {
                    if (isTokenExpired(token)) {
                        alert("Session expired. Please login again.");
                        window.location = "/";
                    }
                }, 60000);
            }
        } else {
            window.location = "/";
        }
    });
                    </script> 