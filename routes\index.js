const express = require("express");
const router = express.Router();
require("dotenv").config();
const productName = "BUSPPMS";
const fileUrl = process.env.apiBaseUrl;
// Helper function to render pages
const renderPage = (page, title) => (req, res, next) => {
    res.render(page, { title, productName, fileUrl });
};

/* GET home page. */
router.get("/", renderPage("index", "University HRMS |Government of Bihar"));

/* College and User Registration Routes */
router.get("/collegelogin", renderPage("collegelogin", "New User Registration - Step 1"));
router.get("/notice-board", renderPage("notice-board", "New User Registration - Step 2"));
router.get("/dashboard", renderPage("dashboard", "Welcome to Dashboard"));
router.get("/mapped-college-number", renderPage("mapped-college-number", "Password Change Request"));
router.get("/reset-password", renderPage("reset-password", "Password Change "));
/* College Absentee Routes */
router.get("/collegeabsentee", renderPage("collegeabsentee", "Employee Absentee Details"));
router.get("/college-absentee-report", renderPage("college-absentee-report", "Employee Absentee Details"));
/* Admin Routes */
router.get("/adminreport", renderPage("adminreport", " Salary Report Details"));
router.get("/admin-pension-report", renderPage("admin-pension-report", " Pension Report Details"));
router.get("/admin-familypension-report", renderPage("admin-familypension-report", " Family-Pension Report Details"));
router.get("/admin-guestteacher-report", renderPage("admin-guestteacher-report", " Guest-Teacher Report Details"));
/* University Admin Routes */
router.get("/universityadmin", renderPage("universityadmin/universityadmin", "Report Details"));
router.get("/universitypension", renderPage("universityadmin/universitypension", "Report Details"));
router.get("/editdetails", renderPage("universityadmin/editdetails", "Edit Details"));
router.get("/raiserequest", renderPage("universityadmin/raiserequest", "Raise Request"));
router.get("/download-university-data", renderPage("universityadmin/download-university-data", "Download Data"));

router.get("/remove-employee-data", renderPage("universityadmin/remove-employee-data", "Remove Employee Request"));
router.get("/remove-pension-data", renderPage("universityadmin/remove-pension-data", "Remove Pension Request"));
router.get("/remove-familypension-data", renderPage("universityadmin/remove-familypension-data", "Remove Family-Pension Request"));
router.get("/remove-guestteacher-data", renderPage("universityadmin/remove-guestteacher-data", "Remove Guest-Teacher Request"));

router.get("/bulkuploadpension", renderPage("universityadmin/bulkuploadpension", "Bulk Upload"));
router.get("/guestteacher", renderPage("universityadmin/guestteacher", "Guest Teacher Upload"));
router.get("/guestteacherindividual", renderPage("universityadmin/guestteacherindividual", "Guest Teacher Upload"));
router.get("/familypension", renderPage("universityadmin/familypension", "Family Pension Upload"));
router.get("/familypensionindividual", renderPage("universityadmin/familypensionindividual", "Family Pension Upload"));

router.get("/pension-report", renderPage("universityadmin/pension-report", "Pension Report"));
router.get("/family-pension-report", renderPage("universityadmin/family-pension-report", "Family Pension Report"));
router.get("/guest-teacher-report", renderPage("universityadmin/guest-teacher-report", "Guest Teacher Report"));
router.get("/pension-edit", renderPage("universityadmin/pension-edit", "Pension Edit"));
router.get("/family-pension-edit", renderPage("universityadmin/family-pension-edit", "Family Pension Edit"));
router.get("/guest-teacher-edit", renderPage("universityadmin/guest-teacher-edit", "Guest Teacher Edit"));
router.get("/upload-salary-data", renderPage("universityadmin/upload-salary-data", "Download Data"));

/* Payment Maker Routes */
router.get("/makerdashboard", renderPage("Makeradmin/makerdashboard", "Payment Maker Dashboard"));
router.get("/makerview", renderPage("Makeradmin/makerview", "Payment Maker Dashboard"));
router.get("/maker-salary-upload", renderPage("Makeradmin/maker-salary-upload", "Payment Maker Dashboard"));
router.get("/pension-maker", renderPage("Makeradmin/pension-maker", "Pension Maker Dashboard"));
router.get("/guest-teacher-maker", renderPage("Makeradmin/guest-teacher-maker", "Guest-Teacher Maker Dashboard"));
router.get("/family-pension-maker", renderPage("Makeradmin/family-pension-maker", "Family-Pension Maker Dashboard"));
/* Payment Checker Routes */
router.get("/checker-dashboard", renderPage("Checkeradmin/checker-dashboard", "Payment Checker Dashboard"));
router.get("/checkerview", renderPage("Checkeradmin/checkerview", "Payment Checker Dashboard"));

/* Approval Routes */
router.get("/approval-dashboard", renderPage("Approvaladmin/approval-dashboard", "Approval Checker Dashboard"));
router.get("/approvalview", renderPage("Approvaladmin/approvalview", "Approval Checker Dashboard"));
router.get("/final-payment-report", renderPage("Approvaladmin/final-payment-report", "Approval Checker Dashboard"));

/* Logout Route */
router.get("/logout", renderPage("logout", "Logout"));



module.exports = router;