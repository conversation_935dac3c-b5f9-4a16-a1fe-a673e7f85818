<!DOCTYPE html>
<html lang="en">

<head>
	<meta charset="UTF-8" />
	<meta http-equiv="X-UA-Compatible" content="IE=edge" />
	<meta name="viewport" content="width=device-width, initial-scale=1.0" />
	<title>
		<%= title %>
	</title>
	<!-- Google Font: Source Sans Pro -->
		<!-- Google Font: Source Sans Pro -->
        <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,400i,700&display=fallback" />

        <!-- Font Awesome -->
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css" />
    
        <!-- Ionicons -->
        <link rel="stylesheet" href="https://code.ionicframework.com/ionicons/2.0.1/css/ionicons.min.css" />
    
    
        <!-- Tempusdominus Bootstrap 4 -->
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/tempusdominus-bootstrap-4/5.39.0/css/tempusdominus-bootstrap-4.min.css" />
    
        <!-- iCheck -->
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/icheck-bootstrap/3.0.1/icheck-bootstrap.min.css" />
    
        <!-- Select2 -->
        <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
        <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/select2@4.0.13/dist/css/select2.min.css" />
        
        <!-- Theme style -->
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/admin-lte/3.1.0/css/adminlte.min.css" />
    
        <!-- overlayScrollbars -->
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/overlayscrollbars/1.13.0/css/OverlayScrollbars.min.css" />
    
        <!-- Daterange picker -->
        <link rel="stylesheet" href="https://cdn.datatables.net/1.10.24/css/dataTables.bootstrap4.min.css">
    
    
        <!-- DataTables -->
        <link rel="stylesheet" href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap4.min.css">
    
        <link rel="stylesheet" href="https://cdn.datatables.net/responsive/2.2.9/css/responsive.bootstrap4.min.css">
    
        <link rel="stylesheet" href="https://cdn.datatables.net/buttons/2.0.1/css/buttons.bootstrap4.min.css">
	<style type="text/css">
		.seat {
			width: 50px;
		}

		input::-webkit-outer-spin-button,
		input::-webkit-inner-spin-button {
			-webkit-appearance: none;
			margin: 0;
		}
        .card-title{
            font-weight: bold !important;
        }
        em{
            color: red;
        }
	</style>
</head>

<body class="hold-transition sidebar-mini layout-fixed sidebar-collapse">
	<div class="wrapper">
		<!-- Preloader -->
		<div class="preloader flex-column justify-content-center align-items-center">
			<img class="animation__shake" src="/images/loader.gif" alt="CodeBucket" height="60"
				width="60" />
		</div>

		<!-- Navbar -->
		<%- include('partials/header'); %>
			<!-- /.navbar -->

			<!-- Main Sidebar Container -->
			<%- include('partials/sidebar'); %>

				<!-- Content Wrapper. Contains page content -->
				<div class="content-wrapper">
					<!-- Content Header (Page header) -->
					<div class="content-header">
						<div class="container-fluid">
							<div class="row mb-2">
								<div class="col-sm-6">
									<h1 class="m-0">Absentee Details</h1>
								</div>
								<!-- /.col -->
								<div class="col-sm-6">
									<ol class="breadcrumb float-sm-right">
										<li class="breadcrumb-item"><a href="/">Home</a></li>
										<li class="breadcrumb-item active">Absentee Details</li>
									</ol>
								</div>
								<!-- /.col -->
							</div>
							<!-- /.row -->
						</div>
						<!-- /.container-fluid -->
					</div>

					<section class="content">
						<div class="container-fluid">
							<div class="row">
								<section class="col-12">
									<form id="frmsearch" name="frmsearch" action="" method="post">
										<div class="card card-primary">
											<!-- /.card-header -->
                                            <div class="card-header">
                                                <h3 class="card-title">Search Employee</h3>
                                            </div>
											<div class="card-body">
                                                <div class="form-group d-flex align-items-center">
                                                    <label for="payeeid" class="mr-2">Payee ID</label>
                                                    <input type="text" class="form-control mr-2" name="payeeid" id="payeeid" placeholder="Enter Payee ID" style="flex: 1" >
                                                  </div>
											</div>
											<div class="card-footer">
												<button type="submit" id="btnsubmit"
													class="btn btn-primary float-right mr-1">
													Search Employee
												</button>
											</div>
										</div>
                                    </form>
                                    <div id="absentee" style="display: none;">
                                        <form id="frmregister" name="frmregister" action="" method="post">
                                            <div class="card card-primary">
                                                <div class="card-header">
                                                    <h3 class="card-title">Employee Absentee Details</h3>
                                                </div>
                                                <div class="card-body">
                                                    <div class="row">
                                                        <div class="col-md-6">
                                                            <div class="form-group">
                                                                <label>Employee Name</label>
                                                                <input type="text" class="form-control" name="NameOfEmployee"
                                                                    id="NameOfEmployee" placeholder="Enter Employee Name" readonly />
                                                            </div>
                                                        </div>
                                                        <div class="col-md-6">
                                                            <div class="form-group">
                                                                <label>Payee ID</label>
                                                                <input type="text" class="form-control" name="PayIdNo"
                                                                    id="PayIdNo" placeholder="Enter Payee ID" />
                                                            </div>
                                                        </div>
                                                        <div class="col-md-6">
                                                            <div class="form-group">
                                                                <label>Designation</label>
                                                                <input type="text" class="form-control" name="Designation"
                                                                    id="Designation" placeholder="Enter Designation" readonly />
                                                            </div>
                                                        </div>
                                                        <div class="col-md-6">
                                                            <div class="form-group">
                                                                <label>Department/Office</label>
                                                                <input type="text" class="form-control" name="DepartmentOffice"
                                                                    id="DepartmentOffice" placeholder="Enter Department" readonly />
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="card card-primary">
                                                <div class="card-header">
                                                    <h3 class="card-title">Total No. of Leave</h3>
                                                </div>
                                                <div class="card-body">
                                                    <div class="row">
                                                        <div class="col-md-3">
                                                            <div class="form-group">
                                                                <label>Casual Leave</label>
                                                                <input type="number" class="form-control" name="TotalCl"
                                                                    id="TotalCl" placeholder="Enter Total CL"  />
                                                            </div>
                                                        </div>
                                                        <div class="col-md-3">
                                                            <div class="form-group">
                                                                <label>Earn Leave</label>
                                                                <input type="number" class="form-control" name="TotalEl"
                                                                    id="TotalEl" placeholder="Enter Total EL"  />
                                                            </div>
                                                        </div>
                                                        <div class="col-md-3">
                                                            <div class="form-group">
                                                                <label>Medical Leave</label>
                                                                <input type="number" class="form-control" name="TotalMedical"
                                                                    id="TotalMedical" placeholder="Enter Total ML"  />
                                                            </div>
                                                        </div>
                                                        <div class="col-md-3">
                                                            <div class="form-group">
                                                                <label>Special Leave</label>
                                                                <input type="number" class="form-control" name="TotalSl"
                                                                    id="TotalSl" placeholder="Enter Total SPL"  />
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="card card-primary">
                                                <div class="card-header">
                                                    <h3 class="card-title">Balance Leave</h3>
                                                </div>
                                                <div class="card-body">
                                                    <div class="row">
                                                        <div class="col-md-4">
                                                            <div class="form-group">
                                                                <label>Casual Leave</label>
                                                                <input type="number" class="form-control" name="BalanceCl"
                                                                    id="BalanceCl" placeholder="Enter Balance CL" />
                                                            </div>
                                                        </div>
                                                        <div class="col-md-4">
                                                            <div class="form-group">
                                                                <label>Earn Leave</label>
                                                                <input type="number" class="form-control" name="BalanceEl"
                                                                    id="BalanceEl" placeholder="Enter Balance EL" />
                                                            </div>
                                                        </div>
                                                        <div class="col-md-4">
                                                            <div class="form-group">
                                                                <label>Medical Leave</label>
                                                                <input type="number" class="form-control" name="BalanceMedical"
                                                                    id="BalanceMedical" placeholder="Enter Balance ML"  />
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="card card-info">
                                                <div class="card-header">
                                                    <h3 class="card-title">Leave Availed in this Month</h3>
                                                    <div class="form-group float-right">
                                                        <label for="availedMonth" >Select Month</label>
                                                        <select class="form-control" id="availedMonth">
                                                            <option value="">Select Month</option>
                                                            <option value="Januray">January</option>
                                                            <option value="February">February</option>
                                                            <option value="March">March</option>
                                                            <option value="April">April</option>
                                                            <option value="May">May</option>
                                                        </select>
                                                    </div>
                                                </div>
                                                <div class="card-body">
                                                    <div class="row">
                                                        <div class="col-md-3">
                                                            <div class="form-group">
                                                                <label>Casual Leave<em>*</em></label>
                                                                <input type="number" class="form-control" name="AvailedCl"
                                                                    id="AvailedCl" placeholder="Enter Total CL" value="0" required />
                                                            </div>
                                                        </div>
                                                        <div class="col-md-3">
                                                            <div class="form-group">
                                                                <label>Earn Leave<em>*</em></label>
                                                                <input type="number" class="form-control" name="AvailedEl"
                                                                    id="AvailedEl" placeholder="Enter Total EL" value="0" required />
                                                            </div>
                                                        </div>
                                                        <div class="col-md-3">
                                                            <div class="form-group">
                                                                <label> Leave Type<em>*</em></label>
                                                                <select class="form-control" name="leaveType" id="leaveType" required>
                                                                    <option value="">Select Medical Leave Type</option>
                                                                    <option value="Half Pay Leave">Half Pay Leave</option>
                                                                    <option value="Commuted Leave">Commuted Leave</option>
                                                                </select>
                                                            </div>
                                                        </div>
                                                        
                                                        <div class="col-md-3">
                                                            <div class="form-group">
                                                                <label> Leave<em>*</em></label>
                                                                <input type="number" class="form-control" name="AvailedMedical"
                                                                    id="AvailedMedical" placeholder="Enter Total ML" value="0" required />
                                                            </div>
                                                        </div>
                                                        <div class="col-md-3">
                                                            <div class="form-group">
                                                                <label>Special Leave<em>*</em></label>
                                                                <input type="number" class="form-control" name="AvailedSl"
                                                                    id="AvailedSl" placeholder="Enter Total SPL" value="0"  />
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="card card-primary">
                                                <div class="card-header">
                                                    <h3 class="card-title">Details of Working Days</h3>
                                                </div>
                                                <div class="card-body">
                                                    <div class="row">
                                                        <div class="col-md-3">
                                                            <div class="form-group">
                                                                <label>Total Working Days<em>*</em></label>
                                                                <input type="number" class="form-control" name="TotalWorkingInMonth"
                                                                    id="TotalWorkingInMonth" placeholder="Enter Total Working Days"  />
                                                            </div>
                                                        </div>
                                                        <div class="col-md-3">
                                                            <div class="form-group">
                                                                <label>Total Present Days<em>*</em></label>
                                                                <input type="number" class="form-control" name="TotalPresenceInTheMonth"
                                                                    id="TotalPresenceInTheMonth" placeholder="Enter Total Present Days"  />
                                                            </div>
                                                        </div>
                                                        <div class="col-md-3">
                                                            <div class="form-group">
                                                                <label>Biometric Report Attach</label>
                                                                <input type="file" class="form-control" name="biometricreport"
                                                                    id="BiometricReportAttach" />
                                                            </div>
                                                        </div>
                                                        <div class="col-md-3">
                                                            <div class="form-group">
                                                                <label>Remarks</label>
                                                                <input type="text" class="form-control" name="RemarksIfAny"
                                                                    id="RemarksIfAny" placeholder="Enter Remarks" />
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="card-footer">
                                                <button type="submit" id="btnsubmit"
                                                    class="btn btn-primary float-right mr-1">
                                                    Submit
                                                </button>
                                            </div>
                                        </form>
                                    </div>
								</section>
							</div>
							<!-- Main row -->
						</div>
						<!-- /.container-fluid -->
					</section>
					<!-- /.content -->
				</div>
				<!-- /.content-wrapper -->
				
				<%- include('partials/footer'); %>
					<!-- Control Sidebar -->
					<aside class="control-sidebar control-sidebar-dark">
						<!-- Control sidebar content goes here -->
					</aside>
					<!-- /.control-sidebar -->
	</div>
	<!-- ./wrapper -->

	<!-- jQuery -->
	<script>
		$.widget.bridge("uibutton", $.ui.button);
	</script>
	<!-- jQuery -->
<!-- jQuery -->
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

<!-- jQuery UI -->
<script src="https://code.jquery.com/ui/1.12.1/jquery-ui.min.js"></script>

<!-- Bootstrap 4 -->
<script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.bundle.min.js"></script>

<!-- DataTables -->
<script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap4.min.js"></script>
<script src="https://cdn.datatables.net/responsive/2.2.9/js/dataTables.responsive.min.js"></script>
<script src="https://cdn.datatables.net/responsive/2.2.9/js/responsive.bootstrap4.min.js"></script>
<script src="https://cdn.datatables.net/buttons/2.2.2/js/dataTables.buttons.min.js"></script>
<script src="https://cdn.datatables.net/buttons/2.2.2/js/buttons.bootstrap4.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.1.3/jszip.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/pdfmake.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/vfs_fonts.js"></script>
<script src="https://cdn.datatables.net/buttons/2.2.2/js/buttons.html5.min.js"></script>
<script src="https://cdn.datatables.net/buttons/2.2.2/js/buttons.print.min.js"></script>
<script src="https://cdn.datatables.net/buttons/2.2.2/js/buttons.colVis.min.js"></script>

<!-- Sparkline -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-sparklines/2.1.2/jquery.sparkline.min.js"></script>

<!-- Select2 -->
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>

<!-- Moment.js -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.1/moment.min.js"></script>

<!-- Date Range Picker -->
<script src="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.min.js"></script>

<!-- Tempusdominus Bootstrap 4 -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/tempusdominus-bootstrap-4/5.1.2/js/tempusdominus-bootstrap-4.min.js"></script>

<!-- OverlayScrollbars -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/overlayscrollbars/1.13.0/js/jquery.overlayScrollbars.min.js"></script>

<!-- AdminLTE App -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/admin-lte/3.2.0/js/adminlte.min.js"></script>

<!-- jQuery Validation -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-validate/1.19.3/jquery.validate.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-validate/1.19.3/additional-methods.min.js"></script>

<!-- Toastr -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/js/toastr.min.js"></script>

<!-- SweetAlert2 -->
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

<script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>

    <script>
        $(function () {
            
    $('.select2').select2();
	const token = localStorage.getItem("access-token-new");
	
		var userDetails = localStorage.getItem('user-data');
		var userDetailsObj = JSON.parse(userDetails);
		var college = userDetailsObj.college;
		$('#college').text(college);
const apiBaseUrl = "<%= process.env.apiBaseUrl %>";


$("#frmsearch").on("submit", function (e) {
    e.preventDefault();
    const payeeId = $("#payeeid").val();
    $("#absentee").show();
  
    fetchEmployeeData(payeeId);
});
		
    
            function fetchEmployeeData(Id) {
                $.ajax({
                	url:"https://university-hrms.codebucketstage.online/api/v1/stage-two/get-absentee-details",
                    method: "POST",
                    contentType: "application/json",
                    data: JSON.stringify({
                          "id": Id
                      }),
                    success: function (data) {
                        // Populate form fields with the fetched data
                        $("#NameOfEmployee").val(data.NameOfEmployee);
                        $("#Designation").val(data.Designation);
                        $("#DepartmentOffice").val(data.DepartmentOffice);
                        $("#TotalCl").val(data.TotalCl);
                        $("#TotalEl").val(data.TotalEl);
                        $("#TotalMedical").val(data.TotalMedical);
                        $("#TotalSl").val(data.TotalSl);
                        $("#availedMonth").val(data.availedMonth);
                        $("#AvailedCl").val(data.AvailedCl);
                        $("#AvailedEl").val(data.AvailedEl);
                        $("#leaveType").val(data.leaveType);
                        $("#AvailedMedical").val(data.AvailedMedical);
                        $("#AvailedSl").val(data.AvailedSl);
                        $("#BalanceCl").val(data.BalanceCl);
                        $("#BalanceEl").val(data.BalanceEl);
                        $("#BalanceMedical").val(data.BalanceMedical);
                        $("#TotalWorkingInMonth").val(data.TotalWorkingInMonth);
                        $("#TotalPresenceInTheMonth").val(data.TotalPresenceInTheMonth);
                        $("#BiometricReportAttach").val(data.BiometricReportAttach);
                        $("#RemarksIfAny").val(data.RemarksIfAny);
                    },
                    error: function (xhr, status, error) {
                        console.error("Error fetching employee data:", error);
                        // Handle error
                    }
                });
            }
        });
        $("#frmregister").submit(function(e){
        e.preventDefault(); // Prevent form from submitting normally
        
        // Collect form data
        var formData = {
            NameOfEmployee: $("#NameOfEmployee").val(),
            PayIdNo: $("#PayIdNo").val(),
            Designation: $("#Designation").val(),
            DepartmentOffice: $("#DepartmentOffice").val(),
            TotalCl: $("#TotalCl").val(),
            TotalEl: $("#TotalEl").val(),
            TotalMedical: $("#TotalMedical").val(),
            TotalSl: $("#TotalSl").val(),
            BalanceCl: $("#BalanceCl").val(),
            BalanceEl: $("#BalanceEl").val(),
            BalanceMedical: $("#BalanceMedical").val(),
            leaveType:$("#leaveType").val(),
            AvailedCl: $("#AvailedCl").val(),
            AvailedEl: $("#AvailedEl").val(),
            AvailedMedical: $("#AvailedMedical").val(),
            AvailedSl: $("#AvailedSl").val(),
            TotalWorkingInMonth: $("#TotalWorkingInMonth").val(),
            TotalPresenceInTheMonth: $("#TotalPresenceInTheMonth").val(),
            BiometricReportAttach: $("#BiometricReportAttach").val(),
            RemarksIfAny: $("#RemarksIfAny").val(),
            availedMonth: $("#availedMonth").val()
        };
        
        // Submit form data using AJAX
        $.ajax({
            type: "POST",
            url:"https://university-hrms.codebucketstage.online/api/v1/stage-two/save-absentee-details",
            data: JSON.stringify(formData),
            contentType: "application/json",
            success: function(response) {
            Swal.fire({
                icon: 'success',
                title: 'Success!',
                text: 'Absentee Details submitted successfully!',
                confirmButtonText: "Okay"
            });
        },
        error: function(xhr, status, error) {
            console.error('Error:', error);
            // Show error message using SweetAlert
            Swal.fire({
                icon: 'error',
                title: 'Oops...',
                text: 'Something went wrong!',
            });
        }
    });
});

    </script>
    <script>
        $(function () {
            if (localStorage.getItem("access-token-new") && localStorage.getItem("user-data")) {
                const usersData = JSON.parse(localStorage.getItem("user-data"));
                if (usersData.role != "College-Admin"){
                    alert("You are not authorized");
                    window.location = "/";
                }
            }
            else window.location = "/";
        });
    </script>
</body>

</html>