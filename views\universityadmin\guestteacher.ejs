<!DOCTYPE html>
<html lang="en">

<head>
	<meta charset="UTF-8" />
	<meta http-equiv="X-UA-Compatible" content="IE=edge" />
	<meta name="viewport" content="width=device-width, initial-scale=1.0" />
	<title>
		<%= title %>
	</title>
	<!-- Google Font: Source Sans Pro -->
	<link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,400i,700&display=fallback" />

	<!-- Font Awesome -->
	<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css" />

	<!-- Ionicons -->
	<link rel="stylesheet" href="https://code.ionicframework.com/ionicons/2.0.1/css/ionicons.min.css" />


	<!-- Tempusdominus Bootstrap 4 -->
	<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/tempusdominus-bootstrap-4/5.39.0/css/tempusdominus-bootstrap-4.min.css" />

	<!-- iCheck -->
	<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/icheck-bootstrap/3.0.1/icheck-bootstrap.min.css" />

	<!-- Select2 -->
	<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
	<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/select2@4.0.13/dist/css/select2.min.css" />
	
	<!-- Theme style -->
	<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/admin-lte/3.1.0/css/adminlte.min.css" />

	<!-- overlayScrollbars -->
	<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/overlayscrollbars/1.13.0/css/OverlayScrollbars.min.css" />

	<!-- Daterange picker -->
	<link rel="stylesheet" href="https://cdn.datatables.net/1.10.24/css/dataTables.bootstrap4.min.css">
<!-- Baoxicon -->
	<link href='https://unpkg.com/boxicons@2.1.4/css/boxicons.min.css' rel='stylesheet'>
	<style type="text/css">

        #downloadExcel{
            color: white;
            text-decoration: none;
            padding: 10px;
        }
     
        #viewInstruction{
            color: white;
            text-decoration: none;
            padding: 10px;
        }
           #page {
            display: none; /* Hide loader by default */
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(255, 255, 255, 0.8);
            justify-content: center;
            align-items: center;
            z-index: 1000;
        }

        #container {
            display: flex;
            justify-content: center;
            align-items: center;
            position: relative;
        }

        #h3 {
            color: rgb(82, 79, 79);
        }

        #ring {
            width: 190px;
            height: 190px;
            border: 1px solid transparent;
            border-radius: 50%;
            position: absolute;
        }

        #ring:nth-child(1) {
            border-bottom: 8px solid rgb(240, 42, 230);
            animation: rotate1 2s linear infinite;
        }

        @keyframes rotate1 {
            from {
                transform: rotateX(50deg) rotateZ(110deg);
            }
            to {
                transform: rotateX(50deg) rotateZ(470deg);
            }
        }

        #ring:nth-child(2) {
            border-bottom: 8px solid rgb(240, 19, 67);
            animation: rotate2 2s linear infinite;
        }

        @keyframes rotate2 {
            from {
                transform: rotateX(20deg) rotateY(50deg) rotateZ(20deg);
            }
            to {
                transform: rotateX(20deg) rotateY(50deg) rotateZ(380deg);
            }
        }

        #ring:nth-child(3) {
            border-bottom: 8px solid rgb(3, 170, 170);
            animation: rotate3 2s linear infinite;
        }

        @keyframes rotate3 {
            from {
                transform: rotateX(40deg) rotateY(130deg) rotateZ(450deg);
            }
            to {
                transform: rotateX(40deg) rotateY(130deg) rotateZ(90deg);
            }
        }

        #ring:nth-child(4) {
            border-bottom: 8px solid rgb(207, 135, 1);
            animation: rotate4 2s linear infinite;
        }

        @keyframes rotate4 {
            from {
                transform: rotateX(70deg) rotateZ(270deg);
            }
            to {
                transform: rotateX(70deg) rotateZ(630deg);
            }
        }

   
        #loader-background {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 999;
        }
		.seat {
			width: 50px;
		}

		input::-webkit-outer-spin-button,
		input::-webkit-inner-spin-button {
			-webkit-appearance: none;
			margin: 0;
		}
        .card-title{
            font-weight: bold !important;
        }
        em{
            color: red;
        }
		.custom-checkbox {
        display: inline-block;
        width: 25px;
        height: 25px;
        background-color: white;
        border: 2px solid #ddd;
        border-radius: 7px;
        margin-right: 15px;
        vertical-align: middle;
        position: relative;
    }

    .form-check-input:checked + .form-check-label .custom-checkbox {
        background-color: blue;
      
    }

    .form-check-input:checked + .form-check-label .custom-checkbox::after {
        content: '';
        position: absolute;
        top: 5px;
        left: 9px;
        width: 6px;
        height: 12px;
        border: solid white;
        border-width: 0 2px 2px 0;
        transform: rotate(45deg);
    }

    .form-check-label {
        cursor: pointer;
    }
	.is-invalid {
    border-color: red;
}
/* From Uiverse.io by Yaya12085 */ 
.form {
  background-color: #fff;
  box-shadow: 0 10px 60px rgb(218, 229, 255);
  border: 1px solid rgb(159, 159, 160);
  border-radius: 20px;
  padding: 2rem .7rem .7rem .7rem;
  text-align: center;
  font-size: 1.125rem;
  /* max-width: 320px; */
}

.form-title {
  color: #000000;
  font-size: 1.8rem;
  font-weight: 500;
}

.form-paragraph {
  margin-top: 10px;
  font-size: 0.9375rem;
  color: rgb(105, 105, 105);
}

.drop-container {
  background-color: #fff;
  position: relative;
  display: flex;
  gap: 10px;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 10px;
  margin-top: 2.1875rem;
  border-radius: 10px;
  border: 2px dashed rgb(171, 202, 255);
  color: #444;
  cursor: pointer;
  transition: background .2s ease-in-out, border .2s ease-in-out;
}

.drop-container:hover {
  background: rgba(0, 140, 255, 0.164);
  border-color: rgba(17, 17, 17, 0.616);
}

.drop-container:hover .drop-title {
  color: #222;
}

.drop-title {
  color: #444;
  font-size: 20px;
  font-weight: bold;
  text-align: center;
  transition: color .2s ease-in-out;
}

#file-input {
  width: 350px;
  max-width: 100%;
  color: #444;
  padding: 2px;
  background: #fff;
  border-radius: 10px;
  border: 1px solid rgba(8, 8, 8, 0.288);
}

#file-input::file-selector-button {
  margin-right: 20px;
  border: none;
  background: #084cdf;
  padding: 10px 20px;
  border-radius: 10px;
  color: #fff;
  cursor: pointer;
  transition: background .2s ease-in-out;
}

#file-input::file-selector-button:hover {
  background: #0d45a5;
}
	</style>
</head>

<body class="hold-transition sidebar-mini layout-fixed sidebar-collapse">
	<div class="wrapper">
		<!-- Preloader -->
		<div class="preloader flex-column justify-content-center align-items-center">
			<img class="animation__shake" src="/images/loader.gif" alt="CodeBucket" height="100"
				width="100" />
		</div>

		<!-- Navbar -->
		<%- include('../partials/header'); %>
			<!-- /.navbar -->

			<!-- Main Sidebar Container -->
			<%- include('../partials/sidebar'); %>

				<!-- Content Wrapper. Contains page content -->
				<div class="content-wrapper">
					<!-- Content Header (Page header) -->
					<div class="content-header">
						<div class="container-fluid">
							<div class="row mb-2">
								<div class="col-sm-6">
									<h1 class="m-0" style="color:blue; font-size: xx-large;">Bulk Upload Guest Teacher Details</h1>
								</div>
								<!-- /.col -->
								<div class="col-sm-6">
									<ol class="breadcrumb float-sm-right">
										<li class="breadcrumb-item"><a href="/logout">Signout</a></li>
									
									</ol>
								</div>
							</div>
						</div>
					</div>

					<section class="content">
						<div class="container-fluid">
							<div class="row">
								<section class="col-12">
									<!-- <form id="frmregister" name="frmregister" action="" method="post"> -->
										<div class="card card-success">
											<!-- /.card-header -->
                                            <div class="card-header">
                                                <h3 class="card-title">Bulk Upload Guest Teacher</h3>
                                            </div>
											<div class="card-body">
												<div class="row">
													<div class="col-12">
														
															<div class="card card-primary">
																
																<div class="card-body">
                                                                    <form id="frmsearch" name="frmsearch" action="" method="post" class="form">
                                                                        <span class="form-title" style="color: red; font-size: x-large;">Guest Teacher Details Uploading.</span> <br>
                                                                        <span class="form-title">Please Download  <span style="color: red; font-weight: bold;">Updated Excel Format </span> and Ensure Excel Format Is Same  As Below and All Mandatory Fields Must be Fill Before Uploading.</span> <br>
                                                                        <span class="form-title" style="color: red;">Please Read ,View and Follow the Instructions Before Uploading Any File .</span>
                                                                        <div>
                                                                            <button type="button" class="btn btn-danger mt-3">
                                                                                <a id="downloadExcel" href="/file/guestteacher.xlsx" download>
                                                                                    Download Excel Format
                                                                                </a>
                                                                            </button>
                                                                            <button type="button" class="btn btn-success mt-3" id="viewInstruction">
                                                                                <a id="viewInstruction" href="/file/guestteacherfinalinstruction.xlsx" download>
                                                                                    View Instruction
                                                                                </a>
                                                                          
                                                                            </button>
                                                                        </div>
                                                                        <span class="form-title mt-3">Upload your file</span>
                                                                        <p class="form-paragraph" style="color: red;">
                                                                            File should be an Excel
                                                                          </p>
                                                                         <label for="file-input" class="drop-container">
                                                                        <span class="drop-title">Drop files here</span>
                                                                        or
                                                                        <input type="file" accept=".xlsx, .xls" required="" id="file-input">
                                                                      </label>
                                                                      </form>
															</div>
															</div>
														<!-- </form> -->
													</div>
                                                    </div>
                                                </div>
                                                <div class="card-footer">
    
                                                    <button type="button" id="btnsubmit"
                                                    class="btn btn-danger float-right mr-1">
                                          Submit  File
                                                </button>
                                                </div>
                                            </div>
                                            <div id="loader-background"></div>
                                            <div id="page">
                                                <div id="container">
                                                    <div id="ring"></div>
                                                    <div id="ring"></div>
                                                    <div id="ring"></div>
                                                    <div id="ring"></div>
                                                    <div id="h3" style="color: rgb(25, 0, 255);">Uploading  File</div>
                                                </div>
                                            </div>
                                            </section>
                                            </div>
                                            </div>
                                            </section>
                                            </div>
                    <%- include('../partials/footer'); %>
                        <!-- Control Sidebar -->
                        <aside class="control-sidebar control-sidebar-dark">
                            <!-- Control sidebar content goes here -->
                        </aside>
                        <!-- /.control-sidebar -->
        </div>
        <script>
            $.widget.bridge("uibutton", $.ui.button);
        </script>
        <!-- jQuery -->
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    
    <!-- jQuery UI -->
    <script src="https://code.jquery.com/ui/1.12.1/jquery-ui.min.js"></script>
    
    <!-- Bootstrap 4 -->
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.bundle.min.js"></script>
    
    <!-- Sparkline -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-sparklines/2.1.2/jquery.sparkline.min.js"></script>
    
    <!-- Select2 -->
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
    
    <!-- Moment.js -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.1/moment.min.js"></script>
    
    <!-- Date Range Picker -->
    <script src="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.min.js"></script>
    
    <!-- Tempusdominus Bootstrap 4 -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/tempusdominus-bootstrap-4/5.1.2/js/tempusdominus-bootstrap-4.min.js"></script>
    
    <!-- OverlayScrollbars -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/overlayscrollbars/1.13.0/js/jquery.overlayScrollbars.min.js"></script>
    
    <!-- AdminLTE App -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/admin-lte/3.2.0/js/adminlte.min.js"></script>
    
    <!-- jQuery Validation -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-validate/1.19.3/jquery.validate.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-validate/1.19.3/additional-methods.min.js"></script>
    
    <!-- Toastr -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/js/toastr.min.js"></script>
    
    <!-- SweetAlert2 -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    
    <script src="https://unpkg.com/boxicons@2.1.4/dist/boxicons.js"></script>

    <script>
    var apiBaseUrl = "<%= process.env.apiBaseUrl %>";
    const token = localStorage.getItem("access-token-new");
    var userDetails = localStorage.getItem('user-data');
    var userDetailsObj = JSON.parse(userDetails);
	var departmentUsername= userDetailsObj.department_username
	console.log(departmentUsername)
    var universityName= userDetailsObj.university
    $('#university1').append(universityName);
    $('#college1').append(universityName);
    console.log(universityName)
    $(document).ready(function() {

        function showLoader() {
            $('#page').css('display', 'flex');
                $('#loader-background').show();
            }
            function hideLoader() {
                $('#page').hide();
                $('#loader-background').hide();
            }
    $('#btnsubmit').click(function() {
        showLoader();
        var fileInput = $('#file-input')[0];
        if (fileInput.files.length === 0) {
            hideLoader();
            Swal.fire({
                
                icon: 'error',
                title: 'Error',
                text: 'Please select a file before submitting.'
            });
            return;
        }
        var file = fileInput.files[0];
        var fileType = file.type;
        var validTypes = ['application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', 'application/vnd.ms-excel'];

        if (!validTypes.includes(fileType)) {
            hideLoader();
            Swal.fire({
                icon: 'error',
                title: 'Error',
                text: 'Only Excel files are allowed.'
            });
            return;
        }
        var formData = new FormData();
        formData.append('file', file);
        formData.append('updatedBy', universityName); 
        $.ajax({
           url: apiBaseUrl + "v1/stage-two/guest-teacher-insert-excel", 
            type: 'POST',
            data: formData,
            contentType: false,
            processData: false,
            headers: {
                    token: localStorage.getItem("access-token-new"),
              
                },
            success: function(response) {
                hideLoader();
                Swal.fire({
                    icon: 'success',
                    title: 'Guest Teacher Excel File Is Finally Submitted Successfully',
                    text: "Please Wait For Approval!",
                    confirmButtonText: 'Okay'
                });
            },
            error: function(xhr, status, error) {
                hideLoader();
                let errorMessage = 'An error occurred. Please try again.';
              if (xhr.responseJSON && xhr.responseJSON.message) {
                  errorMessage = xhr.responseJSON.message;
             }
                Swal.fire({
                    icon: 'error',
                    title: 'Your Excel File is Not Conatins all Mandatory Column Fields And Some Other Issue',
                    text: errorMessage,
                    confirmButtonText: 'OK'
                });
            }
        });
    });
});

</script>
<script>
    function isTokenExpired(token) {
        if (!token) return true;
        try {
            // Decode JWT payload
            const payloadBase64 = token.split('.')[1];
            const payload = JSON.parse(atob(payloadBase64));
            // Check if current time is past the expiration time
            const isExpired = payload.exp * 1000 < Date.now();
            return isExpired;
        } catch (error) {
            console.error("Failed to decode token:", error);
            return true; 
        }
    }
    // Check token expiration on load
    $(function () {
       const token = localStorage.getItem("access-token-new");
        const userData = JSON.parse(userDetails);
        if (token && userData) {
            // Check role and token expiration
            if (usersData.role !== "University-Admin" || isTokenExpired(token)) {
                alert("Session expired or unauthorized access. Redirecting to login.");
                window.location = "/";
            } else {
                setInterval(() => {
                    if (isTokenExpired(token)) {
                        alert("Session expired. Please login again.");
                        window.location = "/";
                    }
                }, 60000);
            }
        } else {
            window.location = "/";
        }
    });
                    </script> 