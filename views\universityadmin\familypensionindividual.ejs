<!DOCTYPE html>
<html lang="en">

<head>
	<meta charset="UTF-8" />
	<meta http-equiv="X-UA-Compatible" content="IE=edge" />
	<meta name="viewport" content="width=device-width, initial-scale=1.0" />
	<title>
		<%= title %>
	</title>
	<!-- Google Font: Source Sans Pro -->
		<!-- Google Font: Source Sans Pro -->
        <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,400i,700&display=fallback" />

        <!-- Font Awesome -->
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css" />
    
        <!-- Ionicons -->
        <link rel="stylesheet" href="https://code.ionicframework.com/ionicons/2.0.1/css/ionicons.min.css" />
    
    
        <!-- Tempusdominus Bootstrap 4 -->
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/tempusdominus-bootstrap-4/5.39.0/css/tempusdominus-bootstrap-4.min.css" />
    
        <!-- iCheck -->
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/icheck-bootstrap/3.0.1/icheck-bootstrap.min.css" />
    
        <!-- Select2 -->
        <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
        <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/select2@4.0.13/dist/css/select2.min.css" />
        
        <!-- Theme style -->
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/admin-lte/3.1.0/css/adminlte.min.css" />
    
        <!-- overlayScrollbars -->
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/overlayscrollbars/1.13.0/css/OverlayScrollbars.min.css" />
    
        <!-- Daterange picker -->
        <link rel="stylesheet" href="https://cdn.datatables.net/1.10.24/css/dataTables.bootstrap4.min.css">
    
    
        <!-- DataTables -->
        <link rel="stylesheet" href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap4.min.css">
    
        <link rel="stylesheet" href="https://cdn.datatables.net/responsive/2.2.9/css/responsive.bootstrap4.min.css">
    
        <link rel="stylesheet" href="https://cdn.datatables.net/buttons/2.0.1/css/buttons.bootstrap4.min.css">
        <!-- Baoxicon -->
	<link href='https://unpkg.com/boxicons@2.1.4/css/boxicons.min.css' rel='stylesheet'>
    <style type="text/css">
		.seat {
			width: 50px;
		}

		input::-webkit-outer-spin-button,
		input::-webkit-inner-spin-button {
			-webkit-appearance: none;
			margin: 0;
		}
        .card-title{
            font-weight: bold !important;
        }
        em{
            color: red;
        }
        .card-primary1 {
            background-color: green;
        }
	</style>
</head>

<body class="hold-transition sidebar-mini layout-fixed sidebar-collapse">
	<div class="wrapper">
		<!-- Preloader -->
		<div class="preloader flex-column justify-content-center align-items-center">
			<img class="animation__shake" src="/images/loader.gif" alt="CodeBucket" height="100"
				width="100" />
		</div>
    <%- include('../partials/header'); %> <%- include('../partials/sidebar'); %>


<!-- Content Wrapper. Contains page content -->
<div class="content-wrapper">
    <!-- Content Header (Page header) -->
    <div class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1 class="m-0" style="color: green;"> Family Pension individual Filling  Details</h1>
                </div>
                <!-- /.col -->
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="/">Home</a></li>
                        <li class="breadcrumb-item active">Family Pension Details & Update Pension individual</li>
                    </ol>
                </div>
                <!-- /.col -->
            </div>
            <!-- /.row -->
        </div>
        <!-- /.container-fluid -->
    </div>

    <section class="content">
        <div class="container-fluid">
            <div class="row">
                <section class="col-12">
                    <form id="frmsearch" name="frmsearch" action="" method="post">
                        <div class="card card-primary">
                            <!-- /.card-header -->
                            <div class="card-header">
                                <h3 class="card-title">Search Pension Deatils</h3>
                            </div>
                            <div class="card-body">
                                <div class="form-group d-flex align-items-center">
                                    <label for="payeeid" class="mr-2" style="color: red;">PPO Number</label>
                                    <input type="text" class="form-control mr-2" name="ppoNo" id="ppoNo" placeholder="Enter PPO Number to Fetch Deatils" style="flex: 1"  >
                                  </div>
                            </div>
                            <div class="card-footer">
                                <button type="submit" id="btnsearch"
                                    class="btn btn-info float-right mr-1">
                                    Search Pensioner
                                </button>
                            </div>
                        </div>
                    </form>
                    <div id="pensionDetails" style="display: none;">
                        <form id="frmregister" name="frmregister" action="" method="post">
                            <div class="card card-primary 1">
                                <div class="card-header">
                                    <h3 class="card-title">Pensioner Personal Details</h3>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label>Prefix<em>*</em></label>
                                                <select class="form-control select2" name="prefix"
                                                    id="prefix" style="width: 100%">
                                                    <option value="Mr.">Mr.</option>
                                                    <option value="Ms." >Ms.</option>
                                                    <option value="Mrs.">Mrs.</option>
                                                    <option value="Dr.">Dr.</option>
                                                    <option value="Prof.">Prof.</option>
                                                    <option value="Shri">Shri</option>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label>First Name<em>*</em></label>
                                                <input type="text" class="form-control" name="firstName"
                                                    id="firstName" placeholder="Enter First Name" 	onkeyup="this.value = this.value.toUpperCase();"  required />
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label>Middle Name</label>
                                                <input type="text" class="form-control" name="MiddleName"
                                                    id="MiddleName" placeholder="Enter Middle Name" 	onkeyup="this.value = this.value.toUpperCase();" />
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label>Last Name</label>
                                                <input type="text" class="form-control" name="lastName"
                                                    id="lastName" placeholder="Enter Last Name" 	onkeyup="this.value = this.value.toUpperCase();"  />
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label>Gender<em>*</em></label>
                                                <select class="form-control select2" name="gender"
                                                    id="gender" style="width: 100%"  required>
                                                    <option value="">Select Gender</option>
                                                    <option value="MALE">MALE</option>
                                                    <option value="FEMALE">FEMALE</option>
                                                    <option value="Other">Other</option>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label>Date of Birth<em>*</em></label>
                                                <input type="text"class="form-control" id="dateOfBirth" name="dateOfBirth" placeholder="DD/MM/YYYY" required>
                                            </div>
                                        </div>
                                        
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label>Father's Name<em>*</em></label>
                                                <input type="text" class="form-control" name="fathersName"
                                                    id="fathersName" placeholder="Enter Father's Name" 	onkeyup="this.value = this.value.toUpperCase();" required/>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label>Mother's Name<em>*</em></label>
                                                <input type="text" class="form-control" name="mothersName"
                                                    id="mothersName" placeholder="Enter Mother's Name"  	onkeyup="this.value = this.value.toUpperCase();" required />
                                            </div>
                                        </div>
                                        <!-- <div class="col-md-6">
                                            <div class="form-group">
                                                <label>Spouse Name (Husband/Wife)</label>
                                                <input type="text" class="form-control" name="spouseName"
                                                    id="spouseName" placeholder="Enter Spouse Name" 	onkeyup="this.value = this.value.toUpperCase();"  />
                                            </div>
                                        </div> -->
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label>Personal Mobile Number<em>*</em></label>
                                                <!-- <input type="number" class="form-control" name="personalMobileNumber"
                                                    id="personalMobileNumber" placeholder="Enter Mobile Number"  minlength="10"   maxlength="10" required  /> -->
                                                    <input
                                                
                                                    class="form-control"
                                                    autocomplete="off"
                                                    type="number"
                                                    data-val="true"
                                                    data-val-length="Invalid Mobile Number"
                                                    data-val-length-max="10"
                                                    data-val-length-min="10"
                                                    data-val-regex="Entered Mobile Number format is not valid."
                                                    data-val-regex-pattern="^\(?([6-9]{1})\)?[-. ]?([0-9]{5})[-. ]?([0-9]{4})$"
                                                    data-val-required="Required"
                                                    id="personalMobileNumber"
                                                    maxlength="10"
                                                    name="personalMobileNumber"
                                                    value=""
                                                    required
                                                  />
                                                  <span
                                                    class="text-danger field-validation-valid"
                                                    data-valmsg-for="personalMobileNumber"
                                                    data-valmsg-replace="true"
                                                  ></span>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label>Personal Email<em>*</em></label>
                                                <input type="email" class="form-control" name="email"
                                                    id="email" placeholder="Enter Email"   />
                                            </div>
                                        </div>
                                        <div class="col-md-6" id="pranNoContainer" >
                                            <div class="form-group">
                                                <label>PRAN No<em>*</em></label>
                                                <input type="number" class="form-control" name="pranNo" id="pranNo" placeholder="Enter PRAN" />
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label>PAN No</label>
                                                <input type="text" class="form-control" name="panNo"
                                                    id="panNo" placeholder="Enter PAN Number" 	onkeyup="this.value = this.value.toUpperCase();"  />
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label>Aadhar Card <em>*</em></label>
                                                <!-- <input type="number" class="form-control" name="aadharCardNo"
                                                    id="aadharCardNo" placeholder="Enter Aadhar Number" required /> -->
                                                    <input
                                                    maxlength="12"
                                                    class="form-control"
                                                    onkeyup="this.value = this.value.toUpperCase();"
                                                    autocomplete="off"
                                                    type="text"
                                                    data-val="true"
                                                    data-val-regex="Invalid Aadhar number."
                                                    data-val-regex-pattern="^[0-9]{12}$"
                                                    data-val-required="Required"
                                                    id="adharCardNumber"
                                                    name="adharCardNumber"
                                                    value=""
                                                    required
                                                  />
                                                  <span
                                                    class="text-danger field-validation-valid"
                                                    data-valmsg-for="adharCardNumber"
                                                    data-valmsg-replace="true"
                                                  ></span>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label>Pay ID No.<em>*</em></label>
                                                <input type="text" class="form-control" name="payeeId"
                                                    id="payeeId" placeholder="Enter Pay ID" 	onkeyup="this.value = this.value.toUpperCase();" required />
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label>Employee Type<em>*</em></label>
                                                <select class="form-control select2" name="employeeType"
                                                    id="employeeType" style="width: 100%" required >
                                                    <option value="">Select Employee Type</option>
                                                    <option value="T">Teaching</option>
                                                    <option value="NT">Non-Teaching</option>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label>PPO No.<em>*</em></label>
                                                <input type="text" class="form-control" name="ppoNO"
                                                    id="ppoNO" placeholder="Enter PPO NO" 	onkeyup="this.value = this.value.toUpperCase();" required />
                                            </div>
                                        </div>
                                    </div>
                                </div>
                               
                           
        <!-- New Fields -->
        <div class="card card-primary">
            <div class="card-header">
                <h3 class="card-title">Employee Official Details</h3>
            </div>
            <div class="card-body">
                <div class="row">
                 
                    <div class="col-md-6">
                        <div class="form-group">
                            <label>Spouse Name<em>*</em></label>
                            <input type="text" class="form-control" id="spouseName" name="spouseName" placeholder="Enter Spouse Name"  onkeyup="this.value = this.value.toUpperCase();" required>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label>Effective date of joining*<em>*</em></label>
                            <input type="text" class="form-control" id="effectiveDateOfJoining" name="effectiveDateOfJoining" placeholder="Enter Effective Date Of Joining(DD/MM/YYYY)" onkeyup="this.value = this.value.toUpperCase();" required>
                        </div>
                    </div>
        
                    <div class="col-md-6">
                        <div class="form-group">
                            <label>Date of Retirement<em>*</em></label>
                            <input type="text" class="form-control" name="dateOfRetirement"
                                id="dateOfRetirement" placeholder="Enter Date of Retirement" required  />
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label>University Name at the time of retirement *<em>*</em></label>
                            <select class="form-control select2" name="universityName"
                                id="universityName" style="width: 100%" >
                                <option value="">Select University Name</option>
                                </select>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label>Designation *<em>*</em></label>
                            <input type="text" class="form-control" name="deginationAtRetirement"
                                id="deginationAtRetirement" placeholder="Enter Designation AT the time Of Retirement" onkeyup="this.value = this.value.toUpperCase();" required />
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label> Length of Service *<em>*</em></label>
                            <input type="text" class="form-control" name="lengthOfService"
                                id="lengthOfService" placeholder="Enter Service Length AT the time Of Retirement"  required/>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label> Date of Death *<em>*(DD/MM/YYYY)</em></label>
                            <input type="text" class="form-control" name="dateOfDeath"
                                id="dateOfDeath" placeholder="Enter Date of Death" required />
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label> Age of Death *<em>*</em></label>
                            <input type="text" class="form-control" name="ageAtDeath"
                                id="ageAtDeath" placeholder="Enter Age of Death AT the time Of Retirement"  />
                        </div>
                    </div>
                   
                </div>
        
                            </div>
                            <section class="col-12">
                                <!-- <form id="frmregister" name="frmregister" action="" method="post"> -->
                                    <div class="card card-primary">
                                        <!-- /.card-header -->
                                        <div class="card-header">
                                            <h3 class="card-title">Spouse Bank Details</h3>
                                        </div>
                                        <div class="card-body">
                                            <div class="row">
                                            
                                                <div class="col-md-6">
                                                    <div class="form-group">
                                                        <label> Spouse Bank Account No<em>*</em></label>
                                                        <input type="number" class="form-control" name="spouseBankNumber"
                                                            id="spouseBankNumber" placeholder="Enter Bank Account No As Par CFMS"   />
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="form-group">
                                                        <label>IFSC Code<em>*</em></label>
                                                        <input type="text" class="form-control" name="ifscCode"
                                                            id="ifscCode" placeholder="Enter Bank IFSC Code" onkeyup="this.value = this.value.toUpperCase();"  />
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="form-group">
                                                        <label>Bank Name<em>*</em></label>
                                                        <input type="text" class="form-control" name="bankName"
                                                            id="bankName" placeholder="Enter Bank Name" onkeyup="this.value = this.value.toUpperCase();"   />
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="form-group">
                                                        <label>Branch Name<em>*</em></label>
                                                        <input type="text" class="form-control" name="branchName"
                                                            id="branchName" placeholder="Enter Bank Branch" onkeyup="this.value = this.value.toUpperCase();"    />
                                                    </div>
                                                </div>
                                              
                                            </div>
                                        </div>
                                    </div>
                                    <div class="card card-primary">
                                        <!-- /.card-header -->
                                        <div class="card-header">
                                            <h3 class="card-title">Family Pensioner Bank Details</h3>
                                        </div>
                                        <div class="card-body">
                                            <div class="row">
                                            
                                                <div class="col-md-6">
                                                    <div class="form-group">
                                                        <label> Family Pensioner Bank Account No<em>*</em></label>
                                                        <input type="number" class="form-control" name="familyPensionerAccountNo"
                                                            id="familyPensionerAccountNo" placeholder="Enter Family Pensioner Bank Account No As Par CFMS"   />
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="form-group">
                                                        <label>IFSC Code<em>*</em></label>
                                                        <input type="text" class="form-control" name="ifscCode"
                                                            id="familyPensionerIFSCCode" placeholder="Enter Bank IFSC Code" onkeyup="this.value = this.value.toUpperCase();"  />
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="form-group">
                                                        <label>Bank Name<em>*</em></label>
                                                        <input type="text" class="form-control" name="familyPensionerBankName"
                                                            id="familyPensionerBankName" placeholder="Enter Bank Name" onkeyup="this.value = this.value.toUpperCase();"   />
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="form-group">
                                                        <label>Branch Name<em>*</em></label>
                                                        <input type="text" class="form-control" name="familyPensionerBranchName"
                                                            id="familyPensionerBranchName" placeholder="Enter Bank Branch" onkeyup="this.value = this.value.toUpperCase();"    />
                                                    </div>
                                                </div>
                                              
                                            </div>
                                        </div>
                                    </div>
                                    <div class="card card-primary">
                                        <!-- /.card-header -->
                                        <div class="card-header">
                                            <h3 class="card-title">Pay Entitlement Details</h3>
                                        </div>
                                        <div class="card-body">
                                            <div class="row">
                                                <div class="col-md-6">
                                                    <div class="form-group">
                                                        <label>Pay Level<em>*</em></label>
                                                        <input type="text" class="form-control" name="payLevel"
                                                            id="payLevel" placeholder="Enter Pay Level" required />
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="form-group">
                                                        <label>Basic Salary (as of 1/1/2016)<em>*</em></label>
                                                        <input type="text" class="form-control" name="basicSalary"
                                                            id="basicSalary" placeholder="Enter Basic Salary" required/>
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="form-group">
                                                        <label>Last Basic Pay<em>*</em></label>
                                                        <input type="text" class="form-control" name="lastBasicPay"
                                                            id="lastBasicPay" placeholder="Enter Last Basic Pay" required />
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="form-group">
                                                        <label>Basic Pension<em>*</em></label>
                                                        <input type="text" class="form-control" name="basicPension"
                                                            id="basicPension" placeholder="Enter Basic Pension" required />
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="form-group">
                                                        <label>PVC NO.<em>*</em></label>
                                                        <input type="text" class="form-control" name="pvcNo"
                                                            id="pvcNo" placeholder="Enter PVC Number" required/>
                                                    </div>
                                                </div>
                                    
                                                <div class="col-md-6">
                                                    <div class="form-group">
                                                        <label>Last Month Gross Family Pension*<em>*</em></label>
                                                        <input type="number" class="form-control" name="LastMonthGrossFamilyPension"
                                                            id="LastMonthGrossFamilyPension" placeholder="Enter Last Month Gross Family Pension" required/>
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="form-group">
                                                        <label>Last Month Withdrawn Family Pension*<em>*</em></label>
                                                        <input type="number" class="form-control" name="LastMonthWithdrawnFamilyPension"
                                                            id="LastMonthWithdrawnFamilyPension" placeholder="Enter Last Month Withdrawn Family Pension" />
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="form-group">
                                                        <label>Basic Family Pension <em>*</em></label>
                                                        <input type="number" class="form-control" name="BasicFamilyPension"
                                                            id="BasicFamilyPension" placeholder="Enter Basic Family Pension" />
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="form-group">
                                                        <label>D.A <em>*</em></label>
                                                        <input type="number" class="form-control" name="DA"
                                                            id="DA" placeholder="Enter D.A" />
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="form-group">
                                                        <label>Medical Allowance <em>*</em></label>
                                                        <input type="number" class="form-control" name="MedicalAllowance"
                                                            id="MedicalAllowance" placeholder="Enter Medical Allowance" />
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="form-group">
                                                        <label>Gross Family Pension <em>*</em></label>
                                                        <input type="number" class="form-control" name="GrossFamilyPension"
                                                            id="GrossFamilyPension" placeholder="Enter Gross Family Pension" />
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="form-group">
                                                        <label>Income Tax <em>*</em></label>
                                                        <input type="number" class="form-control" name="IncomeTax"
                                                            id="IncomeTax" placeholder="Enter Income Tax" />
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="form-group">
                                                        <label>Payable Family Pension <em>*</em></label>
                                                        <input type="number" class="form-control" name="PayableFamilyPension"
                                                            id="PayableFamilyPension" placeholder="Enter Payable Family Pension" required />
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="card-footer">
                                            <button type="button" id="btnsubmit"
                                                class="btn btn-success float-right mr-1">
                                                Submit Pension
                                            </button>
                                        </div>
                                    </div>
                                    
                        </form>
                    </div>
                </section>
            </div>
            <!-- Main row -->
        </div>
        <!-- /.container-fluid -->
    </section>
    <!-- /.content -->
</div>
<!-- /.content-wrapper -->

<%- include('../partials/footer'); %>
    <!-- Control Sidebar -->
    <aside class="control-sidebar control-sidebar-dark">
        <!-- Control sidebar content goes here -->
    </aside>
    <!-- /.control-sidebar -->
</div>
<!-- ./wrapper -->
                <!-- jQuery -->
                <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
                <!-- jQuery UI -->
                <script src="https://code.jquery.com/ui/1.12.1/jquery-ui.min.js"></script>
                <!-- Bootstrap 4 -->
                <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.bundle.min.js"></script>
                <!-- Select2 -->
                <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
                <!-- Moment.js -->
                <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.1/moment.min.js"></script>
                
                <!-- Date Range Picker -->
                <script src="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.min.js"></script>
                
                <!-- Tempusdominus Bootstrap 4 -->
                <script src="https://cdnjs.cloudflare.com/ajax/libs/tempusdominus-bootstrap-4/5.1.2/js/tempusdominus-bootstrap-4.min.js"></script>
                
                <!-- OverlayScrollbars -->
                <script src="https://cdnjs.cloudflare.com/ajax/libs/overlayscrollbars/1.13.0/js/jquery.overlayScrollbars.min.js"></script>
                
                <!-- AdminLTE App -->
                <script src="https://cdnjs.cloudflare.com/ajax/libs/admin-lte/3.2.0/js/adminlte.min.js"></script>
                <!-- Toastr -->
                <script src="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/js/toastr.min.js"></script>
                
                <!-- SweetAlert2 -->
                <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
                <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
                <!-- boxicon -->
                <script src="https://unpkg.com/boxicons@2.1.4/dist/boxicons.js"></script>
                <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
                <!-- FileSaver.js -->
                <script src="https://cdnjs.cloudflare.com/ajax/libs/FileSaver.js/2.0.5/FileSaver.min.js"></script>
                <!-- jsPDF -->
                <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
                <!-- js-xlsx -->
                <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.core.min.js"></script>
            
<script>
        var apiBaseUrl = "<%= process.env.apiBaseUrl %>";
        const token = localStorage.getItem("access-token-new");
		var userDetails = localStorage.getItem('user-data');
		var userDetailsObj = JSON.parse(userDetails);
        var universityName= userDetailsObj.university
        $('#university1').append(universityName);
        $('#college1').append(universityName);
        console.log(universityName)
  </script>
 <script>
$("#btnsearch").on("click", function (e) {
    e.preventDefault();
    const ppoNo = $("#ppoNo").val();
    $("#pensionDetails").show();
});
</script>
<script>
    $(document).ready(function() {
        var apiBaseUrl = "<%= process.env.apiBaseUrl %>";
        $.ajax({
       url: apiBaseUrl + "v1/stage-two/get-university-data",
        type: 'get',
        dataType: 'json',
        success: function(response) {
    
        if (response.status === "success") {
      
            var universities = response.data;
            $('#universityName').append($('<option>', {
                value: '',
                    text: 'Select a university'
                     }));
            $.each(universities, function(index, item) {
                $('#universityName').append($('<option>', {
                    value: item.u_id,
                    text: item.university_name
                }));
            });
         
        } else {
            console.error('API request failed with status:', response.status);
        }
    },
    error: function(xhr, status, error) {
        console.error('Error fetching data from API:', error);
    }
    });
    
    })
    
    </script>
<script>
$(document).ready(function() {

$('#btnsubmit').on('click', function(event) {
 event.preventDefault();
 var requiredFields = [
            '#BasicFamilyPension',
            '#GrossFamilyPension',
            '#IncomeTax',
            '#LastMonthGrossFamilyPension',
            '#LastMonthWithdrawnFamilyPension',
            '#MedicalAllowance',
            '#PayableFamilyPension',
            '#adharCardNumber',
            '#ageAtDeath',
            '#bankName',
            '#basicPension',
            '#basicSalary',
            '#branchName',
            '#dateOfBirth',
            '#dateOfDeath',
            '#dateOfRetirement',
            '#deginationAtRetirement',
            '#effectiveDateOfJoining',
            '#employeeType',
            '#fathersName',
            '#firstName',
            '#gender',
            '#ifscCode',
            '#lastBasicPay',
            '#lengthOfService',
            '#panNo',
            '#payLevel',
            '#payeeId',
            '#personalMobileNumber',
            '#ppoNO',
            '#pranNo',
            '#prefix',
            '#pvcNo',
            '#spouseBankNumber',
            '#spouseName',
            '#universityName',
           
        ];


        var allFilled = true;
        var emptyFields = [];

        requiredFields.forEach(function(selector) {
            var value = $(selector).val();
            if (value === undefined || value.trim() === '') {
                allFilled = false;
                emptyFields.push(selector);
            }
        });


        
        if (!allFilled) {
            var errorMessage = 'Please fill out the following fields: \n' + emptyFields.map(function(selector) {
                return $(selector).attr('name') || selector; 
            }).join(', ');
            alert(errorMessage);
            return; 
        }

    var formData = {
    BasicFamilyPension: $('#BasicFamilyPension').val() || '',
    DA: $('#DA').val() || '',
    GrossFamilyPension: $('#GrossFamilyPension').val() || '',
    IncomeTax: $('#IncomeTax').val() || '',
    LastMonthGrossFamilyPension: $('#LastMonthGrossFamilyPension').val() || '',
    LastMonthWithdrawnFamilyPension: $('#LastMonthWithdrawnFamilyPension').val() || '',
    MedicalAllowance: $('#MedicalAllowance').val() || '',
    MiddleName: $('#MiddleName').val() || '',
    PayableFamilyPension: $('#PayableFamilyPension').val() || '',
    adharCardNumber: $('#adharCardNumber').val() || '',
    ageAtDeath: $('#ageAtDeath').val() || '',
    bankName: $('#bankName').val() || '',
    basicPension: $('#basicPension').val() || '',
    basicSalary: $('#basicSalary').val() || '',
    branchName: $('#branchName').val() || '',
    dateOfBirth: $('#dateOfBirth').val() || '',
    dateOfDeath: $('#dateOfDeath').val() || '',
    dateOfRetirement: $('#dateOfRetirement').val() || '',
    deginationAtRetirement: $('#deginationAtRetirement').val() || '',
    effectiveDateOfJoining: $('#effectiveDateOfJoining').val() || '',
    email: $('#email').val() || '',
    employeeType: $('#employeeType').val() || '',
    fathersName: $('#fathersName').val() || '',
    firstName: $('#firstName').val() || '',
    gender: $('#gender').val() || '',
    ifscCode: $('#ifscCode').val() || '',
    lastBasicPay: $('#lastBasicPay').val() || '',
    lastName: $('#lastName').val() || '',
    lengthOfService: $('#lengthOfService').val() || '',
    mothersName: $('#mothersName').val() || '',
    panNo: $('#panNo').val() || '',
    payLevel: $('#payLevel').val() || '',
    payeeId: $('#payeeId').val() || '',
    personalMobileNumber: $('#personalMobileNumber').val() || '',
    ppoNO: $('#ppoNO').val() || '',
    pranNo: $('#pranNo').val() || '',
    prefix: $('#prefix').val() || '',
    pvcNo: $('#pvcNo').val() || '',
    spouseBankNumber: $('#spouseBankNumber').val() || '',
    spouseName: $('#spouseName').val() || '',
    familyPensionerAccountNo: $('#familyPensionerAccountNo').val() || '',
    familyPensionerBranchName: $('#familyPensionerBranchName').val() || '',
    familyPensionerBankName: $('#familyPensionerBankName').val() || '',
    familyPensionerIFSCCode: $('#familyPensionerIFSCCode').val() || '',
    universityName: $('#universityName').val() || '',
    updatedBy: universityName,
};

           $.ajax({
            url: apiBaseUrl + "v1/stage-two/indi-family-pension",
            type: 'POST',
            contentType: 'application/json',
            headers: {
                    token: localStorage.getItem("access-token-new"),
                    "Content-Type": "application/json"
                },
            data: JSON.stringify(formData),
            success: function(response) {
                Swal.fire({
                    icon: 'success',
                    title: 'Success',
                    text: ' Family Pensioner details submitted successfully!',
                });
                generatePDF(formData); 
                setTimeout(() => {
                  location.reload();
                   }, 3000);
            },
            error: function(xhr, status, error) {
                var errorMessage = 'An error occurred while submitting the form. Please try again.';
                if (xhr.responseJSON && xhr.responseJSON.message) {
                    errorMessage = xhr.responseJSON.message;
                }
                
                Swal.fire({
                    icon: 'error',
                    title: 'Error',
                    text: errorMessage,
    });
            
            }
        });
    });


function generatePDF(formData) {
    const { jsPDF } = window.jspdf;
    const doc = new jsPDF();
    // Custom styles configuration
    const titleFont = "helvetica";
    const titleFontSize = 14;
    const titleColor = [0, 0, 0]; 
    const fieldFont = "helvetica";
    const fieldFontSize = 10;
    const fieldLabelColor = [0, 0, 0];
    const fieldValueColor = [50, 50, 50];

    const lineHeight = 7;
    const pageWidth = doc.internal.pageSize.getWidth();
    const borderMargin = 15;
    // Set up title styles
    doc.setFont(titleFont, "bold");
    doc.setFontSize(titleFontSize);
    doc.setTextColor(...titleColor);

    const additionalText = "(Pensioner Details)";
    const titleText = (formData.updatedBy || "University Name") + additionalText;
    const titleX = (pageWidth - doc.getTextWidth(titleText)) / 2;
    doc.text(titleText, titleX, 10);
    // Sub-title
    doc.text("Employee Personal Details", 10, 20); 

    // Set up field styles
    doc.setFont(fieldFont, "bold");
    doc.setFontSize(fieldFontSize);
    doc.setTextColor(...fieldLabelColor);
    let y = 30; 

    function addTwoFieldsOnSameLineWithBorder(key1, value1, key2, value2, x, y) {
        const totalWidth = pageWidth - borderMargin * 2;
        const separatorX = x + totalWidth / 2;

       
        doc.setDrawColor(0, 0, 0); 
        doc.setLineWidth(0.5); 
        doc.rect(x - 2, y - lineHeight, totalWidth, lineHeight + 4);
        doc.line(separatorX, y - lineHeight, separatorX, y + 4);
        doc.setTextColor(...fieldLabelColor);
        doc.text(`${key1}:`, x + 2, y);
        doc.setTextColor(...fieldValueColor);
        doc.text(value1, x + 2 + doc.getTextWidth(`${key1}: `), y);

        doc.setTextColor(...fieldLabelColor);
        doc.text(`${key2}:`, separatorX + 2, y);
        doc.setTextColor(...fieldValueColor);
        doc.text(value2, separatorX + 2 + doc.getTextWidth(`${key2}: `), y);
    }
    function addContentWithBorder(key, value, x, y, valueXOffset = 50) {
        const totalWidth = pageWidth - borderMargin * 2;

   
        doc.setDrawColor(0, 0, 0); // Black border
        doc.setLineWidth(0.5); // Border thickness
        doc.rect(x - 2, y - lineHeight, totalWidth, lineHeight + 4);

      
        doc.setTextColor(...fieldLabelColor);
        doc.text(`${key}:`, x + 2, y);
        doc.setTextColor(...fieldValueColor);
        doc.text(value, x + valueXOffset, y);
    }
    addTwoFieldsOnSameLineWithBorder("Payee ID", formData.payeeId, "Prefix", formData.prefix, borderMargin, y);
    y += lineHeight + 5;
    addTwoFieldsOnSameLineWithBorder("First Name", formData.firstName, "Middle Name", formData.MiddleName, borderMargin, y);
    y += lineHeight + 5;
    addTwoFieldsOnSameLineWithBorder("Last Name", formData.lastName, "Gender", formData.gender, borderMargin, y);
    y += lineHeight + 5;
    addTwoFieldsOnSameLineWithBorder("Date of Birth", formData.dateOfBirth, "Father's Name", formData.fathersName, borderMargin, y);
    y += lineHeight + 5;
    addTwoFieldsOnSameLineWithBorder("Mother's Name", formData.mothersName,"PRAN No", formData.pranNo, borderMargin, y);
    y += lineHeight + 5;
    addTwoFieldsOnSameLineWithBorder("Personal Mobile Number", formData.personalMobileNumber, "Email", formData.email, borderMargin, y);
    y += lineHeight + 5;
    addTwoFieldsOnSameLineWithBorder("PAN No", formData.panNo, "Aadhar Card No", formData.adharCardNumber, borderMargin, y);
    y += lineHeight + 5;

    doc.setFontSize(titleFontSize);
    doc.setTextColor(...titleColor);
    doc.text("Employee Official Details", 10, y);
    y += lineHeight + 5;

    doc.setFont(fieldFont, "bold");
    doc.setFontSize(fieldFontSize);
    doc.setTextColor(...fieldLabelColor);
	addTwoFieldsOnSameLineWithBorder("Effective Date of Joining", formData.effectiveDateOfJoining,  "Date of Retirement*", formData.dateOfRetirement, borderMargin, y);
    y += lineHeight + 5;
	addContentWithBorder( "University Name", formData.universityNameRetirement, borderMargin, y);
    y += lineHeight + 5;
    // Specific section with full-width border and no vertical separator
	if (y >= 250) {
        doc.addPage();
        y = 20;
    } else {
        y += lineHeight * 2;
    }
    // Add bank details heading
    doc.setFontSize(titleFontSize);
    doc.setTextColor(...titleColor);
    doc.text("Bank Details", 10, y);
    y += lineHeight + 5;

    doc.setFont(fieldFont, "bold");
    doc.setFontSize(fieldFontSize);
    doc.setTextColor(...fieldLabelColor);

    addTwoFieldsOnSameLineWithBorder("Bank Account No", formData.bankAccountNo, "Bank Name", formData.bankName, borderMargin, y);
    y += lineHeight + 5;
    addTwoFieldsOnSameLineWithBorder("Branch Name", formData.branchName, "IFSC Code", formData.ifscCode, borderMargin, y);
    y += lineHeight + 5;
	// doc.addPage();
    // y = 20;
    // Add Pay Entitlement Details heading
    doc.setFontSize(titleFontSize);
    doc.setTextColor(...titleColor);
    doc.text("Pension Details", 10, y);
    y += lineHeight + 5;

    doc.setFont(fieldFont, "bold");
    doc.setFontSize(fieldFontSize);
    doc.setTextColor(...fieldLabelColor);
    
    addTwoFieldsOnSameLineWithBorder("Basic Pension", formData.basicPension, "Dearness Relief (@ % of basic pension)*", formData.dearnessReliefPercentage, borderMargin, y);
    y += lineHeight + 5;
    addTwoFieldsOnSameLineWithBorder("Medical Allowance", formData.medicalAllowance, "Gratuity (if any in the relevant month)", formData.gratuity, borderMargin, y);
    y += lineHeight + 5;
	addContentWithBorder("Leave Encashment (if any in the relevant month)", formData.leaveEncashment, borderMargin, y,100);
    y += lineHeight + 5;
    addContentWithBorder("Arrears (if any in the relevant month)", formData.arrears, borderMargin, y,100);
    y += lineHeight + 5;
    // Save the PDF
    doc.save('Employeepension.pdf');
}
const formData = {
  
};
});		
</script>
<script>
    function isTokenExpired(token) {
        if (!token) return true;
        try {
            // Decode JWT payload
            const payloadBase64 = token.split('.')[1];
            const payload = JSON.parse(atob(payloadBase64));
            // Check if current time is past the expiration time
            const isExpired = payload.exp * 1000 < Date.now();
            return isExpired;
        } catch (error) {
            console.error("Failed to decode token:", error);
            return true; 
        }
    }
    // Check token expiration on load
    $(function () {
       const token = localStorage.getItem("access-token-new");
        const userData = JSON.parse(userDetails);
        if (token && userData) {
            // Check role and token expiration
            if (usersData.role !== "University-Admin" || isTokenExpired(token)) {
                alert("Session expired or unauthorized access. Redirecting to login.");
                window.location = "/";
            } else {
                setInterval(() => {
                    if (isTokenExpired(token)) {
                        alert("Session expired. Please login again.");
                        window.location = "/";
                    }
                }, 60000);
            }
        } else {
            window.location = "/";
        }
    });
                    </script> 