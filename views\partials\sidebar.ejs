<style>
	.nav-item .nav-link i {
    padding: 5px;
	padding-top: 0px; 
	padding-bottom: 4px;
	font-size: 27px;
	
}
.bx-spin {
    display: inline-block;
    animation: bx-spin 2s infinite linear;
    font-size: inherit; 
}

@keyframes bx-spin {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}
.nav-item .nav-link:hover {
		background-color: blue !important;
	}
</style>
<aside class="main-sidebar sidebar-dark-primary elevation-4">
	<!-- Brand Logo -->
	<a href="/" class="brand-link">
		<img
			src="/images/BrandLogo.png"
			alt="CodeBucket"
			class="brand-image img-circle elevation-3"
			style="opacity: 0.8"
		/>
		<span class="brand-text font-weight-light" style="color: rgb(0, 255, 149); font-size: larger;" ><%= productName %></span>
	</a>
	<!-- Sidebar -->
	<div class="sidebar">
		<!-- Sidebar user panel (optional) -->
		<div class="user-panel mt-3 pb-3 mb-3 d-flex">
			<div class="info">
				<a href="#" class="d-block" id="college1"></a>
			</div>
		</div>
		<!-- Sidebar Menu -->
		<span id="sideBar"> </span>
		<!-- /.sidebar-menu -->
	</div>
	<!-- /.sidebar -->
</aside>

<script>
	const usersData = JSON.parse(localStorage.getItem("user-data"));

	if (usersData) {
		console.log(usersData.role)
		
		
		if(usersData.role=="College-Admin") {
			$("#sideBar").html(`<nav class="mt-2">
			<ul
				class="nav nav-pills nav-sidebar flex-column"
				data-widget="treeview"
				role="menu"
				data-accordion="false"
			>
			<li class="nav-item">
					<a href="/dashboard" class="nav-link">
						<i class='bx bxs-color bx-spin' ></i>
						<p>Dashboard</p>
					</a>
				</li>
				<li class="nav-item">
					<a href="notice-board" class="nav-link">
						<i class='bx bx-log-in-circle bx-tada' ></i>
						<p>Registration Closed</p>
					</a>
				</li>
				<li class="nav-item">
					<a href="/collegeabsentee" class="nav-link">
					<i class='bx bxs-lock-open bx-tada' ></i>
						<p> Upload Attendance Report</p>
					</a>
				</li>
				<li class="nav-item">
					<a href="/college-absentee-report" class="nav-link">
					<i class='bx bxs-report bx-flashing' ></i>
						<p> View Attendance Report</p>
					</a>
				</li>
				<li class="nav-item">
					<a href="/mapped-college-number" class="nav-link">
					<i class='bx bx-headphone bx-flashing' ></i>
						<p>Mapped College Number</p>
					</a>
				</li>
				<li class="nav-item">
					<a href="/logout" class="nav-link">
						<i class='bx bxs-user-voice bx-burst' ></i>
						<p>Logout</p>
					</a>
				</li>
			</ul>
		</nav>`);
		
		} 
	
		if(usersData.role=="Admin") {
			$("#sideBar").html(`<nav class="mt-2">
			<ul
				class="nav nav-pills nav-sidebar flex-column"
				data-widget="treeview"
				role="menu"
				data-accordion="false"
			>
			 <li class="nav-item">
					<a href="/adminreport" class="nav-link">
			<i class='bx bx-recycle bx-fade-right' ></i>
						<p>Salary Report</p>

					</a>
				</li>
		     <li class="nav-item">
					<a href="/admin-pension-report" class="nav-link">
						<i class='bx bxs-report bx-tada' ></i>
						<p>Pension Report</p>

					</a>
				</li>
			
				 <li class="nav-item">
					<a href="/admin-familypension-report" class="nav-link">
				<i class='bx bxs-report bx-flashing' ></i>
						<p>Family Pension Report</p>

					</a>
				</li>
					<li class="nav-item">
					<a href="/admin-guestteacher-report" class="nav-link">
					<i class='bx bxs-report bx-spin' ></i>
						<p>Guest Teacher Report</p>

					</a>
				</li>
				<li class="nav-item">
					<a href="/logout" class="nav-link">
						<i class='bx bxs-user-voice bx-burst' ></i>
						<p>Logout</p>
					</a>
				</li>
			</ul>
		</nav>`);
		
		} 

//for payment maker admin
		if(usersData.role=="Maker-Login") {
			$("#sideBar").html(`<nav class="mt-2">
			<ul
				class="nav nav-pills nav-sidebar flex-column"
				data-widget="treeview"
				role="menu"
				data-accordion="false"
			>

			      <li class="nav-item">
					<a href="/makerdashboard" class="nav-link">
						<i class='bx bxl-microsoft bx-burst' ></i>
						<p>Maker Dashboard</p>
					</a>
				</li>
				      <li class="nav-item">
					<a href="/maker-salary-upload" class="nav-link">
				<i class='bx bxl-react bx-spin' ></i>
						<p>Salary Bulk Upload</p>
					</a>
				</li>
		      <li class="nav-item">
					<a href="/makerview" class="nav-link">
		             <i class='bx bxl-paypal bx-burst' ></i>
						<p>Make Salary Payment Request</p>
					</a>
				</li>
               <li class="nav-item">
					<a href="#" class="nav-link">
		           <i class='bx bx-rupee bx-flashing' ></i>
						<p>Make Pension Payment Request</p>
					</a>
				</li>
                  <li class="nav-item">
					<a href="#" class="nav-link">
		           <i class='bx bxs-dollar-circle bx-fade-left' ></i>
						<p>Make Family-Pension Payment Request</p>
					</a>
				</li>
				  <li class="nav-item">
					<a href="#" class="nav-link">
		           <i class='bx bxl-mastercard bx-tada' ></i>
						<p>Make Guest-Teacher Payment Request</p>
					</a>
				</li>
				<li class="nav-item">
					<a href="/logout" class="nav-link">
						<i class='bx bxs-user-voice bx-burst' ></i>
						<p>Logout</p>
					</a>
				</li>
			</ul>
		</nav>`);
		
		} 


//for payment Checker  admin
if(usersData.role=="Checker-Login") {
			$("#sideBar").html(`<nav class="mt-2">
			<ul
				class="nav nav-pills nav-sidebar flex-column"
				data-widget="treeview"
				role="menu"
				data-accordion="false"
			>

			      <li class="nav-item">
					<a href="/checker-dashboard" class="nav-link">
					<i class='bx bxl-tux bx-fade-down' ></i>
						<p>Checker Dashboard</p>
					</a>
				</li>

		      <li class="nav-item">
					<a href="/checkerview" class="nav-link">
						<i class='bx bxl-paypal bx-tada' ></i>
						<p>Verify Payment Request</p>
					</a>
				</li>
				<li class="nav-item">
					<a href="/logout" class="nav-link">
						<i class='bx bxs-user-voice bx-burst' ></i>
						<p>Logout</p>
					</a>
				</li>
			</ul>
		</nav>`);
		
		} 
//for payment approval admin
		if(usersData.role=="Approver-Login") {
			$("#sideBar").html(`<nav class="mt-2">
			<ul
				class="nav nav-pills nav-sidebar flex-column"
				data-widget="treeview"
				role="menu"
				data-accordion="false"
			>

			      <li class="nav-item">
					<a href="/approval-dashboard" class="nav-link">
					<i class='bx bxl-tux bx-fade-down' ></i>
						<p>Approver Dashboard</p>
					</a>
				</li>

		      <li class="nav-item">
					<a href="/approvalview" class="nav-link">
						<i class='bx bxl-paypal bx-tada' ></i>
						<p>Approve Payment Request</p>
					</a>
				</li>
				 <li class="nav-item">
					<a href="/final-payment-report" class="nav-link">
						<i class='bx bxl-paypal bx-tada' ></i>
						<p>Final Payment/Salary Slip</p>
					</a>
				</li>
				<li class="nav-item">
					<a href="/logout" class="nav-link">
						<i class='bx bxs-user-voice bx-burst' ></i>
						<p>Logout</p>
					</a>
				</li>
			</ul>
		</nav>`);
		
		} 
		if(usersData.role=="University-Admin") {
			$("#sideBar").html(`<nav class="mt-2">
			<ul
				class="nav nav-pills nav-sidebar flex-column"
				data-widget="treeview"
				role="menu"
				data-accordion="false"
			>
		<li class="nav-item">
					<a href="/universityadmin" class="nav-link">
					<i class='bx bxs-color bx-spin' ></i>
						<p> Dashboard Report </p>
					</a>
				</li>
				<li class="nav-item">
					<a href="/download-university-data" class="nav-link">
				<i class='bx bx-download bx-flashing' ></i>
						<p> Download Data <img src="/images/new.gif"> </p>
					</a>
				</li>
				<li class="nav-item">
					<a href="#" class="nav-link">
					<i class="nav-icon fas fa-copy"></i>
					<p>
						Pensioner Reports
						<i class="fas fa-angle-left right"></i>
					</p>
					</a>
					<ul class="nav nav-treeview">
						<li class="nav-item">
							<a href="/pension-report" class="nav-link">
								<i class="far fa-circle nav-icon"></i>
								<p>Pensioner Report</p>
							</a>
						</li>
						<li class="nav-item">
							<a href="/family-pension-report" class="nav-link">
								<i class="far fa-circle nav-icon"></i>
								<p>Family Pensioner Report</p>
							</a>
						</li>
						
						<li class="nav-item">
							<a href="/guest-teacher-report" class="nav-link">
								<i class="far fa-circle nav-icon"></i>
								<p>Guest Teacher Report</p>
							</a>
						</li>
					</ul>
				</li>
				<li class="nav-item">
					<a href="#" class="nav-link">
					<i class="nav-icon fas fa-copy"></i>
					<p>
						Edit Pensioner Details 
						<i class="fas fa-angle-left right"></i>
					</p>
					</a>
					<ul class="nav nav-treeview">
						<li class="nav-item">
							<a href="/pension-edit" class="nav-link">
								<i class="far fa-circle nav-icon"></i>
								<p>Edit Pensioner Details</p>
							</a>
						</li>
						<li class="nav-item">
							<a href="/family-pension-edit" class="nav-link">
								<i class="far fa-circle nav-icon"></i>
								<p>Edit Family Pensioner Details</p>
							</a>
						</li>
						
						<li class="nav-item">
							<a href="/guest-teacher-edit" class="nav-link">
								<i class="far fa-circle nav-icon"></i>
								<p>Edit Guest Teacher Details </p>
							</a>
						</li>
					</ul>
				</li>
				<li class="nav-item">
					<a href="#" class="nav-link">
					<i class="nav-icon fas fa-copy"></i>
					<p>
						Delete Employee Data
						<i class="fas fa-angle-left right"></i>
					</p>
					</a>
					<ul class="nav nav-treeview">
						<li class="nav-item">
					<a href="/remove-employee-data" class="nav-link">
					<i class='bx bxs-message-alt-x bx-tada' ></i>
						<p>Remove Salary Data</p>
					 </a>
				       </li>
						<li class="nav-item">
							<a href="/remove-pension-data" class="nav-link">
							<i class='bx bxs-message-rounded-x bx-burst' ></i>
								<p>Remove Pension Data</p>
							</a>
						</li>
						<li class="nav-item">
							<a href="/remove-familypension-data" class="nav-link">
							<i class='bx bxs-message-rounded-x bx-burst' ></i>
								<p>Remove Family-Pension Data</p>
							</a>
						</li>
						<li class="nav-item">
							<a href="/remove-guestteacher-data" class="nav-link">
							<i class='bx bxs-message-rounded-x bx-burst' ></i>
								<p>Remove Guest-Teacher Data</p>
							</a>
						</li>
					</ul>
				</li>
				<li class="nav-item">
					<a href="/editdetails" class="nav-link">
					<i class='bx bxs-edit bx-tada' ></i>
						<p>New Register/Edit Salary Details</p>
					</a>
				</li>
				<li class="nav-item">
					<a href="/upload-salary-data" class="nav-link">
				      <i class='bx bx-upload bx-flashing' ></i>
						<p>Upload Salary Data <img src="/images/animated-new.gif"></p>
					</a>
				</li>
				<li class="nav-item">
					<a href="/universitypension" class="nav-link">
				<i class='bx bx-plus-medical bx-spin' ></i>
						<p>Pensioner Filling</p>
					</a>
				</li>
				<li class="nav-item">
					<a href="/bulkuploadpension" class="nav-link">
				<i class='bx bxs-cloud-upload bx-flashing' ></i>
						<p> Bulk Upload Pensioner Data</p>
					</a>
				</li>
				<li class="nav-item">
					<a href="/familypensionindividual" class="nav-link">
				<i class='bx bx-plus-medical bx-spin' ></i>
						<p>Family Pensioner Filling</p>
					</a>
				</li>
				<li class="nav-item">
					<a href="/familypension" class="nav-link">
				<i class='bx bxs-cloud-upload bx-flashing' ></i>
						<p>Bulk Upload Family Pensioner Data</p>
					</a>
				</li>
					<li class="nav-item">
					<a href="/guestteacherindividual" class="nav-link">
				<i class='bx bxs-arrow-from-bottom bx-burst' ></i>
						<p> Guest Teacher Filling </p>
					</a>
				</li>
					<li class="nav-item">
					<a href="/guestteacher" class="nav-link">
			       <i class='bx bx-cloud-upload bx-burst' ></i>
						<p> Bulk Upload Guest Teacher Data </p>
					</a>
				</li>
					<li class="nav-item">
					<a href="/logout" class="nav-link">
						<i class='bx bxs-user-voice bx-burst' ></i>
						<p>Logout</p>
					</a>
				</li>
			</ul>
		</nav>`);
		
		} 
		
	}
</script>