<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>
        <%= title %>
    </title>
    <!-- Google Font: Source Sans Pro -->
    <link rel="stylesheet"
        href="https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,400i,700&display=fallback" />
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css" />
    <!-- Ionicons -->
    <link rel="stylesheet" href="https://code.ionicframework.com/ionicons/2.0.1/css/ionicons.min.css" />
    <!-- Tempusdominus Bootstrap 4 -->
    <link rel="stylesheet"
        href="https://cdnjs.cloudflare.com/ajax/libs/tempusdominus-bootstrap-4/5.39.0/css/tempusdominus-bootstrap-4.min.css" />
    <!-- Theme style -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/admin-lte/3.1.0/css/adminlte.min.css" />

    <!-- overlayScrollbars -->
    <link rel="stylesheet"
        href="https://cdnjs.cloudflare.com/ajax/libs/overlayscrollbars/1.13.0/css/OverlayScrollbars.min.css" />

    <!-- Baoxicon -->
    <link href='https://unpkg.com/boxicons@2.1.4/css/boxicons.min.css' rel='stylesheet'>
    <style type="text/css">
        #button1 {
            --border-radius: 5px;
            --border-width: 8px;
            appearance: none;
            position: relative;
            padding: 1em 2em;
            border: 0;
            background-color: #212121;
            font-family: "Roboto", Arial, "Segoe UI", sans-serif;
            font-size: 18px;
            font-weight: 500;
            color: #fff;
            z-index: 2;
        }

        #button1::after {
            --m-i: linear-gradient(#000, #000);
            --m-o: content-box, padding-box;
            content: "";
            position: absolute;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            padding: var(--border-width);
            border-radius: var(--border-radius);
            background-image: conic-gradient(#488cfb,
                    #29dbbc,
                    #ddf505,
                    #ff9f0e,
                    #e440bb,
                    #655adc,
                    #488cfb);
            -webkit-mask-image: var(--m-i), var(--m-i);
            mask-image: var(--m-i), var(--m-i);
            -webkit-mask-origin: var(--m-o);
            mask-origin: var(--m-o);
            -webkit-mask-clip: var(--m-o);
            mask-composite: exclude;
            -webkit-mask-composite: destination-out;
            filter: hue-rotate(0);
            animation: rotate-hue linear 500ms infinite;
            animation-play-state: paused;
        }

        #button1:hover::after {
            animation-play-state: running;
        }

        @keyframes rotate-hue {
            to {
                filter: hue-rotate(1turn);
            }
        }

        #button1,
        button1::after {
            box-sizing: border-box;
        }

        #button1:active {
            --border-width: 5px;
        }

        #downloadExcel {
            color: white;
            text-decoration: none;
            padding: 10px;
        }

        #viewInstruction {
            color: white;
            text-decoration: none;
            padding: 10px;
        }

        #page {
            display: none;
            /* Hide loader by default */
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(255, 255, 255, 0.8);
            justify-content: center;
            align-items: center;
            z-index: 1000;
        }

        #container {
            display: flex;
            justify-content: center;
            align-items: center;
            position: relative;
        }

        #h3 {
            color: rgb(82, 79, 79);
        }

        #ring {
            width: 190px;
            height: 190px;
            border: 1px solid transparent;
            border-radius: 50%;
            position: absolute;
        }

        #ring:nth-child(1) {
            border-bottom: 8px solid rgb(240, 42, 230);
            animation: rotate1 2s linear infinite;
        }

        @keyframes rotate1 {
            from {
                transform: rotateX(50deg) rotateZ(110deg);
            }

            to {
                transform: rotateX(50deg) rotateZ(470deg);
            }
        }

        #ring:nth-child(2) {
            border-bottom: 8px solid rgb(240, 19, 67);
            animation: rotate2 2s linear infinite;
        }

        @keyframes rotate2 {
            from {
                transform: rotateX(20deg) rotateY(50deg) rotateZ(20deg);
            }

            to {
                transform: rotateX(20deg) rotateY(50deg) rotateZ(380deg);
            }
        }

        #ring:nth-child(3) {
            border-bottom: 8px solid rgb(3, 170, 170);
            animation: rotate3 2s linear infinite;
        }

        @keyframes rotate3 {
            from {
                transform: rotateX(40deg) rotateY(130deg) rotateZ(450deg);
            }

            to {
                transform: rotateX(40deg) rotateY(130deg) rotateZ(90deg);
            }
        }

        #ring:nth-child(4) {
            border-bottom: 8px solid rgb(207, 135, 1);
            animation: rotate4 2s linear infinite;
        }

        @keyframes rotate4 {
            from {
                transform: rotateX(70deg) rotateZ(270deg);
            }

            to {
                transform: rotateX(70deg) rotateZ(630deg);
            }
        }


        #loader-background {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 999;
        }

        .seat {
            width: 50px;
        }

        input::-webkit-outer-spin-button,
        input::-webkit-inner-spin-button {
            -webkit-appearance: none;
            margin: 0;
        }

        .card-title {
            font-weight: bold !important;
        }

        em {
            color: red;
        }

        .custom-checkbox {
            display: inline-block;
            width: 25px;
            height: 25px;
            background-color: white;
            border: 2px solid #ddd;
            border-radius: 7px;
            margin-right: 15px;
            vertical-align: middle;
            position: relative;
        }

        .form-check-input:checked+.form-check-label .custom-checkbox {
            background-color: blue;

        }

        .form-check-input:checked+.form-check-label .custom-checkbox::after {
            content: '';
            position: absolute;
            top: 5px;
            left: 9px;
            width: 6px;
            height: 12px;
            border: solid white;
            border-width: 0 2px 2px 0;
            transform: rotate(45deg);
        }

        .form-check-label {
            cursor: pointer;
        }

        .is-invalid {
            border-color: red;
        }

        /* From Uiverse.io by Yaya12085 */
        .form {
            background-color: #fff;
            box-shadow: 0 10px 60px rgb(218, 229, 255);
            border: 1px solid rgb(159, 159, 160);
            border-radius: 20px;
            padding: 2rem .7rem .7rem .7rem;
            text-align: center;
            font-size: 1.125rem;
            /* max-width: 320px; */
        }

        .form-title {
            color: #000000;
            font-size: 1.5rem;
            font-weight: 500;
        }

        .form-paragraph {
            margin-top: 10px;
            font-size: 0.9375rem;
            color: rgb(105, 105, 105);
        }

        .drop-container {
            background-color: #fff;
            position: relative;
            display: flex;
            gap: 10px;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            padding: 10px;
            margin-top: 2.1875rem;
            border-radius: 10px;
            border: 2px dashed rgb(171, 202, 255);
            color: #444;
            cursor: pointer;
            transition: background .2s ease-in-out, border .2s ease-in-out;
        }

        .drop-container:hover {
            background: rgba(0, 140, 255, 0.164);
            border-color: rgba(17, 17, 17, 0.616);
        }

        .drop-container:hover .drop-title {
            color: #222;
        }

        .drop-title {
            color: #444;
            font-size: 20px;
            font-weight: bold;
            text-align: center;
            transition: color .2s ease-in-out;
        }

        #file-input {
            width: 350px;
            max-width: 100%;
            color: #444;
            padding: 2px;
            background: #fff;
            border-radius: 10px;
            border: 1px solid rgba(8, 8, 8, 0.288);
        }

        #file-input::file-selector-button {
            margin-right: 20px;
            border: none;
            background: #084cdf;
            padding: 10px 20px;
            border-radius: 10px;
            color: #fff;
            cursor: pointer;
            transition: background .2s ease-in-out;
        }

        #file-input::file-selector-button:hover {
            background: #0d45a5;
        }
    </style>
</head>

<body class="hold-transition sidebar-mini layout-fixed sidebar-collapse">
    <div class="wrapper">
        <!-- Preloader -->
        <div class="preloader flex-column justify-content-center align-items-center">
            <img class="animation__shake" src="/images/loader.gif" alt="CodeBucket" height="100" width="100" />
        </div>

        <!-- Navbar -->
        <%- include('../partials/header'); %>
            <!-- /.navbar -->

            <!-- Main Sidebar Container -->
            <%- include('../partials/sidebar'); %>

                <!-- Content Wrapper. Contains page content -->
                <div class="content-wrapper">
                    <!-- Content Header (Page header) -->
                    <div class="content-header">
                        <div class="container-fluid">
                            <div class="row mb-2">
                                <div class="col-sm-6">
                                    <h1 class="m-0" style="color:black; font-size: xx-large; font-weight: bolder;">Payment Maker Salary Upload Dashboard:
                                    </h1>
                                </div>
                                <!-- /.col -->
                                <div class="col-sm-6">
                                    <ol class="breadcrumb float-sm-right">
                                        <li class="breadcrumb-item"><a href="/logout">Signout</a></li>

                                    </ol>
                                </div>
                                <!-- /.col -->
                            </div>
                            <!-- /.row -->
                        </div>
                        <!-- /.container-fluid -->
                    </div>

                    <section class="content">
                        <div class="container-fluid">
                            <div class="row">
                                <section class="col-12">
                                    <!-- <form id="frmregister" name="frmregister" action="" method="post"> -->
                                    <div class="card card-success">
                                        <!-- /.card-header -->
                                        <div class="card-header">
                                            <h3 class="card-title">Payment Maker Salary Report</h3>
                                        </div>
                                        <div class="card-body">
                                            <div class="row">
                                                <div class="col-12">

                                                    <div class="card card-primary">

                                                        <div class="card-body">
                                                            <form id="frmsearch" name="frmsearch" action=""
                                                                method="post" class="form">
                                                                <span class="form-title">Please Download <span
                                                                        style="color: red; font-weight: bold;">Salary
                                                                        Details Of Employees and Make The Necessary
                                                                        Changes And Verify All Details Of the Employee
                                                                    </span><span
                                                                        style="color: green; font-weight: bolder;"> and
                                                                        Re-Upload The Excel File Of Employees On the
                                                                        Portal.</span> Ensure Excel Format Is
                                                                    Same As Below and All Mandatory Fields Must be
                                                                    Filled
                                                                    Before Uploading.</span> <br>
                                                                <span class="form-title" style="color: red;">Please Read
                                                                    ,View and Follow the Instructions Before Uploading
                                                                    Any File .</span>
                                                                   
                                                                 <div>  
                                                                    <button type="button" class="btn btn-success mt-3"
                                                                  >
                                                                    <a id="viewInstruction"
                                                                       href="file/Makerinstruction.xlsx" download>
                                                                        View Instruction
                                                                    </a>
                                                                </button> </div>
                                                                <div style="text-align: center;">
                                                                    <label for="monthYearDropdown1"
                                                                        class="form-title">Select the Attendance/Absentee  Month  For Downloading the Salary Data File
                                                                       :</label>
                                                                    <select id="monthYearDropdown1" class="form-control "
                                                                        style="width: 50%; margin: 0 auto;" required>
                                                                        <option value="">Select a Absentee month For Download the Salary File</option>                                                                    </select>
                                                                </div>
                                                                <div>
                                                                    <p class="form-paragraph"
                                                                        style="color: rgb(4, 0, 255); font-size: 40px;">
                                                                        Download Salary Data Files Here:
                                                                    </p>
                                                                    <button type="button" id="button1" style="display: none;">
                                                                        <a id="downloadExcel" href="" target="_blank">
                                                                            Download Excel File
                                                                        </a>
                                                                    </button>
                                                                  
                                                                </div>

                                                                <!-- Centered Month Dropdown -->
                                                                <div style="text-align: center;">
                                                                    <label for="monthYearDropdown"
                                                                        class="form-title">Choose the Month For Which Salary Making Is In Process
                                                                       :</label>
                                                                    <select id="monthYearDropdown" class="form-control "
                                                                        style="width: 50%; margin: 0 auto;" required>
                                                                        <option value="">Select a month For Salary Process</option>

                                                                    </select>
                                                                </div>
                                                                <p class="form-paragraph" style="color: red;">
                                                                    File should be an Excel
                                                                </p>
                                                                <label for="file-input" class="drop-container">
                                                                    <span class="drop-title"
                                                                        style="color: red; font-size: 20px; font-weight: bold;">Upload
                                                                      Processed Monthly Salary files here After Verifying All Details:</span>
                                                                    or
                                                                    <input type="file" accept=".xlsx, .xls" required=""
                                                                        id="file-input">
                                                                </label>
                                                            </form>
                                                        </div>
                                                    </div>
                                                    <!-- </form> -->
                                                </div>
                                            </div>
                                        </div>
                                        <div class="card-footer">

                                            <button type="button" id="btnsubmit"
                                                class="btn btn-danger float-right mr-1">
                                                Submit Salary File
                                            </button>
                                        </div>
                                    </div>
                                    <div id="loader-background"></div>
                                    <div id="page">
                                        <div id="container">
                                            <div id="ring"></div>
                                            <div id="ring"></div>
                                            <div id="ring"></div>
                                            <div id="ring"></div>
                                            <div id="h3" style="color: rgb(25, 0, 255);">Uploading File</div>
                                        </div>
                                    </div>
                                </section>
                            </div>
                            <!-- Main row -->
                        </div>
                        <!-- /.container-fluid -->
                    </section>
                    <!-- /.content -->
                </div>

                <%- include('../partials/footer'); %>
                    <!-- Control Sidebar -->
                    <aside class="control-sidebar control-sidebar-dark">
                        <!-- Control sidebar content goes here -->
                    </aside>
                    <!-- /.control-sidebar -->
    </div>
    <!-- ./wrapper -->
    <script>
        $.widget.bridge("uibutton", $.ui.button);
    </script>

    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- jQuery UI -->
    <script src="https://code.jquery.com/ui/1.12.1/jquery-ui.min.js"></script>
    <!-- Bootstrap 4 -->
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.bundle.min.js"></script>
    <!-- Select2 -->
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>

    <!-- Moment.js -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.1/moment.min.js"></script>

    <!-- Date Range Picker -->
    <script src="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.min.js"></script>

    <!-- Tempusdominus Bootstrap 4 -->
    <script
        src="https://cdnjs.cloudflare.com/ajax/libs/tempusdominus-bootstrap-4/5.1.2/js/tempusdominus-bootstrap-4.min.js"></script>

    <!-- OverlayScrollbars -->
    <script
        src="https://cdnjs.cloudflare.com/ajax/libs/overlayscrollbars/1.13.0/js/jquery.overlayScrollbars.min.js"></script>

    <!-- AdminLTE App -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/admin-lte/3.2.0/js/adminlte.min.js"></script>

    <!-- jQuery Validation -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-validate/1.19.3/jquery.validate.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-validate/1.19.3/additional-methods.min.js"></script>

    <!-- Toastr -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/js/toastr.min.js"></script>
    <!-- SweetAlert2 -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

    <script src="https://unpkg.com/boxicons@2.1.4/dist/boxicons.js"></script>
   
<script>
    $(document).ready(function () {
        const apiBaseUrl = "<%= process.env.apiBaseUrl %>";
        
        // Attach the change event handler to the dropdown
        $('#monthYearDropdown1').on('change', function () {
            const selectedMonth = $(this).val();
            const token = localStorage.getItem("access-token-new");
            
            if (selectedMonth) {
                fetchCollegeFile(selectedMonth, token);
            }
        });

        function fetchCollegeFile(monthYear, token) {
            $.ajax({
                url: apiBaseUrl + "v1/maker/excel-sallary-data",
                type: 'POST',
                headers: {
                    "token": token,
                },
                data: JSON.stringify({ date: monthYear }), 
                contentType: 'application/json',
                success: function (response) {
                    if (response.status === "success" && response.data && response.data.fileUrl) {
                            const fileUrl = response.data.fileUrl.fileUrl;
                            $('#button1').show();
                            $('#downloadExcel').attr('href', fileUrl);

                            $('#downloadExcel').text("Download Excel File (File Ready)");

                            $('#button1').click(function (e) {
                              e.preventDefault();
                               window.open(fileUrl, '_blank'); 
                        });
                    }
                         else {
                        console.warn("Response is not in expected format: ", response);
                    }
                },
                error: function (error) {
                    console.error('Error fetching college file:', error);
                }
            });
        }
    });
</script>
    <script>

        $(document).ready(function () {
            const apiBaseUrl = "<%= process.env.apiBaseUrl %>";
            $.ajax({

                url: apiBaseUrl + "v1/absentee/get-month",
                type: 'get',
                dataType: 'json',
                headers: {
                    token: localStorage.getItem("access-token-new"),
                    "Content-Type": "application/json"
                },
                success: function (response) {
                    if (response.status === "success") {

                        var months = response.data;

                        $.each(months, function (index, monthData) {
                            var monthYearText = monthData.month_name + " " + monthData.year;

                            $('<option>', {
                              value: monthYearText,
                              text: monthYearText
                        }).appendTo('#monthYearDropdown, #monthYearDropdown1');

                        });
                    } else {
                        console.error('API request failed with status:', response.status);
                    }
                },
                error: function (xhr, status, error) {
                    console.error('Error fetching data from API:', error);
                }
            });

        })

    </script>

    <script>
        var apiBaseUrl = "<%= process.env.apiBaseUrl %>";
        const token = localStorage.getItem("access-token-new");
        var userDetails = localStorage.getItem('user-data');
        var userDetailsObj = JSON.parse(userDetails);
        var departmentUsername = userDetailsObj.department_username
        console.log(departmentUsername)
        var universityName = userDetailsObj.university
        $('#university1').append(universityName);
        $('#college1').append(universityName);
        console.log(universityName)
        $(document).ready(function () {

            function showLoader() {
                $('#page').css('display', 'flex');
                $('#loader-background').show();
            }
            function hideLoader() {
                $('#page').hide();
                $('#loader-background').hide();
            }
            $('#btnsubmit').click(function () {
                showLoader();
                var date = $('#monthYearDropdown').val();
                var fileInput = $('#file-input')[0];
                 // Check if date is selected
                if (!date) {
                hideLoader();
                    Swal.fire({
                    icon: 'error',
                    title: 'Please select Attendance month',
                    text: 'Please select absentee month before submitting.'
                    });
                     return; 
                    }

                if (fileInput.files.length === 0) {
                    hideLoader();
                    Swal.fire({

                        icon: 'error',
                        title: 'Error',
                        text: 'Please select a file before submitting.'
                    });
                    return;
                }

                var file = fileInput.files[0];
                var fileType = file.type;
                var validTypes = ['application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', 'application/vnd.ms-excel'];

                if (!validTypes.includes(fileType)) {
                    hideLoader();
                    Swal.fire({
                        icon: 'error',
                        title: 'Error',
                        text: 'Only Excel files are allowed.'
                    });
                    return;
                }

                var formData = new FormData();
                formData.append('filePaths', file);
                formData.append('updatedBy', universityName);
                formData.append('date', date);
                formData.append('submittedBy', departmentUsername);
                $.ajax({
                    url: apiBaseUrl + "v1/maker/save-excel-maker-data",
                    // url:"http://192.168.137.86:5000/v1/absentee/insert-excel-absentee",
                    type: 'POST',
                    data: formData,
                    headers: {
                        "token": localStorage.getItem("access-token-new"),

                    },
                    contentType: false,
                    processData: false,

                    success: function (response) {
                        hideLoader();
                        Swal.fire({
                            icon: 'success',
                            title: 'Payment Maker Excel File Is Finally Submitted Successfully',
                            text: "Please Wait For Approval And Details Verified By Payment Checker!",
                            confirmButtonText: 'Okay'
                        });
                    },
                    error: function (xhr, status, error) {
                        hideLoader();
                        let errorMessage = 'An error occurred. Please try again.';
                        if (xhr.responseJSON && xhr.responseJSON.message) {
                            errorMessage = xhr.responseJSON.message;
                        }
                        Swal.fire({
                            icon: 'error',
                            title: 'Your Excel File does Not Conatins all Mandatory  Fields And Some Other Issue',
                            text: errorMessage,
                            confirmButtonText: 'OK'
                        });
                    }
                });
            });
        });

    </script>
   <script>
    function isTokenExpired(token) {
        if (!token) return true;
        try {
            // Decode JWT payload
            const payloadBase64 = token.split('.')[1];
            const payload = JSON.parse(atob(payloadBase64));
            // Check if current time is past the expiration time
            const isExpired = payload.exp * 1000 < Date.now();
            return isExpired;
        } catch (error) {
            console.error("Failed to decode token:", error);
            return true; 
        }
    }
    // Check token expiration on load
    $(function () {
        const token = localStorage.getItem("access-token-new");
        const userData = JSON.parse(userDetails);
        if (token && userData) {
            // Check role and token expiration
            if (usersData.role !== "Maker-Login" || isTokenExpired(token)) {
                alert("Session expired or unauthorized access. Redirecting to login.");
                window.location = "/";
            } else {
                setInterval(() => {
                    if (isTokenExpired(token)) {
                        alert("Session expired. Please login again.");
                        window.location = "/";
                    }
                }, 60000);
            }
        } else {
            window.location = "/";
        }
    });
                    </script> 