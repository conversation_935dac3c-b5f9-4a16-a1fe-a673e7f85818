<!DOCTYPE html>
<html lang="en">

<head>
	<meta charset="UTF-8" />
	<meta http-equiv="X-UA-Compatible" content="IE=edge" />
	<meta name="viewport" content="width=device-width, initial-scale=1.0" />
	<title>
		<%= title %>
	</title>
	<!-- Google Font: Source Sans Pro -->
	<link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,400i,700&display=fallback" />

	<!-- Font Awesome -->
	<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css" />

	<!-- Ionicons -->
	<link rel="stylesheet" href="https://code.ionicframework.com/ionicons/2.0.1/css/ionicons.min.css" />


	<!-- Tempusdominus Bootstrap 4 -->
	<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/tempusdominus-bootstrap-4/5.39.0/css/tempusdominus-bootstrap-4.min.css" />

	<!-- iCheck -->
	<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/icheck-bootstrap/3.0.1/icheck-bootstrap.min.css" />

	<!-- Select2 -->
	<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
	<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/select2@4.0.13/dist/css/select2.min.css" />
	
	<!-- Theme style -->
	<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/admin-lte/3.1.0/css/adminlte.min.css" />

	<!-- overlayScrollbars -->
	<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/overlayscrollbars/1.13.0/css/OverlayScrollbars.min.css" />

	<!-- Daterange picker -->
	<link rel="stylesheet" href="https://cdn.datatables.net/1.10.24/css/dataTables.bootstrap4.min.css">


	<!-- DataTables -->
	<link rel="stylesheet" href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap4.min.css">

	<link rel="stylesheet" href="https://cdn.datatables.net/responsive/2.2.9/css/responsive.bootstrap4.min.css">

	<link rel="stylesheet" href="https://cdn.datatables.net/buttons/2.0.1/css/buttons.bootstrap4.min.css">
<!-- Baoxicon -->
	<link href='https://unpkg.com/boxicons@2.1.4/css/boxicons.min.css' rel='stylesheet'>
	<style type="text/css">
		.seat {
			width: 50px;
		}

		input::-webkit-outer-spin-button,
		input::-webkit-inner-spin-button {
			-webkit-appearance: none;
			margin: 0;
		}
        .card-title{
            font-weight: bold !important;
        }
        em{
            color: red;
        }
		.custom-checkbox {
        display: inline-block;
        width: 25px;
        height: 25px;
        background-color: white;
        border: 2px solid #ddd;
        border-radius: 7px;
        margin-right: 15px;
        vertical-align: middle;
        position: relative;
    }

    .form-check-input:checked + .form-check-label .custom-checkbox {
        background-color: blue;
      
    }

    .form-check-input:checked + .form-check-label .custom-checkbox::after {
        content: '';
        position: absolute;
        top: 5px;
        left: 9px;
        width: 6px;
        height: 12px;
        border: solid white;
        border-width: 0 2px 2px 0;
        transform: rotate(45deg);
    }

    .form-check-label {
        cursor: pointer;
    }
	.is-invalid {
    border-color: red;
}

	</style>
</head>

<body class="hold-transition sidebar-mini layout-fixed sidebar-collapse">
	<div class="wrapper">
		<!-- Preloader -->
		<div class="preloader flex-column justify-content-center align-items-center">
			<img class="animation__shake" src="/images/loader.gif" alt="CodeBucket" height="100"
				width="100" />
		</div>

		<!-- Navbar -->
		<%- include('../partials/header'); %>
			<!-- /.navbar -->

			<!-- Main Sidebar Container -->
			<%- include('../partials/sidebar'); %>

				<!-- Content Wrapper. Contains page content -->
				<div class="content-wrapper">
					<!-- Content Header (Page header) -->
					<div class="content-header">
						<div class="container-fluid">
							<div class="row mb-2">
								<div class="col-sm-6">
									<h1 class="m-0" style="color: red; font-size: xx-large;">Register & Edit Employee Details By University Admin</h1>
								</div>
								<!-- /.col -->
								<div class="col-sm-6">
									<ol class="breadcrumb float-sm-right">
										<li class="breadcrumb-item"><a href="/logout">Signout</a></li>
									
									</ol>
								</div>
								<!-- /.col -->
							</div>
							<!-- /.row -->
						</div>
						<!-- /.container-fluid -->
					</div>

					<section class="content">
						<div class="container-fluid">
							<div class="row">
								<section class="col-12">
									<!-- <form id="frmregister" name="frmregister" action="" method="post"> -->
										<div class="card card-primary">
											<!-- /.card-header -->
                                            <div class="card-header">
                                                <h3 class="card-title">Employee Personal Details</h3>
                                            </div>
											<div class="card-body">
												<div class="row">
													<div class="col-12">
														<!-- <form id="frmsearch" name="frmsearch" action="" method="post"> -->
															<div class="card card-primary">
																
																<div class="card-body">
																	<div class="form-group d-flex align-items-center">
																		<label for="id" class="mr-2">Payee ID</label>
																		<input type="text" class="form-control mr-2" name="id" id="id" onkeyup="this.value = this.value.toUpperCase();"  placeholder="Enter Payee ID For Search" style="flex: 1">
																	<button type="button" id="btnsearch" class="btn btn-danger float-right mr-1">Search By PayID</button>
																</div>
															</div>
															</div>
														<!-- </form> -->
													</div>
													<form id="frmregister" name="frmregister" action="" method="post">
														<div class="card-body">
															<div class="row">
													<div class="col-md-6">
														<div class="form-group">
															<label>Prefix</label>
															<select class="form-control select2" name="prefix"
																id="prefix" style="width: 100%" required>
                                                                <option value="Mr.">Mr.</option>
                                                                <option value="Ms." >Ms.</option>
                                                                <option value="Mrs.">Mrs.</option>
                                                                <option value="Dr.">Dr.</option>
                                                                <option value="Prof.">Prof.</option>
																<option value="Shri">Shri</option>
                                                            </select>
														</div>
													</div>
                                                    <div class="col-md-6">
														<div class="form-group">
															<label>First Name<em>*</em></label>
															<input type="text" class="form-control" name="firstName"
																id="firstName" placeholder="Enter First Name" 	onkeyup="this.value = this.value.toUpperCase();"  required />
														</div>
													</div>
                                                    <div class="col-md-6">
														<div class="form-group">
															<label>Middle Name</label>
															<input type="text" class="form-control" name="middleName"
																id="middleName" placeholder="Enter Middle Name" 	onkeyup="this.value = this.value.toUpperCase();" />
														</div>
													</div>
                                                    <div class="col-md-6">
														<div class="form-group">
															<label>Last Name</label>
															<input type="text" class="form-control" name="lastName"
																id="lastName" placeholder="Enter Last Name" 	onkeyup="this.value = this.value.toUpperCase();"  required />
														</div>
													</div>
                                                    <div class="col-md-6">
														<div class="form-group">
															<label>Gender<em>*</em></label>
															<select class="form-control select2" name="gender"
																id="gender" style="width: 100%"  required>
																<option value="">Select Gender</option>
                                                                <option value="MALE">MALE</option>
                                                                <option value="FEMALE">FEMALE</option>
                                                                <option value="Other">Other</option>
                                                            </select>
														</div>
													</div>
													<div class="col-md-6">
														<div class="form-group">
															<label>Date of Birth<em>*</em></label>
															<input type="text"class="form-control" id="dateOfBirth" name="dateOfBirth" placeholder="DD/MM/YYYY" required>
														</div>
													</div>
													
                                                    <div class="col-md-6">
														<div class="form-group">
															<label>Father's Name<em>*</em></label>
															<input type="text" class="form-control" name="fatherName"
																id="fatherName" placeholder="Enter Father's Name" 	onkeyup="this.value = this.value.toUpperCase();" required />
														</div>
													</div>
                                                    <div class="col-md-6">
														<div class="form-group">
															<label>Mother's Name<em>*</em></label>
															<input type="text" class="form-control" name="motherName"
																id="motherName" placeholder="Enter Mother's Name"  	onkeyup="this.value = this.value.toUpperCase();" required />
														</div>
													</div>
													<!-- <div class="col-md-6">
														<div class="form-group">
															<label>Spouse Name (Husband/Wife)</label>
															<input type="text" class="form-control" name="spouseName"
																id="spouseName" placeholder="Enter Spouse Name" 	onkeyup="this.value = this.value.toUpperCase();"  />
														</div>
													</div> -->
                                                    <div class="col-md-6">
														<div class="form-group">
															<label>Personal Mobile Number<em>*</em></label>
															<!-- <input type="number" class="form-control" name="personalMobileNumber"
																id="personalMobileNumber" placeholder="Enter Mobile Number"  minlength="10"   maxlength="10" required  /> -->
																<input
															
																class="form-control"
																autocomplete="off"
																type="number"
																data-val="true"
																data-val-length="Invalid Mobile Number"
																data-val-length-max="10"
																data-val-length-min="10"
																data-val-regex="Entered Mobile Number format is not valid."
																data-val-regex-pattern="^\(?([6-9]{1})\)?[-. ]?([0-9]{5})[-. ]?([0-9]{4})$"
																data-val-required="Required"
																id="personalMobileNumber"
																maxlength="10"
																name="personalMobileNumber"
																value=""
																required
															  />
															  <span
																class="text-danger field-validation-valid"
																data-valmsg-for="personalMobileNumber"
																data-valmsg-replace="true"
															  ></span>
														</div>
													</div>
                                                    <div class="col-md-6">
														<div class="form-group">
															<label>Personal Email<em>*</em></label>
															<input type="email" class="form-control" name="personalEmail"
																id="personalEmail" placeholder="Enter Email"  required />
														</div>
													</div>
													<div class="col-md-6">
														<div class="form-group">
															<label>Effective Date of Joining <span style="color: red;">(Please Enter Date in only this Format (DD/MM/YYYY)EX:(08/03/2002))<em>*</em></span></label>
															<input type="text" class="form-control" id="effectiveDateOfJoining" name="effectiveDateOfJoining" placeholder="DD/MM/YYYY" required>
														</div>
													</div>
												
													<div class="col-md-6" id="pfAccountNoContainer">
														<div class="form-group">
															<label>PF Account No</label>
															<input type="text" class="form-control" name="pfAccountNo" id="pfAccountNo" placeholder="Enter PF Account Number" />
														</div>
													</div>
													<div class="col-md-6"  id="pfIfscCodeContainer" >
														<div class="form-group">
															<label>PF Account IFSC Code</label>
															<input type="text" class="form-control" name="pfAccountIfscCode" id="pfAccountIfscCode" placeholder="Enter PF Account Number" />
														</div>
													</div>
													<div class="col-md-6" id="npsOptedContainer"  >
														<div class="form-group">
															<label>NPS Opted<em>*</em></label>
															<select class="form-control select2" name="npsOpted"
																id="npsOpted" style="width: 100%" required >
                                                                <option value="">Choose Status</option>
                                                                <option value="Yes">Yes</option>
                                                                <option value="No">No</option>
                                                            </select>
														</div>
													</div>
													<div class="col-md-6" id="npsCreatedContainer" style="display: none;">
														<div class="form-group">
															<label>NPS Created or not (PRAN NO.)?<em>*</em></label>
															<select class="form-control select2" name="npsCreated"
																id="npsCreated" style="width: 100%"  >
                                                                <option value="">Choose Status</option>
                                                                <option value="Yes">Yes</option>
                                                                <option value="No">No</option>
                                                            </select>
														</div>
													</div>
                                                    <div class="col-md-6" id="pranNoContainer" >
														<div class="form-group">
															<label>PRAN No</label>
															<input type="number" class="form-control" name="pranNo" id="pranNo" placeholder="Enter PRAN" />
														</div>
													</div>
                                                    <div class="col-md-6">
														<div class="form-group">
															<label>PAN No<em>*</em></label>
															<input type="text" class="form-control" name="panNo"
																id="panNo" placeholder="Enter PAN Number" 	onkeyup="this.value = this.value.toUpperCase();"  required/>
														</div>
													</div>
                                                    <div class="col-md-6">
														<div class="form-group">
															<label>Aadhar Card No<em>*</em></label>
															<!-- <input type="number" class="form-control" name="aadharCardNo"
																id="aadharCardNo" placeholder="Enter Aadhar Number" required /> -->
																<input
																maxlength="12"
																class="form-control"
																onkeyup="this.value = this.value.toUpperCase();"
																autocomplete="off"
																type="text"
																data-val="true"
																data-val-regex="Invalid Aadhar number."
																data-val-regex-pattern="^[0-9]{12}$"
																data-val-required="Required"
																id="aadharCardNo"
																name="aadharCardNo"
																value=""
																required
															  />
															  <span
																class="text-danger field-validation-valid"
																data-valmsg-for="aadharCardNo"
																data-valmsg-replace="true"
															  ></span>
														</div>
													</div>
                                                    <div class="col-md-6">
														<div class="form-group">
															<label>Pay ID No.<em>*</em></label>
															<input type="text" class="form-control" name="payIdNo"
																id="payIdNo" placeholder="Enter Pay ID" 	onkeyup="this.value = this.value.toUpperCase();"  />
														</div>
													</div>
												</div>
											</div>
										</div>

                                        <div class="card card-primary">
											<!-- /.card-header -->
                                            <div class="card-header">
                                                <h3 class="card-title">Employee Official Details</h3>
                                            </div>
											<div class="card-body">
												<div class="row">
													<div class="col-md-6">
														<div class="form-group">
															<label>Subject.<em>*</em></label>
															<input type="text" class="form-control" name="subject"
																id="subject" placeholder="Enter Subject Details" 	onkeyup="this.value = this.value.toUpperCase();"  />
														</div>
													</div>
													<div class="col-md-6">
														<div class="form-group">
															<label>Appointment Letter Issue Date<em>*</em></label>
															<input type="text" class="form-control" id="appointmentLetterOrderNoDate" name="appointmentLetterOrderNoDate" placeholder="DD/MM/YYYY" required>
														</div>
													</div>
													
                                                    <div class="col-md-6">
														<div class="form-group">
															<label>Employee Type<em>*</em></label>
															<select class="form-control select2" name="employeeType"
																id="employeeType" style="width: 100%" required>
																<option value="">Select Employee Type</option>
                                                                <option value="Teaching">Teaching</option>
                                                                <option value="Non-Teaching">Non-Teaching</option>
                                                            </select>
														</div>
													</div>
												
													<div class="col-md-6">
														<div class="form-group">
															<label>Designation At the Time of Joining<em>*</em></label>
															<input type="text" class="form-control" id="designationAtJoining" name="designationAtJoining" placeholder="Enter Designation At the time Of Joining" onkeyup="this.value = this.value.toUpperCase();" required>
														</div>
													</div>
													<div class="col-md-6">
														<div class="form-group">
															<label for="currentUniversityName">Current University Name<em>*</em></label>
															<select class="form-control select2" name="currentUniversityName" id="currentUniversityName" onkeyup="this.value = this.value.toUpperCase();" required>
															</select>
															</div>
													</div>
													
                                                    <div class="col-md-6">
														<div class="form-group">
															<label>Currently Posted at University Office/Department/College<em>*</em></label>
															<select class="form-control select2" name="currentlyPostedDetails"
																id="currentlyPostedDetails" placeholder="Enter Currently Posting At" required >
																<option value="">Select Posted Office</option>
                                                                <option value="University Office">University Office</option>
                                                                <option value="Department">Department</option>
																<option value="College">College</option>
														</select>
															</div>
													</div>
													<div class="col-md-6">
														<div class="form-group">
															<label for="currentlyPostedAtUniversityOfficeDepartmentCollege">Current Office/Department/College<em>*</em></label>
															<select class="form-control select2" type="text" name="currentlyPostedAtUniversityOfficeDepartmentCollege" id="currentlyPostedAtUniversityOfficeDepartmentCollege" onkeyup="this.value = this.value.toUpperCase();" required >
														</select>
														</div>
													</div>
													
													<div class="col-md-6">
														<div class="form-group">
															<label for="currentDesignation">Current Designation<em>*</em></label>
															<input class="form-control" type="text" name="currentDesignation" id="currentDesignation" onkeyup="this.value = this.value.toUpperCase();" required >
														</div>
													</div>
													
                                                    <div class="col-md-6">
														<div class="form-group">
															<label>Date of Retirement<em>*</em></label>
															<input type="text" class="form-control" id="dateOfRetirement" name="dateOfRetirement" placeholder="DD/MM/YYYY" required >
														</div>
													</div>
													
                                                    <div class="col-md-6">
														<div class="form-group">
															<label>Govt. Quarter Occupied<em>*</em></label>
															<select class="form-control select2" name="govtQuarterOccupied"
																id="govtQuarterOccupied" style="width: 100%" required>
																<option value="">Select Govt Quarter</option>
                                                                <option value="No">No</option>
                                                                <option value="Yes">Yes</option>
                                                            </select>
														</div>
													</div>  
													<div class="col-md-6">
														<div class="form-group">
															<label>Annual Increment Opted <em>*</em></label>
															<select class="form-control select2" name="annualOptedDate"
																id="annualOptedDate" style="width: 100%" required>
																<option value="">Select Annual Opted Month</option>
                                                                <option value="January">January</option>
                                                                <option value="July">July</option>
                                                            </select>
														</div>
													</div>
													
													<div class="col-md-6">
														<div class="form-group">
															<label>Payment Verification Status(PVC)<em>*</em></label>
															<select class="form-control select2" name="payVerificationStatus" id="payVerificationStatus" style="width: 100%" required>
																<option value="">Choose Status</option>
																<option value="Yes">Yes</option>
																<option value="No">No</option>
															</select>
														</div>
													</div>
													<div class="col-md-6" id="pvcStatusContainer" style="display: none;">
														<div class="form-group">
															<label>In Case Of No PVC Please Choose Reason<em>*</em></label>
															<select class="form-control select2" name="pvcStatus" id="pvcStatus" style="width: 100%">
																<option value="">Choose PVC Status Reason</option>
																<option value="Request Submitted At University But Not forwarded to PVC">Request Submitted At University But Not forwarded to PVC</option>
																<option value="Request Submitted At PVC but not Completed">Request Submitted At PVC but not Completed</option>
																<option value="New Joinee">New Joinee</option>
																<option value="PVC not Applied">PVC not Applied</option>
																<option value="Other Reason">Other Reason</option>
															</select>
														</div>
													</div>
													<div class="col-md-6" id="pvcNoContainer" style="display: none;">
														<div class="form-group">
															<label>PVC No<span style="color: crimson;">(प्राप्ति संख्या )<em>*</em></span></label>
															<input type="text" class="form-control" name="pvcNo" id="pvcNo" placeholder="Enter Enter  PVC No  12 or 13 Digits " />
														</div>
													</div>
												
											</div>
											
										</div>
									<!-- </form> -->
								</section>
								<section class="col-12">
									<!-- <form id="frmregister" name="frmregister" action="" method="post"> -->
										<div class="card card-primary">
											<!-- /.card-header -->
                                            <div class="card-header">
                                                <h3 class="card-title">Controlling Officer Details</h3>
                                            </div>
											<div class="card-body">
												<div class="row">
												
													<div class="col-md-6">
														<div class="form-group">
															<label>Present Controlling Officer payee Id<em>*</em></label>
															<input type="text" class="form-control" name="presentControllingOfficerpayeeid"
																id="presentControllingOfficerpayeeid" placeholder="Enter Controlling Officer payee Id" onkeyup="this.value = this.value.toUpperCase();" required  />
														</div>
													</div>
													<div class="col-md-6">
														<div class="form-group">
															<label>Present Controlling Officer Mobile No.<em>*</em></label>
															<input type="number" class="form-control" name="presentControllingOfficerMobileNO"
																id="presentControllingOfficerMobileNO" placeholder="Enter Controlling Officer Mobile No." onkeyup="this.value = this.value.toUpperCase();" required  />
														</div>
													</div>
                                                    <div class="col-md-6">
														<div class="form-group">
															<label>Present Controlling Officer Name<em>*</em></label>
															<input type="text" class="form-control" name="presentControllingOfficerName"
																id="presentControllingOfficerName" placeholder="Enter Controlling Officer Name" onkeyup="this.value = this.value.toUpperCase();" required />
														</div>
													</div>
													
                                                    <div class="col-md-6">
														<div class="form-group">
															<label>Present Controlling Officer Designation<em>*</em></label>
															<input type="text" class="form-control" name="presentControllingOfficerDesignation"
																id="presentControllingOfficerDesignation" placeholder="Enter Controlling Officer Designation" onkeyup="this.value = this.value.toUpperCase();" required  />
														</div>
													</div>
												</div>
											</div>
										</div>
								<section class="col-12">
									<!-- <form id="frmregister" name="frmregister" action="" method="post"> -->
										<div class="card card-primary">
											<!-- /.card-header -->
                                            <div class="card-header">
                                                <h3 class="card-title">Bank Details</h3>
                                            </div>
											<div class="card-body">
												<div class="row">
												
                                                    <div class="col-md-6">
														<div class="form-group">
															<label>Bank Account No<em>*</em></label>
															<input type="number" class="form-control" name="bankAccountNo"
																id="bankAccountNo" placeholder="Enter Bank Account No As Par CFMS" required  />
														</div>
													</div>
													<div class="col-md-6">
														<div class="form-group">
															<label>IFSC Code<em>*</em></label>
															<input type="text" class="form-control" name="ifscCode"
																id="ifscCode" placeholder="Enter Bank IFSC Code" onkeyup="this.value = this.value.toUpperCase();" required />
														</div>
													</div>
                                                    <div class="col-md-6">
														<div class="form-group">
															<label>Bank Name<em>*</em></label>
															<input type="text" class="form-control" name="bankName"
																id="bankName" placeholder="Enter Bank Name" onkeyup="this.value = this.value.toUpperCase();" required  readonly/>
														</div>
													</div>
                                                    <div class="col-md-6">
														<div class="form-group">
															<label>Branch Name<em>*</em></label>
															<input type="text" class="form-control" name="branchName"
																id="branchName" placeholder="Enter Bank Branch" onkeyup="this.value = this.value.toUpperCase();" required  readonly />
														</div>
													</div>
                                                  
												</div>
											</div>
										</div>

                                        <div class="card card-primary">
											<!-- /.card-header -->
                                            <div class="card-header">
                                                <h3 class="card-title">Pay Entitlement Details</h3>
                                            </div>
											<div class="card-body">
												<div class="row">
													<div class="col-md-6">
														<div class="form-group">
															<label>Pay Level(LEVEL 15)</label>
															<input type="text" class="form-control" name="payLevel"
																id="payLevel" placeholder="Enter Pay Level" />
														</div>
													</div>
													<div class="col-md-6">
														<div class="form-group">
															<label>Basic Salary Fixed by PVC As ON 1/1/2016 Or at joining</label>
															<input type="number" class="form-control" name="basicSalaryFixed"
																id="basicSalaryFixed" placeholder="Enter Basic Salary Fixed BY PVC AS ON 1/1/2016 at Joining"  />
														</div>
													</div>
                                                    <div class="col-md-6">
														<div class="form-group">
															<label>Current Basic Salary</label>
															<input type="number" class="form-control" name="basicSalary"
																id="basicSalary" placeholder="Enter Current Basic Salary"  />
														</div>
													</div>
													
													<div class="col-md-6">
														<div class="form-group">
															<label>DA</label>
															<input type="number" class="form-control" name="da"
																id="da" placeholder="Enter DA Amount"   />
														</div>
													</div>
											

                                                    <div class="col-md-6">
														<div class="form-group">
															<label>HRA</label>
															<input type="number" class="form-control" name="hra"
																id="hra" placeholder="Enter HRA Amount"   />
														</div>
													</div>
                                                    <div class="col-md-6">
														<div class="form-group">
															<label>CTA</label>
															<input type="number" class="form-control" name="cta"
																id="cta" placeholder="Enter CTA Amount"  />
														</div>
													</div>
													<div class="col-md-6">
														<div class="form-group">
															<label>Medical Allowance</label>
															<input type="number" class="form-control" name="medicalAllowance"
																id="medicalAllowance" placeholder="Enter Medical Allowance"  />
														</div>
													</div>
                                                    <div class="col-md-6">
														<div class="form-group">
															<label>Special/Other Allowance</label>
															<input type="number" class="form-control" name="specialOtherAllowance"
																id="specialOtherAllowance" placeholder="Enter Special Allowance" />
														</div>
													</div>
                                                  
													<div class="col-md-6">
														<div class="form-group">
															<label>Last Month Gross Salary <em>*</em></label>
															<input type="number" class="form-control" name="lastPaymentWithdrawnSalary"
																id="lastPaymentWithdrawnSalary" placeholder="Enter Last Paymemt Withdrawn Salary" required  />
														</div>
													</div>
													<div class="col-md-6">
														<div class="form-group">
															<label>Last month withdrawn Salary(After all deduction)<em>*</em></label>
															<input type="number" class="form-control" name="lastPaymentWithdrawnSalarydeduction"
																id="lastPaymentWithdrawnSalarydeduction" placeholder="Enter Last month withdrawn SalarySalary(After all deduction)" required/>
														</div>
													</div>
												</div>
											</div>
										</div>

                                        <div class="card card-primary">
											<!-- /.card-header -->
                                            <div class="card-header">
                                                <h3 class="card-title">Pay Entitlement Deduction</h3>
                                            </div>
											<div class="card-body">
												<div class="row">
													<div class="col-md-6">
														<div class="form-group">
															<label>Income Tax</label>
															<input type="number" class="form-control" name="incomeTax"
																id="incomeTax" placeholder="Enter Income Tax"  />
														</div>
													</div>
                                                   
													<div class="col-md-6" id="npsContainer" >
														<div class="form-group">
															<label id="npsLabel">NPS</label>
															<input type="number" class="form-control" name="nps" id="nps" placeholder="Enter NPS Amount" />
														</div>
													</div>
													<div class="col-md-6" id="pfContainer">
														<div class="form-group">
															<label>P.F.</label>
															<input type="number" class="form-control" name="pf" id="pf" placeholder="Enter PF Amount" />
														</div>
													</div>
													<div class="col-md-6" id="pfLoanContainer">
														<div class="form-group">
															<label>P.F. Loan</label>
															<input type="number" class="form-control" name="pfLoan" id="pfLoan" placeholder="Enter PF Loan" />
														</div>
													</div>
                                                   
                                                    <div class="col-md-6">
														<div class="form-group">
															<label>G.I.P.</label>
															<input type="number" class="form-control" name="gip"
																id="gip" placeholder="Enter GIP" />
														</div>
													</div>
                                                    <!-- <div class="col-md-6">
														<div class="form-group">
															<label>Net Payable</label>
															<input type="text" class="form-control" name="netpayable"
																id="netpayable" placeholder="Enter Net Payable" />
														</div>
													</div> -->
												</div>
											</div>
											<div class="card-footer">
												<div class="form-group">
													<div class="form-check">
														<input type="checkbox" class="form-check-input" id="declaration" required style="display: none;">
														<label class="form-check-label" for="declaration" style="font-size:large; color: red;">
															<span class="custom-checkbox"></span> I hereby declare that the above information is updated finally by  University  and all details are correct.
														</label>
													</div>
												</div>
												<div>
													<button type="submit" id="btnsubmit" class="btn btn-danger float-right">
														Final Submit
													</button>
												</div>
											</div>
										</div>
										</form>
										</section>
										</div>
										<!-- Main row -->
										</div>
										<!-- /.container-fluid -->
										</section>
										<!-- /.content -->
										</div>
				
				<%- include('../partials/footer'); %>
					<!-- Control Sidebar -->
					<aside class="control-sidebar control-sidebar-dark">
						<!-- Control sidebar content goes here -->
					</aside>
					<!-- /.control-sidebar -->
	</div>
	<!-- ./wrapper -->
	<script>
		$.widget.bridge("uibutton", $.ui.button);
	</script>
	<!-- jQuery -->
<!-- jQuery -->
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<!-- jQuery UI -->
<script src="https://code.jquery.com/ui/1.12.1/jquery-ui.min.js"></script>
<!-- Bootstrap 4 -->
<script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.bundle.min.js"></script>
<!-- Sparkline -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-sparklines/2.1.2/jquery.sparkline.min.js"></script>
<!-- Select2 -->
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
<!-- Moment.js -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.1/moment.min.js"></script>
<!-- Date Range Picker -->
<script src="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.min.js"></script>

<!-- Tempusdominus Bootstrap 4 -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/tempusdominus-bootstrap-4/5.1.2/js/tempusdominus-bootstrap-4.min.js"></script>
<!-- OverlayScrollbars -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/overlayscrollbars/1.13.0/js/jquery.overlayScrollbars.min.js"></script>

<!-- AdminLTE App -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/admin-lte/3.2.0/js/adminlte.min.js"></script>
<!-- jQuery Validation -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-validate/1.19.3/jquery.validate.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-validate/1.19.3/additional-methods.min.js"></script>
<!-- Toastr -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/js/toastr.min.js"></script>

<!-- SweetAlert2 -->
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
<script src="https://unpkg.com/boxicons@2.1.4/dist/boxicons.js"></script>
	<script>
	const apiBaseUrl = "<%= process.env.apiBaseUrl %>";
    const token = localStorage.getItem("access-token-new");
    var userDetails = localStorage.getItem('user-data');
    var userDetailsObj = JSON.parse(userDetails);
	var departmentUsername= userDetailsObj.department_username
	console.log(departmentUsername)
    var universityName= userDetailsObj.university
    $('#university1').append(universityName);
    $('#college1').append(universityName);
    console.log(universityName)
		$(function () {
            $('.select2').select2()
$(document).ready(function() {
    $('#effectiveDateOfJoining').on('change', function() {
        const inputDate = $(this).val();
        const effectiveDate = new Date(inputDate.split('/').reverse().join('-'));
        const cutoffDate = new Date('2005-08-01');

        if (effectiveDate < cutoffDate) {
            $('#npsOptedContainer').hide();
            $('#npsCreatedContainer').hide();
			$('#npsOpted').prop('required', false); 
            $('#npsCreated').prop('required', false); 
        } else {
            $('#npsOptedContainer').show();
            $('#npsOpted').prop('required', true);  // Add required attribute
            $('#npsCreatedContainer').hide();
        }
    });
    function setEffectiveDateOfJoining(date) {
        $('#effectiveDateOfJoining').val(date).trigger('change');
    }
    const apiDate = '';
    setEffectiveDateOfJoining(apiDate);
});

$('#npsOpted').change(function() {
        let npsOptedVal = $(this).val();
        if (npsOptedVal === 'Yes') {
            $('#npsCreatedContainer').show();
            $('#npsCreated').prop('required', true); // Add required attribute
            $('#pf, #pfLoan').prop('required', false).siblings('em').remove();
            $('#pfAccountNoContainer, #pfIfscCodeContainer').show().find('input').prop('required', false).siblings('em').remove();
        } else if (npsOptedVal === 'No') {
            $('#npsCreatedContainer').hide();
            $('#npsCreated').val('').prop('required', false); // Remove required attribute
            $('#pfAccountNoContainer, #pfIfscCodeContainer').find('input').prop('required', true);
			$('#pfContainer label').append('<em>*</em>');
			$('#pfAccountNoContainer label, #pfIfscCodeContainer label').append('<em>*</em>');
            $('#pfContainer').show().find('input').prop('required', true);
            $('#pfLoanContainer').show().find('input').prop('required', false);
        }
    });

    $('#npsCreated').change(function() {
        let npsCreatedVal = $(this).val();
        if ($('#npsOpted').val() === 'Yes' && npsCreatedVal === 'Yes') {
            $('#pranNoContainer').show();
            $('#pranNo').prop('required', true);
			    $('#pranNoContainer label').append('<em>*</em>');
            $('#pfAccountNoContainer, #pfIfscCodeContainer').find('input').prop('required', false).siblings('em').remove();
        } else {
            $('#pranNo').val('').prop('required', false);
        }
    });

	$('#payVerificationStatus').change(function() {
                if ($(this).val() === 'Yes') {
                    $('#pvcNoContainer').show();
                    $('#pvcNo').attr('required', 'required');
                    
                    $('#pvcStatusContainer').hide();
                    $('#pvcStatus').removeAttr('required');
                } else if ($(this).val() === 'No') {
                    $('#pvcStatusContainer').show();
                    $('#pvcStatus').attr('required', 'required');
                    
                    $('#pvcNoContainer').hide();
                    $('#pvcNo').removeAttr('required');
                } else {
                    $('#pvcNoContainer').hide();
                    $('#pvcStatusContainer').hide();
                    $('#pvcNo').removeAttr('required');
                    $('#pvcStatus').removeAttr('required');
                }
            });
	  $(document).ready(function() {
          $('#btnsubmit').prop('disabled', true); 
          $('#declaration').change(function() {
     if ($(this).is(':checked')) {
            $('#btnsubmit').prop('disabled', false); 
			Swal.fire({
			title: "Please Verify and Check All Details which was changed and  Updated By University Admin Before Final Submission",
            text: "कृपया फाइनल  सबमिट करने से पहले जो भी विवरण  बदले गए है उसे सत्यापित और जांच कर  लें",
             icon: "warning",
            confirmButtonColor: "#3085d6",
           cancelButtonColor: "#d33",
           confirmButtonText: "Ok"
            });
        } else {
            $('#btnsubmit').prop('disabled', true); 
        }
    });
});
$(document).ready(function() {
				$.ajax({
                // url: "https://university-hrms.codebucketstage.online/api/v1/stage-two/get-university",
				url: apiBaseUrl + "v1/stage-two/get-university",
                type: 'POST',
				dataType: 'json',
				success: function(response) {
                if (response.status === "success") {
              
                    var universities = response.data.distinctUniversity;
					$('#currentUniversityName').append($('<option>', {
                        value: '',
                            text: 'Select a university'
                             }));
                    $.each(universities, function(index, item) {
                        $('#currentUniversityName').append($('<option>', {
                            value: item.university,
                            text: item.university
                        }));
                    });
                } else {
                    console.error('API request failed with status:', response.status);
                }
            },
            error: function(xhr, status, error) {
                console.error('Error fetching data from API:', error);
            }
        });
		$('#currentUniversityName').change(function() {
            var selectedUniversity = $(this).val();
            $.ajax({
				url: apiBaseUrl + "v1/stage-two/get-university",
                type: 'POST',
                dataType: 'json',
				data: {
                    university: selectedUniversity
                },
                success: function(response) {
                    $('#currentlyPostedAtUniversityOfficeDepartmentCollege').empty();
                    if (response.status === "success") {
						var colleges = response.data.colleges;
						$('#currentlyPostedAtUniversityOfficeDepartmentCollege').append($('<option>', {
                           value: '',
                           text: 'Select a College'
                           }));

                        $.each(colleges, function(index, item) {
							$('#currentlyPostedAtUniversityOfficeDepartmentCollege').append($('<option>', {
                                value: item.college,
                                text: item.college
                            }));
                        });
                    } else {
                        console.error('Failed to load offices:', response.status);
                    }
                },
                error: function(xhr, status, error) {
                    console.error('Error fetching offices:', error);
                }
            });
        });
	});
	$('#dateOfBirth, #employeeType').on('change', function() {
            calculateRetirementDate();
        });
        function calculateRetirementDate() {
            var dateOfBirth = $('#dateOfBirth').val();
            var employeeType = $('#employeeType').val();
    
            if (dateOfBirth && employeeType) {
                var dobParts = dateOfBirth.split('/');
                var day = parseInt(dobParts[0], 10);
                var month = parseInt(dobParts[1], 10) - 1; // JavaScript months are 0-11
                var year = parseInt(dobParts[2], 10);
                var retirementAge = (employeeType === 'Teaching') ? 65 : 62;

                var retirementDate = new Date(year + retirementAge, month, day);
                var formattedRetirementDate = ('0' + retirementDate.getDate()).slice(-2) + '/' +
                                              ('0' + (retirementDate.getMonth() + 1)).slice(-2) + '/' +
                                              retirementDate.getFullYear();

                $('#dateOfRetirement').val(formattedRetirementDate);
            }
        }
    
		// function calculateDA() {
        //     var basicSalary = parseFloat($('#basicSalary').val());
        //     var daPercentage = parseFloat($('#daPercentage').val());
 
        //     if (!isNaN(basicSalary) && !isNaN(daPercentage)) {
        //         var daAmount = (basicSalary * daPercentage) / 100;
        //         $('#da').val(daAmount.toFixed(2));
        //     } else {
        //         $('#da').val('');
        //     }

        //     calculateTotal();
        // }

        // function calculateHRA() {
        //     var basicSalary = parseFloat($('#basicSalary').val());
        //     var hraPercentage = parseFloat($('#hraPercentage').val());

        //     if (!isNaN(basicSalary) && !isNaN(hraPercentage)) {
        //         var hraAmount = (basicSalary * hraPercentage) / 100;
        //         $('#hra').val(hraAmount.toFixed(2));
        //     } else {
        //         $('#hra').val('');
        //     }

        //     calculateTotal();
        // }

        // function calculateTotal() {
        //     var basicSalary = parseFloat($('#basicSalary').val());
        //     var da = parseFloat($('#da').val());
        //     var hra = parseFloat($('#hra').val());
        //     var cta = parseFloat($('#cta').val());
        //     var medicalAllowance = parseFloat($('#medicalAllowance').val());
        //     var specialOtherAllowance = parseFloat($('#specialOtherAllowance').val());

        //     if (!isNaN(basicSalary) && !isNaN(da) && !isNaN(hra) && !isNaN(cta) && !isNaN(medicalAllowance) && !isNaN(specialOtherAllowance)) {
        //         var totalSalary = basicSalary + da + hra + cta + medicalAllowance + specialOtherAllowance;
        //         $('#lastPaymentWithdrawnSalary').val(totalSalary.toFixed(2));
        //     } else {
        //         $('#lastPaymentWithdrawnSalary').val('');
        //     }
        // }

        // $('#basicSalary, #daPercentage').on('input change', calculateDA);
        // $('#basicSalary, #hraPercentage').on('input change', calculateHRA);
        // $('#cta, #medicalAllowance, #specialOtherAllowance').on('input change', calculateTotal);


		
// Function to handle final submission
function finalSubmitForm() {
	const apiBaseUrl = "<%= process.env.apiBaseUrl %>";
    var formData = {};
	var isValid = true;

// Validate required fields
$('#frmregister').find('[required]').each(function() {
	if ($(this).val() === '') {
		isValid = false;
		$(this).addClass('is-invalid'); 
		$(this).removeClass('is-invalid'); 
	}
});
if (!isValid) {

	Swal.fire({
		icon: 'warning',
		title: 'Oops...',
		text: 'Please fill all required fields and then try to Update!',
	});
	return;
}

$('#frmregister').serializeArray().forEach(function(item) {
        formData[item.name] = item.value;
    });

 
    formData.finalSubmit = 'YES';
    formData.spouseName = "";
    formData.editedByUniversity="YES";
    formData.editedBy=universityName;
	formData.id = $('#id').val();
	formData.departmentUsername = departmentUsername;
    var jsonData = JSON.stringify(formData);

    $.ajax({
        type: 'POST',
         url: apiBaseUrl + "v1/stage-two/save-university-data",
        data: jsonData,
        dataType: 'json', 
		headers: {
                    token: localStorage.getItem("access-token-new"),
                    "Content-Type": "application/json"
                },
        success: function(response) {
            Swal.fire({
                icon: 'success',
                title: 'Employee Details is Finally Registered, Updated and Changed!',
                text: "PDF is Automatically Downloaded. Click On Okay Button!",
                confirmButtonText: "Okay"
			}).then((result) => {
            if (result.isConfirmed) {
                generatePDF(formData);
                location.reload(); 
            }
        });
    },
        error: function(xhr, status, error) {
            console.error('Error:', error);
            Swal.fire({
                icon: 'warning',
                title: 'Oops...',
                text: 'Please Fill All Required Details and Then Try to Update!',
            });
        }
    });
}

// Function to handle save
function saveForm() {
	const apiBaseUrl = "<%= process.env.apiBaseUrl %>";
    var formData = {};

    $('#frmregister').serializeArray().forEach(function(item) {
		formData[item.name] = item.value || "";
		// if (item.name === "gender" && !item.value) {
        //     formData[item.name] = ""; 
        // } else {
        //     formData[item.name] = item.value;
        // }
    });
	  if (!formData.hasOwnProperty('pvcStatus')) {
                formData['pvcStatus'] = '';
            }
            if (!formData.hasOwnProperty('pvcNo')) {
                formData['pvcNo'] = '';
            }

    // Add the extra field with the specified value
    formData.finalSubmit = 'NO';
    formData.spouseName = "";
	formData.id = $('#id').val();
	formData.departmentUsername = departmentUsername;
    var jsonData = JSON.stringify(formData);
    $.ajax({
        type: 'POST',
		// url: apiBaseUrl + "v1/stage-two/save-university-data",
       url: apiBaseUrl + "v1/stage-two/save-university-data",
        data: jsonData,
        contentType: 'application/json', 
        dataType: 'json', 
        success: function(response) {
            Swal.fire({
                icon: 'success',
                title: 'Form is Successfully Saved!',
                text: "Your data has been saved.",
                confirmButtonText: "Okay"
			}).then((result) => {
				if (result.isConfirmed) {
                generatePDF(formData);
                location.reload(); 
            }
        });
    },
        error: function(xhr, status, error) {
            console.error('Error:', error);
            Swal.fire({
                icon: 'warning',
                title: 'Oops...',
                text: 'Please Fill All Required Details and Dropdown values and Then Try to Save!',
            });
        }
    });
}

// Handle form submission with "Final Submit" button
$('#frmregister').submit(function(e) {
    $('form').on('keydown', function(event) {
        if (event.key === 'Enter') {
            event.preventDefault();
        }
    });
    e.preventDefault();
    finalSubmitForm();
});

// Handle form submission with "Save" button
$('#savebutton').click(function() {
    saveForm();
});

//for ifsc auto fetch

$('#ifscCode').on('keyup', function() {
                var ifscCode = $(this).val().toUpperCase();
                if (ifscCode.length === 11) {
                    $.ajax({
                       url: apiBaseUrl + "v1/stage-two/get-ifsc-code",
                        method: 'POST',
                        contentType: 'application/json',
                        data: JSON.stringify({ ifscCode: ifscCode }), 
                        success: function(response) {
                            if (response.status === 'success' && response.data.length > 0) {
                                var bankDetails = response.data[0];
                                $('#bankName').val(bankDetails.bank);
                                $('#branchName').val(bankDetails.branch_name);
                            } else {
                                alert('Invalid IFSC Code');
                            }
                        },
                        error: function() {
                            alert('Error fetching bank details');
                        }
                    });
                }
            });

$('#btnsearch').click(function() {
	const apiBaseUrl = "<%= process.env.apiBaseUrl %>";
    var payeeID = $('#id').val();

    $.ajax({
		 url: apiBaseUrl + "v1/stage-two/get-payee-details",
        //  url: "https://university-hrms.codebucketstage.online/api/v1/stage-two/get-payee-details",
        method: "POST",
        contentType: "application/json",
        data: JSON.stringify({
            "id": payeeID
        }),
        success: function (response) {
			$('#frmregister')[0].reset();
			$("#prefix").val("").trigger('change');
            $('#gender').val("").trigger('change');
			$("#currentlyPostedDetails").val("").trigger('change');
			$("#annualOptedDate").val("").trigger('change');
			$("#payVerificationStatus").val("").trigger('change');
			$("#pvcStatus").val("").trigger('change');
			$("#npsOpted").val("").trigger('change');
			$("#npsCreated").val("").trigger('change');
			$("#hraPercentage").val("").trigger('change');
			 $("#daPercentage").val("").trigger('change');
			 $("#employeeType").val("").trigger('change');
			 $("#currentUniversityName").val("").trigger('change');
            $("#currentlyPostedDetails").val("").trigger('change');
                     if (response.data.length === 0) {
							alert("⚠️ Your New Payee ID is not Registered. Please Register and Submit The Employee Details . 📞");
							return;
						}
            var payeeDetails = response.data[0];
            var genderValue = payeeDetails.gender;
            var bankAccountNo = payeeDetails.bankAccountNo;
            // Populate the fields
            $("#prefix").val(payeeDetails.prefix).trigger('change');
            $("#firstName").val(payeeDetails.firstName);
            $("#middleName").val(payeeDetails.middleName || "");
            $("#lastName").val(payeeDetails.lastName);
            $('#gender').val(genderValue || "").trigger('change');
            $("#dateOfBirth").val(formatDate(payeeDetails.dateOfBirth));
            $("#fatherName").val(payeeDetails.fatherName || "");
            $("#motherName").val(payeeDetails.motherName || "");
            $("#spouseName").val("");
            $("#personalMobileNumber").val(payeeDetails.personalMobileNumber);
            $("#personalEmail").val(payeeDetails.personalEmail);
            $("#pfAccountNo").val(payeeDetails.pfAccountNo || "");
			$("#pfAccountIfscCode").val(payeeDetails.pfAccountIfscCode || "");
            $("#pranNo").val(payeeDetails.pranNo);
            $("#panNo").val(payeeDetails.panNo || "");
            $("#aadharCardNo").val(payeeDetails.aadharCardNo || "");
            $("#payIdNo").val(payeeDetails.payIdNo);
			$("#bankAccountNo").val(bankAccountNo);
            $("#bankName").val(payeeDetails.bankName);
            $("#branchName").val(payeeDetails.branchName || "");
            $("#ifscCode").val(payeeDetails.ifscCode || "").trigger('change');
            // $("#currentUniversityName").val(payeeDetails.currentUniversityName).trigger('change');
            // $("#currentlyPostedDetails").val(payeeDetails.currentlyPostedDetails).trigger('change');
			// setTimeout(function() {
            //     $("#currentlyPostedAtUniversityOfficeDepartmentCollege").val(payeeDetails.currentlyPostedAtUniversityOfficeDepartmentCollege).trigger('change');
            // }, 1000);
        
			$("#currentDesignation").val(payeeDetails.currentDesignation);
			$("#designationAtJoining").val(payeeDetails.designationAtJoining);
            $("#currentlyPostedDetails").val(payeeDetails.currentlyPostedDetails).trigger('change');
            $("#presentControllingOfficerName").val(payeeDetails.presentControllingOfficerName || "");
            $("#presentControllingOfficerDesignation").val(payeeDetails.presentControllingOfficerDesignation || "");
            $("#dateOfRetirement").val(formatDate(payeeDetails.dateOfRetirement));
            $("#govtQuarterOccupied").val(payeeDetails.govtQuarterOccupied).trigger('change');
            $("#annualOptedDate").val(payeeDetails.annualOptedDate).trigger('change');
            $("#payVerificationStatus").val(payeeDetails.payVerificationStatus).trigger('change');
			$("#pvcNo").val(payeeDetails.pvcNo || "");
			$("#pvcStatus").val(payeeDetails.pvcStatus).trigger('change');
            $("#payLevel").val(payeeDetails.payLevel || "");
            $("#basicSalary").val(payeeDetails.basicSalary || "");
			$("#hra").val(payeeDetails.hra || "");
            $("#cta").val(payeeDetails.cta || "");
            $("#da").val(payeeDetails.da || "")
			$("#subject").val(payeeDetails.subject || "")
            $("#specialOtherAllowance").val(payeeDetails.specialOtherAllowance || "");
            $("#medicalAllowance").val(payeeDetails.medicalAllowance || "");
            $("#lastPaymentWithdrawnSalary").val(payeeDetails.lastPaymentWithdrawnSalary || "");
            $("#incomeTax").val(payeeDetails.incomeTax || "");
            $("#npsOpted").val(payeeDetails.npsOpted).trigger('change');
			$("#npsCreated").val(payeeDetails.npsCreated).trigger('change');
			// $("#npsOpted").val(payeeDetails.npsOpted).trigger('change');
            $("#pf").val(payeeDetails.pf || "");
            $("#nps").val(payeeDetails.nps || "");
            $("#pfLoan").val(payeeDetails.pfLoan || "");
			 $("#hraPercentage").val(payeeDetails.hraPercentage || "").trigger('change');
			 $("#daPercentage").val(payeeDetails.daPercentage || "").trigger('change');
            // $("#lic").val(payeeDetails.lic || "");
            $("#gip").val(payeeDetails.gip || "");
            $("#academicGradePay").val(payeeDetails.academicGradePay || "");
			$("#basicSalaryFixed").val(payeeDetails.basicSalaryFixed || "");
            $("#appointmentLetterOrderNoDate").val(formatDate(payeeDetails.appointmentLetterOrderNoDate));
            $("#employeeType").val(payeeDetails.employeeType).trigger('change');
            $("#effectiveDateOfJoining").val(formatDate(payeeDetails.effectiveDateOfJoining));
            $("#presentControllingOfficerpayeeid").val(payeeDetails.presentControllingOfficerpayeeid);
            $("#presentControllingOfficerMobileNO").val(payeeDetails.presentControllingOfficerMobileNO);
			$("#lastPaymentWithdrawnSalarydeduction").val(payeeDetails.lastPaymentWithdrawnSalarydeduction);
			$("#currentUniversityName").val(payeeDetails.currentUniversityName).trigger('change');
            $("#currentlyPostedDetails").val(payeeDetails.currentlyPostedDetails).trigger('change');
			
            // Use a promise to handle dependent dropdown population
            populateCollegeDropdown(payeeDetails.currentUniversityName)
                .then(() => {
                    $("#currentlyPostedAtUniversityOfficeDepartmentCollege").val(payeeDetails.currentlyPostedAtUniversityOfficeDepartmentCollege).trigger('change');
                })
                .catch(error => {
                    console.error("Error populating college dropdown: ", error);
                });
           
        },
        error: function (xhr, status, error) {
            console.error(xhr.responseText);
        }
		
    });
});
$('#ifscCode').on('change', function() {
                        var ifscCode = $(this).val().toUpperCase();
                        if (ifscCode.length === 11) {
                            $.ajax({
                               url: apiBaseUrl + "v1/stage-two/get-ifsc-code",
                                method: 'POST',
                                contentType: 'application/json',
                                data: JSON.stringify({ ifscCode: ifscCode }), 
                                success: function(response) {
                                    if (response.status === 'success' && response.data.length > 0) {
                                        var bankDetails = response.data[0];
                                        $('#bankName').val(bankDetails.bank);
                                        $('#branchName').val(bankDetails.branch_name);
                                    } else {
                                        alert('Invalid IFSC Code');
                                    }
                                },
                                error: function() {
                                    alert('Error fetching bank details');
                                }
                            });
                        }
					})
function populateCollegeDropdown(universityName) {
    return new Promise((resolve, reject) => {
       
        $.ajax({
			url: apiBaseUrl + "v1/stage-two/get-university",
            method: "POST",
            contentType: "application/json",
            data: JSON.stringify({
                "university": universityName
            }),
			success: function (response) {
                var colleges = response.data.colleges;
                var $collegeDropdown = $("#currentlyPostedAtUniversityOfficeDepartmentCollege");
                $collegeDropdown.empty(); 
                $.each(colleges, function(index, college) {
                    $collegeDropdown.append(new Option(college.college, college.college));
                });
                resolve();
            },
            error: function (xhr, status, error) {
                console.error("Error populating college dropdown: ", error);
                reject(error);
            }
        });
    });
}


function formatDate(dateString) {
    if (!dateString) return '';

    var dateParts = dateString.split(/[-\/]/); 
    if (dateParts.length !== 3) return '';

    var year, month, day;
    if (dateParts[0].length === 4) { // Format YYYY-MM-DD or YYYY/MM/DD
        year = dateParts[0];
        month = dateParts[1];
        day = dateParts[2];
    } else if (dateParts[2].length === 4) { // Format DD-MM-YYYY or DD/MM/YYYY
        day = dateParts[0];
        month = dateParts[1];
        year = dateParts[2];
    } else {
        return ''; 
    }

    return [day.padStart(2, '0'), month.padStart(2, '0'), year].join('/');
}
//automatic generate pdf
function generatePDF(formData) {
    const { jsPDF } = window.jspdf;
    const doc = new jsPDF();
  
    const titleFont = "helvetica";
    const titleFontSize = 14;
    const titleColor = [0, 0, 0];

    const fieldFont = "helvetica";
    const fieldFontSize = 10;
    const fieldLabelColor = [0, 0, 0];
    const fieldValueColor = [50, 50, 50];

    const lineHeight = 7;
    const pageWidth = doc.internal.pageSize.getWidth();
    const borderMargin = 15;

    // Set up title styles
    doc.setFont(titleFont, "bold");
    doc.setFontSize(titleFontSize);
    doc.setTextColor(...titleColor);
    const additionalText = "(Update By University)";
    const titleText = (formData.currentUniversityName || "University Name") + additionalText;

    const titleX = (pageWidth - doc.getTextWidth(titleText)) / 2;
    doc.text(titleText, titleX, 10); 

    // Sub-title
    doc.text("Employee Personal Details", 10, 20); 

    // Set up field styles
    doc.setFont(fieldFont, "bold");
    doc.setFontSize(fieldFontSize);
    doc.setTextColor(...fieldLabelColor);


    let y = 30;
     function addTwoFieldsOnSameLineWithBorder(key1, value1, key2, value2, x, y) {
        const totalWidth = pageWidth - borderMargin * 2;
        const separatorX = x + totalWidth / 2;

    
        doc.setDrawColor(0, 0, 0); 
        doc.setLineWidth(0.5);
        doc.rect(x - 2, y - lineHeight, totalWidth, lineHeight + 4);

        doc.line(separatorX, y - lineHeight, separatorX, y + 4);

        doc.setTextColor(...fieldLabelColor);
        doc.text(`${key1}:`, x + 2, y);
        doc.setTextColor(...fieldValueColor);
        doc.text(value1, x + 2 + doc.getTextWidth(`${key1}: `), y);

        doc.setTextColor(...fieldLabelColor);
        doc.text(`${key2}:`, separatorX + 2, y);
        doc.setTextColor(...fieldValueColor);
        doc.text(value2, separatorX + 2 + doc.getTextWidth(`${key2}: `), y);
    }

    function addContentWithBorder(key, value, x, y, valueXOffset = 50) {
        const totalWidth = pageWidth - borderMargin * 2;
        doc.setDrawColor(0, 0, 0); 
        doc.setLineWidth(0.5); 
        doc.rect(x - 2, y - lineHeight, totalWidth, lineHeight + 4);
        doc.setTextColor(...fieldLabelColor);
        doc.text(`${key}:`, x + 2, y);
        doc.setTextColor(...fieldValueColor);
        doc.text(value, x + valueXOffset, y);
    }
    addTwoFieldsOnSameLineWithBorder("Payee ID", formData.payIdNo, "Prefix", formData.prefix, borderMargin, y);
    y += lineHeight + 5;
    addTwoFieldsOnSameLineWithBorder("First Name", formData.firstName, "Middle Name", formData.middleName, borderMargin, y);
    y += lineHeight + 5;
    addTwoFieldsOnSameLineWithBorder("Last Name", formData.lastName, "Gender", formData.gender, borderMargin, y);
    y += lineHeight + 5;
    addTwoFieldsOnSameLineWithBorder("Date of Birth", formData.dateOfBirth, "Father's Name", formData.fatherName, borderMargin, y);
    y += lineHeight + 5;
    addTwoFieldsOnSameLineWithBorder("Mother's Name", formData.motherName,"PRAN No", formData.pranNo, borderMargin, y);
    y += lineHeight + 5;
    addTwoFieldsOnSameLineWithBorder("Personal Mobile Number", formData.personalMobileNumber, "Email", formData.personalEmail, borderMargin, y);
    y += lineHeight + 5;
    addTwoFieldsOnSameLineWithBorder("PF Account No", formData.pfAccountNo, "PF Account IFSC Code", formData.pfAccountIfscCode, borderMargin, y);
    y += lineHeight + 5;
    addTwoFieldsOnSameLineWithBorder("PAN No", formData.panNo, "Aadhar Card No", formData.aadharCardNo, borderMargin, y);
    y += lineHeight + 5;
    // New section: Employee Official Details
    doc.setFontSize(titleFontSize);
    doc.setTextColor(...titleColor);
    doc.text("Employee Official Details", 10, y);
    y += lineHeight + 5;

    doc.setFont(fieldFont, "bold");
    doc.setFontSize(fieldFontSize);
    doc.setTextColor(...fieldLabelColor);

    addTwoFieldsOnSameLineWithBorder("Appointment Letter Issue Date", formData.appointmentLetterOrderNoDate, "Employee Type", formData.employeeType, borderMargin, y);
    y += lineHeight + 5;
	addTwoFieldsOnSameLineWithBorder("Effective Date of Joining", formData.effectiveDateOfJoining,  "Designation At the Time of Joining*", formData.designationAtJoining, borderMargin, y);
    y += lineHeight + 5;
	addContentWithBorder( "University Name", formData.currentUniversityName, borderMargin, y);
    y += lineHeight + 5;
    // Specific section with full-width border and no vertical separator
    addContentWithBorder("Currently Posted at University Office/Department/College", formData.currentlyPostedDetails, borderMargin, y, 100);
    y += lineHeight + 5;
    addContentWithBorder("Current Office/Department/College", formData.currentlyPostedAtUniversityOfficeDepartmentCollege, borderMargin, y, 65);
    y += lineHeight + 5;

    addTwoFieldsOnSameLineWithBorder("Current Designation", formData.currentDesignation, "Date of Retirement", formData.dateOfRetirement, borderMargin, y);
    y += lineHeight + 5;
  
    addTwoFieldsOnSameLineWithBorder("Govt. Quarter Occupied", formData.govtQuarterOccupied, "Annual Increment Opted Date", formData.annualOptedDate, borderMargin, y);
    y += lineHeight + 5;
	addTwoFieldsOnSameLineWithBorder("Present Controlling Officer Payid", formData.presentControllingOfficerpayeeid, " Controlling Officer Mobile No. ", formData.presentControllingOfficerMobileNO, borderMargin, y);
    y += lineHeight + 5;

    addContentWithBorder("Present Controlling Officer Name", formData.presentControllingOfficerName, borderMargin, y, 70);
    y += lineHeight + 5;
    addContentWithBorder("Present Controlling Officer Designation", formData.presentControllingOfficerDesignation, borderMargin, y, 75);
    y += lineHeight + 5;
    addContentWithBorder("Payment Verification Status", formData.payVerificationStatus, borderMargin, y);
    y += lineHeight + 5;
    if (formData.payVerificationStatus === "Yes" && formData.pvcNo) {
        addContentWithBorder("PVC No", formData.pvcNo, borderMargin, y);
        y += lineHeight + 5;
    }
	 if (formData.payVerificationStatus === "No" && formData.pvcStatus) {
        addContentWithBorder("PVC Status Reason", formData.pvcStatus, borderMargin, y);
        y += lineHeight + 5;
    }
	if (y >= 250) {
        doc.addPage();
        y = 20;
    } else {
        y += lineHeight * 2;
    }


    // Add bank details heading
    doc.setFontSize(titleFontSize);
    doc.setTextColor(...titleColor);
    doc.text("Bank Details", 10, y);
    y += lineHeight + 5;

    doc.setFont(fieldFont, "bold");
    doc.setFontSize(fieldFontSize);
    doc.setTextColor(...fieldLabelColor);

    addTwoFieldsOnSameLineWithBorder("Bank Account No", formData.bankAccountNo, "Bank Name", formData.bankName, borderMargin, y);
    y += lineHeight + 5;
    addTwoFieldsOnSameLineWithBorder("Branch Name", formData.branchName, "IFSC Code", formData.ifscCode, borderMargin, y);
    y += lineHeight + 5;

	// doc.addPage();
    // y = 20;

    // Add Pay Entitlement Details heading
    doc.setFontSize(titleFontSize);
    doc.setTextColor(...titleColor);
    doc.text("Pay Entitlement Details", 10, y);
    y += lineHeight + 5;

    doc.setFont(fieldFont, "bold");
    doc.setFontSize(fieldFontSize);
    doc.setTextColor(...fieldLabelColor);

    addTwoFieldsOnSameLineWithBorder("Pay Level", formData.payLevel, "Current Basic Salary", formData.basicSalary, borderMargin, y);
    y += lineHeight + 5;
	addContentWithBorder("Basic Salary Fixed BY PVC As ON 1/1/2016 at Or joining", formData.basicSalaryFixed, borderMargin, y,100);
    y += lineHeight + 5;
	addContentWithBorder("Last month withdrawn Salary(After all deduction)", formData.lastPaymentWithdrawnSalarydeduction, borderMargin, y,100);
    y += lineHeight + 5;
    addTwoFieldsOnSameLineWithBorder("HRA", formData.hra, "CTA", formData.cta, borderMargin, y);
    y += lineHeight + 5;
    addTwoFieldsOnSameLineWithBorder("DA", formData.da, "Special/Other Allowance", formData.specialOtherAllowance, borderMargin, y);
    y += lineHeight + 5;
    addTwoFieldsOnSameLineWithBorder("Medical Allowance", formData.medicalAllowance, "Last Month Gross Salary", formData.lastPaymentWithdrawnSalary, borderMargin, y);
    y += lineHeight + 5;


    // Add Pay Entitlement Deduction heading
    doc.setFontSize(titleFontSize);
    doc.setTextColor(...titleColor);
    doc.text("Pay Entitlement Deduction", 10, y);
    y += lineHeight + 5;

    doc.setFont(fieldFont, "bold");
    doc.setFontSize(fieldFontSize);
    doc.setTextColor(...fieldLabelColor);

    addTwoFieldsOnSameLineWithBorder("Income Tax", formData.incomeTax, "NPS Opted", formData.npsOpted, borderMargin, y);
    y += lineHeight + 5;
    addTwoFieldsOnSameLineWithBorder("NPS created", formData.npsCreated, "NPS", formData.nps, borderMargin, y);
    y += lineHeight + 5;
	addContentWithBorder("PF Loan", formData.pfLoan,  borderMargin, y,60);
    y += lineHeight + 5;
	addTwoFieldsOnSameLineWithBorder("GIP", formData.gip,"PF", formData.pf, borderMargin, y);

    // Save the PDF
    doc.save('EmployeeDetails.pdf');
}
const formData = {
};
});
	</script>
	 <script>
        function isTokenExpired(token) {
            if (!token) return true;
            try {
                // Decode JWT payload
                const payloadBase64 = token.split('.')[1];
                const payload = JSON.parse(atob(payloadBase64));
                // Check if current time is past the expiration time
                const isExpired = payload.exp * 1000 < Date.now();
                return isExpired;
            } catch (error) {
                console.error("Failed to decode token:", error);
                return true; 
            }
        }
        // Check token expiration on load
        $(function () {
           const token = localStorage.getItem("access-token-new");
            const userData = JSON.parse(userDetails);
            if (token && userData) {
                // Check role and token expiration
                if (usersData.role !== "University-Admin" || isTokenExpired(token)) {
                    alert("Session expired or unauthorized access. Redirecting to login.");
                    window.location = "/";
                } else {
                    setInterval(() => {
                        if (isTokenExpired(token)) {
                            alert("Session expired. Please login again.");
                            window.location = "/";
                        }
                    }, 60000);
                }
            } else {
                window.location = "/";
            }
        });
                        </script> 
</body>

</html>