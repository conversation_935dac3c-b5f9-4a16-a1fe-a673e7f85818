{"name": "bpschrms", "version": "0.0.0", "author": "<PERSON><PERSON> <<EMAIL>>", "private": true, "scripts": {"start": "nodemon ./bin/www"}, "dependencies": {"cookie-parser": "~1.4.4", "debug": "~2.6.9", "dotenv": "^16.1.4", "ejs": "~2.6.1", "env": "0.0.2", "express": "~4.16.1", "http-errors": "~1.6.3", "morgan": "~1.9.1", "node-localstorage": "^3.0.5", "mongoose": "^8.0.0", "multer": "^1.4.5-lts.1", "xlsx": "^0.18.5", "pdfkit": "^0.14.0", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "nodemailer": "^6.9.7", "moment": "^2.29.4", "express-validator": "^7.0.1", "cors": "^2.8.5"}, "devDependencies": {"nodemon": "^3.1.9"}}