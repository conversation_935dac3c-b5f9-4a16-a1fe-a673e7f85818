<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>
        <%= title %>
    </title>
    <!-- Google Font: Source Sans Pro -->
    <link rel="stylesheet"
        href="https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,400i,700&display=fallback" />

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css" />

    <!-- Ionicons -->
    <link rel="stylesheet" href="https://code.ionicframework.com/ionicons/2.0.1/css/ionicons.min.css" />


    <!-- Tempusdominus Bootstrap 4 -->
    <link rel="stylesheet"
        href="https://cdnjs.cloudflare.com/ajax/libs/tempusdominus-bootstrap-4/5.39.0/css/tempusdominus-bootstrap-4.min.css" />

    <!-- iCheck -->
    <link rel="stylesheet"
        href="https://cdnjs.cloudflare.com/ajax/libs/icheck-bootstrap/3.0.1/icheck-bootstrap.min.css" />

    <!-- Select2 -->
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/select2@4.0.13/dist/css/select2.min.css" />

    <!-- Theme style -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/admin-lte/3.1.0/css/adminlte.min.css" />

    <!-- overlayScrollbars -->
    <link rel="stylesheet"
        href="https://cdnjs.cloudflare.com/ajax/libs/overlayscrollbars/1.13.0/css/OverlayScrollbars.min.css" />

    <!-- Daterange picker -->
    <link rel="stylesheet" href="https://cdn.datatables.net/1.10.24/css/dataTables.bootstrap4.min.css">


    <!-- DataTables -->
    <link rel="stylesheet" href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap4.min.css">
    <link rel="stylesheet" href="https://cdn.datatables.net/responsive/2.2.9/css/responsive.bootstrap4.min.css">
    <link rel="stylesheet" href="https://cdn.datatables.net/buttons/2.0.1/css/buttons.bootstrap4.min.css">
    <!-- Baoxicon -->
    <link href='https://unpkg.com/boxicons@2.1.4/css/boxicons.min.css' rel='stylesheet'>
    <style type="text/css">
        .seat {
            width: 50px;
        }

        input::-webkit-outer-spin-button,
        input::-webkit-inner-spin-button {
            -webkit-appearance: none;
            margin: 0;
        }

        .card-title {
            font-weight: bold !important;
        }

        em {
            color: red;
        }

        .loader {
            position: relative;
            width: 2.5em;
            height: 2.5em;
            transform: rotate(165deg);
        }

        .loader:before,
        .loader:after {
            content: "";
            position: absolute;
            top: 50%;
            left: 50%;
            display: block;
            width: 0.5em;
            height: 0.5em;
            border-radius: 0.25em;
            transform: translate(-50%, -50%);
        }

        .loader:before {
            animation: before8 2s infinite;
        }

        .loader:after {
            animation: after6 2s infinite;
        }

        @keyframes before8 {
            0% {
                width: 0.5em;
                box-shadow: 1em -0.5em rgba(225, 20, 98, 0.75), -1em 0.5em rgba(111, 202, 220, 0.75);
            }

            35% {
                width: 2.5em;
                box-shadow: 0 -0.5em rgba(225, 20, 98, 0.75), 0 0.5em rgba(111, 202, 220, 0.75);
            }

            70% {
                width: 0.5em;
                box-shadow: -1em -0.5em rgba(225, 20, 98, 0.75), 1em 0.5em rgba(111, 202, 220, 0.75);
            }

            100% {
                box-shadow: 1em -0.5em rgba(225, 20, 98, 0.75), -1em 0.5em rgba(111, 202, 220, 0.75);
            }
        }

        @keyframes after6 {
            0% {
                height: 0.5em;
                box-shadow: 0.5em 1em rgba(61, 184, 143, 0.75), -0.5em -1em rgba(233, 169, 32, 0.75);
            }

            35% {
                height: 2.5em;
                box-shadow: 0.5em 0 rgba(61, 184, 143, 0.75), -0.5em 0 rgba(233, 169, 32, 0.75);
            }

            70% {
                height: 0.5em;
                box-shadow: 0.5em -1em rgba(61, 184, 143, 0.75), -0.5em 1em rgba(233, 169, 32, 0.75);
            }

            100% {
                box-shadow: 0.5em 1em rgba(61, 184, 143, 0.75), -0.5em -1em rgba(233, 169, 32, 0.75);
            }
        }

        .loader {
            position: absolute;
            top: calc(50% - 1.25em);
            left: calc(50% - 1.25em);
        }

        .modal-fullscreen {
            max-width: 90%;
            width: 90%;
            height: 220vh;
            margin: 0;
            margin-left: 60px;
        }

        .modal-content {
            height: 100%;

        }
    </style>
</head>
<body class="hold-transition sidebar-mini layout-fixed sidebar-collapse">
    <div class="wrapper">
        <!-- Preloader -->
        <div class="preloader flex-column justify-content-center align-items-center">
            <img class="animation__shake" src="/images/finger.gif" alt="CodeBucket" height="100" width="100" />
        </div>
        <!-- Navbar -->
        <%- include('../partials/header'); %>
            <!-- /.navbar -->
            <!-- Main Sidebar Container -->
            <%- include('../partials/sidebar'); %>
                <!-- Content Wrapper. Contains page content -->
                <div class="content-wrapper">
                    <!-- Content Header (Page header) -->
                    <div class="content-header">
                        <div class="container-fluid">
                            <div class="row mb-2">
                                <div class="col-sm-6">
                                    <h1 class="m-0" style="color: blue;">Rejected Salary Details Of Employee:</h1>
                                </div>
                                <!-- /.col -->
                                <div class="col-sm-6">
                                    <ol class="breadcrumb float-sm-right">
                                        <li class="breadcrumb-item"><a href="/">Home</a></li>
                                        <li class="breadcrumb-item active">Dashboard</li>
                                    </ol>
                                </div>
                            </div>
                        </div>
                        <!-- /.container-fluid -->
                    </div>
                    <!-- Main content -->
                    <section class="content">
                        <div class="container-fluid">
                            <div class="row">
                                <section class="col-12">
                                    <div class="row">
                                        <!-- <div class="col-lg-4 col-12"> -->
                                        <!-- small card -->
                                        <!-- <div class="small-box bg-info">
                                <div class="inner">
                                    <h3>Total Employee &nbsp; &nbsp;<span id="totalEmployee">111</span></h3>
                                    <p>Employee Details</p>
                                </div>
                                <div class="icon">
                                    <i class="ion ion-stats-bars"></i>
                                </div>
                                <a href="#pendinglist" onclick="showPendingList()" class="small-box-footer">
                                    View Pending List <i class="fas fa-arrow-circle-right"></i>
                                </a>
                            </div>
                        </div> -->
                                        <div class="col-lg-6 col-12">

                                            <div class="small-box bg-info">
                                                <div class="inner">
                                                    <h3>View Details Of Rejected By Checker &nbsp; &nbsp; <span
                                                            id="rejectedemployeechecker">0</span></h3>

                                                </div>
                                                <div class="icon">
                                                    <i class="ion ion-stats-bars"></i>
                                                </div>
                                                <a href="#checkerEmployee" id="checkerEmployeeLink"
                                                    class="small-box-footer">
                                                    View Details Of Rejected Employees <i
                                                        class="fas fa-arrow-circle-right"></i>
                                                </a>
                                            </div>
                                        </div>
                                        <div class="col-lg-6 col-12">
                                            <!-- small card -->
                                            <div class="small-box bg-danger">
                                                <div class="inner">
                                                    <h3>View Details Of Rejected By Approver &nbsp; &nbsp; <span
                                                            id="rejectedemployeeapprover">0</span></h3>
                                                    <p></p>
                                                </div>
                                                <div class="icon">
                                                    <i class="ion ion-stats-bars"></i>
                                                </div>
                                                <a href="#showapproverEmployee" id="approverEmployeeLink"
                                                    class="small-box-footer">
                                                    View Details Of Rejected Employees <i
                                                        class="fas fa-arrow-circle-right"></i>
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </section>
                                <div class="loader" style="display: none; margin-top: 10px;"></div>
                                <section class="col-12">
                                    <div id="checkerEmployeeDetails" class="card card-default" style="display: none;">
                                        <div class="card-header">
                                            <h1 class="card-title" style="color: red;">Total Rejected Employees By
                                                Payment Checker:</h1>
                                        </div>
                                        <div class="card-body">
                                            <table class="table table-bordered" id="checkerEmployeeTable"
                                                style="width: 100%;">
                                                <thead>
                                                    <tr>
                                                        <th>Serail No.</th>
                                                        <th>Name</th>
                                                        <th>Payee ID</th>
                                                        <th>Absentee Month</th>
                                                        <th>Reject Remarks</th>
                                                        <th>View Data</th>

                                                    </tr>
                                                </thead>
                                                <tbody id="checkerEmployeeTbody">
                                                    <!-- Table Body Here -->
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                    </section>
                    </div>
                    <div id="approverEmployeeDetails" class="card card-default" style="display: none;">
                        <div class="card-header">

                            <h1 class="card-title" style="color: blue;">Total Rejected Employees Details By Payment
                                Approver:</h1>
                        </div>
                        <div class="card-body">
                            <table class="table table-bordered" id="approverEmployeeTable" style="width: 100%;">
                                <thead>
                                    <tr>
                                        <th>Serail No.</th>
                                        <th>Name</th>
                                        <th>Payee ID</th>
                                        <th>Absentee Month</th>
                                        <th>Reject Remarks</th>
                                        <th>View Data</th>
                                    </tr>
                                </thead>
                                <tbody id="approverEmployeeTbody">
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                <div class="modal fade" id="modalresolved" tabindex="-1" role="dialog" aria-labelledby="modal1">
                    <div class="modal-dialog modal-fullscreen" role="document">
                        <div class="modal-content">
                            <div class="modal-header">
                                <!-- Close button -->
                                <button type="button" class="close close-red" data-dismiss="modal" aria-label="Close">
                                    <span aria-hidden="true">&times;</span>
                                </button>
                            </div>
                            <div class="modal-body">
                                <div class="row">
                                    <!-- Salary Details Section -->
                                    <div class="col-md-10">
                                        <h5><b> Details to Verify By Payment Maker:</b></h5>
                                        <form id="frmupdate" name="frmupdate" action="" method="post"
                                            enctype="multipart/form-data">
                                            <table class="table table-bordered">
                                                <!-- Personal Details Section -->
                                                <thead>
                                                    <tr>
                                                        <th colspan="3"
                                                            style="color: red ; font-weight: bold; font-size: larger;">
                                                            Personal Details to Verify</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <tr>
                                                        <td><b>Pay ID No: </b><input type="text" class="form-control"
                                                                id="salaryNo" readonly>
                                                        </td>
                                                        <td><b>Full Name: </b><input type="text" class="form-control"
                                                                id="fullName" readonly>
                                                        </td>
                                                        <td><b>Currently Posted Department/College: </b><input
                                                                type="text" class="form-control"
                                                                id="currentlyPostedAtUniversityOfficeDepartmentCollege"
                                                                readonly></td>
                                                    </tr>
                                                    <tr>
                                                        <td><b>Bank Account No: </b><input type="text"
                                                                class="form-control" id="bankAccountNo" readonly></td>
                                                        <td><b>Branch Name: </b><input type="text" class="form-control"
                                                                id="branchName" readonly>
                                                        </td>
                                                        <td><b>IFSC Code: </b><input type="text" class="form-control"
                                                                id="ifscCode" readonly>
                                                        </td>
                                                    </tr>
                                                </tbody>
                                                <!-- Salary Details Section -->
                                                <thead>
                                                    <tr>
                                                        <th colspan="3"
                                                            style="color: rgb(21, 0, 255) ; font-weight: bold; font-size: larger;">
                                                            Salary Details to Verify</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <tr>
                                                        <td><b>Pay Level: </b><input type="text" class="form-control"
                                                                id="payLevel" readonly></td>
                                                        <td><b>Basic Salary: </b><input type="text" class="form-control"
                                                                id="basicSalary"></td>
                                                        <td><b style="color: red;">Basic Salary Fixed:
                                                            </b><input type="text" class="form-control"
                                                                id="basicSalaryFixed"></td>
                                                    </tr>
                                                    <tr>
                                                        <td><label for="dapercentage"
                                                                style="color:black; font-weight: bolder;">Choose
                                                                your DA Percentage :</label><select id="daPercentage"
                                                                class="form-control select2">
                                                                <option value="">Select DA Percentage</option>
                                                                <!-- Options from 1 to 50 -->
                                                                <option value="0">0</option>
                                                                <option value="1">1</option>
                                                                <option value="2">2</option>
                                                                <option value="3">3</option>
                                                                <option value="4">4</option>
                                                                <option value="5">5</option>
                                                                <option value="6">6</option>
                                                                <option value="7">7</option>
                                                                <option value="8">8</option>
                                                                <option value="9">9</option>
                                                                <option value="10">10</option>
                                                                <option value="11">11</option>
                                                                <option value="12">12</option>
                                                                <option value="13">13</option>
                                                                <option value="14">14</option>
                                                                <option value="15">15</option>
                                                                <option value="16">16</option>
                                                                <option value="17">17</option>
                                                                <option value="18">18</option>
                                                                <option value="19">19</option>
                                                                <option value="20">20</option>
                                                                <option value="21">21</option>
                                                                <option value="22">22</option>
                                                                <option value="23">23</option>
                                                                <option value="24">24</option>
                                                                <option value="25">25</option>
                                                                <option value="26">26</option>
                                                                <option value="27">27</option>
                                                                <option value="28">28</option>
                                                                <option value="29">29</option>
                                                                <option value="30">30</option>
                                                                <option value="31">31</option>
                                                                <option value="32">32</option>
                                                                <option value="33">33</option>
                                                                <option value="34">34</option>
                                                                <option value="35">35</option>
                                                                <option value="36">36</option>
                                                                <option value="37">37</option>
                                                                <option value="38">38</option>
                                                                <option value="39">39</option>
                                                                <option value="40">40</option>
                                                                <option value="41">41</option>
                                                                <option value="42">42</option>
                                                                <option value="43">43</option>
                                                                <option value="44">44</option>
                                                                <option value="45">45</option>
                                                                <option value="46">46</option>
                                                                <option value="47">47</option>
                                                                <option value="48">48</option>
                                                                <option value="49">49</option>
                                                                <option value="50">50</option>
                                                            </select></td>
                                                        <td><b>DA: </b><input type="text" class="form-control" id="da"
                                                                readonly></td>
                                                        <td><b>HRA Percentage: </b><select id="HRAPercentage"
                                                                class="form-control select2">
                                                                <option value="">Select HRA Percentage</option>
                                                                <!-- Options from 1 to 50 -->
                                                                <option value="0">0</option>
                                                                <option value="1">1</option>
                                                                <option value="2">2</option>
                                                                <option value="3">3</option>
                                                                <option value="4">4</option>
                                                                <option value="5">5</option>
                                                                <option value="6">6</option>
                                                                <option value="7">7</option>
                                                                <option value="8">8</option>
                                                                <option value="9">9</option>
                                                                <option value="10">10</option>
                                                                <option value="11">11</option>
                                                                <option value="12">12</option>
                                                                <option value="13">13</option>
                                                                <option value="14">14</option>
                                                                <option value="15">15</option>
                                                                <option value="16">16</option>
                                                                <option value="17">17</option>
                                                                <option value="18">18</option>
                                                                <option value="19">19</option>
                                                                <option value="20">20</option>
                                                                <option value="21">21</option>
                                                                <option value="22">22</option>
                                                                <option value="23">23</option>
                                                                <option value="24">24</option>
                                                                <option value="25">25</option>
                                                                <option value="26">26</option>
                                                                <option value="27">27</option>
                                                                <option value="28">28</option>
                                                                <option value="29">29</option>
                                                                <option value="30">30</option>
                                                                <option value="31">31</option>
                                                                <option value="32">32</option>
                                                                <option value="33">33</option>
                                                                <option value="34">34</option>
                                                                <option value="35">35</option>
                                                                <option value="36">36</option>
                                                                <option value="37">37</option>
                                                                <option value="38">38</option>
                                                                <option value="39">39</option>
                                                                <option value="40">40</option>
                                                                <option value="41">41</option>
                                                                <option value="42">42</option>
                                                                <option value="43">43</option>
                                                                <option value="44">44</option>
                                                                <option value="45">45</option>
                                                                <option value="46">46</option>
                                                                <option value="47">47</option>
                                                                <option value="48">48</option>
                                                                <option value="49">49</option>
                                                                <option value="50">50</option>
                                                            </select></td>
                                                    <tr>
                                                        <td><b>HRA: </b><input type="text" class="form-control" id="hra"
                                                                readonly></td>
                                                        <td><b>CTA: </b><input type="text" class="form-control"
                                                                id="cta"></td>
                                                    </tr>
                                                    </tr>
                                                    <tr>
                                                        <td><b>Medical Allowance: </b><input type="text"
                                                                class="form-control" id="medicalAllowance"></td>
                                                        <td><b>Special Other Allowance: </b><input type="text"
                                                                class="form-control" id="specialOtherAllowance">
                                                        </td>
                                                        <td><b>Last Payment Withdrawn Salary: </b><input type="text"
                                                                class="form-control" id="lastPaymentWithdrawnSalary">
                                                        </td>
                                                    </tr>
                                                    <tr>

                                                        <td><b style="color: red;">Last Payment Withdrawn Salary
                                                                Deduction: </b><input type="text" class="form-control"
                                                                id="lastPaymentWithdrawnSalarydeduction"></td>

                                                        <td><b style="color: red;">Gross Salary (Before
                                                                Deduction) </b><input type="text" class="form-control"
                                                                id="grosssalary"></td>
                                                        <td><b style="color: red;">Net Pay Salary (After All
                                                                Deduction)</b><input type="text" class="form-control"
                                                                id="netpaysalary"></td>
                                                    </tr>
                                                </tbody>
                                                <!-- Deduction Details Section -->
                                                <thead>
                                                    <tr>
                                                        <th colspan="3"
                                                            style="color: crimson ; font-weight: bold; font-size: larger;">
                                                            Salary Deduction Details to Verify</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <tr>
                                                        <td><b>Income Tax: </b><input type="text" class="form-control"
                                                                id="incomeTax"></td>
                                                        <td><b>P.F: </b><input type="text" class="form-control" id="pf">
                                                        </td>
                                                        <td><b>P.F. Loan: </b><input type="text" class="form-control"
                                                                id="pfLoan"></td>
                                                    </tr>
                                                    <tr>
                                                        <td><b>NPS Opted: </b><input type="text" class="form-control"
                                                                id="npsOpted"></td>
                                                        <td><b>NPS: </b><input type="text" class="form-control"
                                                                id="nps"></td>
                                                        <td><b>Professional Tax: </b><input type="text"
                                                                class="form-control" id="professionalTax"></td>
                                                    </tr>

                                                    <tr>
                                                        <td><b>L.I.C: </b><input type="text" class="form-control"
                                                                id="lic"></td>
                                                        <td><b>G.I.P: </b><input type="text" class="form-control"
                                                                id="gip"></td>
                                                        <td><b>Any Other Deduction: </b><input type="text"
                                                                class="form-control" id="otherdeduction"></td>
                                                    </tr>
                                                    <tr>
                                                        <td><b> Other Deduction1: </b><input type="text"
                                                                class="form-control" id="otherDeduction1"></td>
                                                        <td><b> Other Deduction2: </b><input type="text"
                                                                class="form-control" id="otherDeduction2"></td>
                                                        <td><b> Other Deduction3: </b><input type="text"
                                                                class="form-control" id="otherDeduction3"></td>
                                                        <input type="hidden" id="absentee_date" name="absentee_date">
                                                        <input type="hidden" id="annualOptedDate"
                                                            name="annualOptedDate">
                                                    </tr>
                                                </tbody>
                                                <tr>
                                                    <td colspan="5">
                                                        <p
                                                            style="font-style: italic; color:rgb(128, 0, 0); font-size: larger;">
                                                            Note: Please Verify All Payment Details and then Submit
                                                            Request To Payment Maker

                                                        </p>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td colspan="5" align="center">
                                                        <button type="button" class="btn btn-primary"
                                                            id="btnsubmit" name="btnsubmit">Submit Request</button>
                                                    </td>
                                                </tr>
                                            </table>
                                        </form>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
    </div>
    <script>
        $.widget.bridge("uibutton", $.ui.button);
    </script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- jQuery UI -->
    <script src="https://code.jquery.com/ui/1.12.1/jquery-ui.min.js"></script>
    <!-- Bootstrap 4 -->
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.bundle.min.js"></script>
    <!-- Select2 -->
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
    <!-- Moment.js -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.1/moment.min.js"></script>
    <!-- Date Range Picker -->
    <script src="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.min.js"></script>
    <!-- Tempusdominus Bootstrap 4 -->
    <script
        src="https://cdnjs.cloudflare.com/ajax/libs/tempusdominus-bootstrap-4/5.1.2/js/tempusdominus-bootstrap-4.min.js"></script>
    <!-- OverlayScrollbars -->
    <script
        src="https://cdnjs.cloudflare.com/ajax/libs/overlayscrollbars/1.13.0/js/jquery.overlayScrollbars.min.js"></script>
    <!-- AdminLTE App -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/admin-lte/3.2.0/js/adminlte.min.js"></script>
    <!-- jQuery Validation -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-validate/1.19.3/jquery.validate.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-validate/1.19.3/additional-methods.min.js"></script>
    <!-- Toastr -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/js/toastr.min.js"></script>
    <!-- SweetAlert2 -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <!-- boxicon -->
    <script src="https://unpkg.com/boxicons@2.1.4/dist/boxicons.js"></script>
    <script>
        var apiBaseUrl = "<%= process.env.apiBaseUrl %>";
        const token = localStorage.getItem("access-token-new");
        const userData = localStorage.getItem("user-data");
        const college = usersData.college;
        const university = usersData.university;
        $('#college').text(college);
        $('#college1').text(college);
        $('#university1').text(university);
        function viewData(payIdNo, absenteeDate) {
            $.ajax({
                url: apiBaseUrl + "v1/maker/get-sallary-details",
                method: "POST",
                contentType: "application/json",
                data: JSON.stringify({
                    payIdNo: payIdNo,
                    date: absenteeDate
                }),
                headers: {
                    "token": localStorage.getItem("access-token-new")
                },
                success: function (response) {
                    const salaryDetails = response.data.data;
                    // Reset HRA,DA,CTA percentage 
                    $('#ctapercentage').val('').trigger('change');
                    $('#daPercentage').val('').trigger('change');
                    $('#HRAPercentage').val('').trigger('change');
                    $('#salaryNo').val(salaryDetails.payIdNo);
                    $('#fullName').val(salaryDetails.full_name);
                    $('#gender').val(salaryDetails.gender);
                    $('#dateOfBirth').val(salaryDetails.dateOfBirth);
                    $('#aadharCardNo').val(salaryDetails.aadharCardNo);
                    $('#employeeType').val(salaryDetails.employeeType);
                    $('#dateOfRetirement').val(salaryDetails.dateOfRetirement);
                    $('#basicSalaryFixed').val(salaryDetails.basic_salary_fixed);
                    $('#currentlyPostedAtUniversityOfficeDepartmentCollege').val(salaryDetails.university_name || salaryDetails.college_name);
                    $('#bankAccountNo').val(salaryDetails.bank_account_no);
                    $('#bankName').val(salaryDetails.bank_name);
                    $('#branchName').val(salaryDetails.branch_name);
                    $('#ifscCode').val(salaryDetails.ifsc_code);
                    $('#payLevel').val(salaryDetails.payLevel);
                    $('#basicSalary').val(salaryDetails.basic_salary);
                    $('#hra').val(salaryDetails.hr);
                    $('#cta').val(salaryDetails.cta);
                    $('#da').val(salaryDetails.da);
                    $('#medicalAllowance').val(salaryDetails.medical_allowance);
                    $('#npsOpted').val(salaryDetails.nps_opted);
                    $('#nps').val(salaryDetails.nps);
                    $('#lastPaymentWithdrawnSalary').val(salaryDetails.last_payment_withdrawn_sallary);
                    $('#lastPaymentWithdrawnSalarydeduction').val(salaryDetails.last_payment_withdrwan_sallary_deduction);
                    $('#annualOptedDate').val(salaryDetails.annual_opted_date);
                    $('#lic').val(salaryDetails.lic);
                    $('#pf').val(salaryDetails.pf);
                    $('#pfLoan').val(salaryDetails.pfLoan);
                    $('#absentee_date').val(salaryDetails.absentee_date);
                    $('#gip').val(salaryDetails.gip);
                    $('#incomeTax').val(salaryDetails.income_tax);
                    $('#netpaysalary').val(salaryDetails.net_pay_sallary);
                    $('#grosssalary').val(salaryDetails.gross_sallary);
                    $('#professionalTax').val(salaryDetails.professional_tax);
                    $('#specialOtherAllowance').val(salaryDetails.special_other_allowance ? salaryDetails.special_other_allowance : "0");
                    $('#otherDeduction1').val(salaryDetails.other_deduction1);
                    $('#otherDeduction2').val(salaryDetails.other_deduction2);
                    $('#otherDeduction3').val(salaryDetails.other_deduction3);


                    $('#modalresolved').modal('show');
                },
                error: function (error) {
                    alert("An error occurred while fetching absentee details.");
                }
            });
        }
        // function roundOff(value) {
        //     return Math.round(value);
        // }
        // function calculateDA() {
        //     var basicSalary = parseFloat($('#basicSalary').val());
        //     var daPercentage = parseFloat($('#daPercentage').val());

        //     if (!isNaN(basicSalary) && !isNaN(daPercentage)) {
        //         var daAmount = (basicSalary * daPercentage) / 100;
        //         $('#da').val(roundOff(daAmount)); // Apply round-off
        //     } else {
        //         $('#da').val('');
        //     }

        //     calculateGrossSalary(); // Recalculate Gross Salary
        // }

        // function calculateHRA() {
        //     var basicSalary = parseFloat($('#basicSalary').val());
        //     var hraPercentage = parseFloat($('#HRAPercentage').val());

        //     if (!isNaN(basicSalary) && !isNaN(hraPercentage)) {
        //         var hraAmount = (basicSalary * hraPercentage) / 100; // Now calculated from basicSalary
        //         $('#hra').val(roundOff(hraAmount)); // Apply round-off
        //     } else {
        //         $('#hra').val('');
        //     }

        //     calculateGrossSalary(); // Recalculate Gross Salary
        // }

        // function calculateGrossSalary() {
        //     var basicSalary = parseFloat($('#basicSalary').val());
        //     var da = parseFloat($('#da').val());
        //     var hra = parseFloat($('#hra').val());
        //     var cta = parseFloat($('#cta').val()); // CTA is entered directly now
        //     var medicalAllowance = parseFloat($('#medicalAllowance').val());
        //     var specialOtherAllowance = parseFloat($('#specialOtherAllowance').val());
        //     var incomeTax = parseFloat($('#incomeTax').val());
        //     var pf = parseFloat($('#pf').val());
        //     var pfLoan = parseFloat($('#pfLoan').val());
        //     var nps = parseFloat($('#nps').val());
        //     var professionalTax = parseFloat($('#professionalTax').val());
        //     var lic = parseFloat($('#lic').val());
        //     var gip = parseFloat($('#gip').val());
        //     var otherDeduction = parseFloat($('#otherdeduction').val());
        //     // Ensure all deduction values are valid numbers
        //     var totalDeductions = (incomeTax || 0) + (pf || 0) + (pfLoan || 0) + (nps || 0) + (professionalTax || 0) + (lic || 0) + (gip || 0) + (otherDeduction || 0);
        //     // Ensure all the components of the salary are valid numbers
        //     if (!isNaN(basicSalary) && !isNaN(da) && !isNaN(hra) && !isNaN(cta) && !isNaN(medicalAllowance) && !isNaN(specialOtherAllowance)) {
        //         var grossSalary = basicSalary + da + hra + cta + medicalAllowance + specialOtherAllowance;
        //         $('#grosssalary').val(roundOff(grossSalary)); // Apply round-off

        //         var netSalary = grossSalary - totalDeductions;
        //         $('#netpaysalary').val(roundOff(netSalary)); // Apply round-off
        //     } else {
        //         $('#grosssalary').val('');
        //         $('#netpaysalary').val('');
        //     }
        // }
        // $('#basicSalary, #daPercentage').on('input change', calculateDA);
        // $('#HRAPercentage').on('input change', calculateHRA);
        // $('#cta').on('input change', calculateGrossSalary); // CTA is entered directly now
        // $('#medicalAllowance, #specialOtherAllowance, #incomeTax, #pf, #pfLoan, #nps, #professionalTax, #lic, #gip, #otherdeduction').on('input change', calculateGrossSalary);
    $(function () {
    var apiBaseUrl = "<%= process.env.apiBaseUrl %>";
    $.ajax({
        url: apiBaseUrl + "v1/maker/rejected-dashboard",
        method: 'POST',
        headers: {
            token: localStorage.getItem("access-token-new"),
            "Content-Type": "application/json"
        },
        success: function (res) {
            $("#rejectedemployeechecker").text(res.data.checkerData.totalCheckerPendingData);
            $("#rejectedemployeeapprover").text(res.data.approverData.totalApproverPendingData);
        },
        error: function (error) {
            console.error('API call failed:', error);
        }
    });
    $('#checkerEmployeeLink').on('click', function (e) {
        e.preventDefault();
        $('#approverEmployeeDetails').hide();
        $('.loader').show();
        $.ajax({
            url: apiBaseUrl + "v1/maker/rejected-dashboard",
            type: 'POST',
            contentType: 'application/json',
            headers: {
                token: localStorage.getItem("access-token-new"),
                "Content-Type": "application/json"
            },
            beforeSend: function () {
                $('.loader').show();
            },
            success: function (response) {
                $('.loader').hide();
                var checkerData = response.data.checkerData;
                $('#rejectedemployeechecker').text(checkerData.totalCheckerPendingData);
                $('#checkerEmployeeTbody').empty();
                checkerData.indiCheckerPendingData.forEach(function (employee, index) {
                    var row = '<tr>' +
                        '<td>' + (index + 1) + '</td>' +
                        '<td>' + (employee.full_name || "N/A") + '</td>' +
                        '<td>' + employee.payIdNo + '</td>' +
                        '<td>' + employee.absentee_date + '</td>' +
                        '<td>' + employee.checker_remarks + '</td>' +
                        '<td>' +
                        '<button ' +
                        'type="button" ' +
                        'class="btn btn-primary" ' +
                        'onclick="viewData(\'' + employee.payIdNo + '\', \'' + employee.absentee_date + '\')">' +
                        'View' +
                        '</button>' +
                        '</td>' +
                        '</tr>';
                    $('#checkerEmployeeTbody').append(row);
                });
                $('#checkerEmployeeDetails').show();
                $('#approverEmployeeDetails').hide();
            },
            error: function (xhr, status, error) {
                console.error('AJAX Error: ' + status + error);
            }
        });
    });
    $('#approverEmployeeLink').on('click', function (e) {
        e.preventDefault();
        $('#checkerEmployeeDetails').hide();
        $('.loader').show();
        $.ajax({
            url: apiBaseUrl + "v1/maker/rejected-dashboard",
            type: 'POST',
            contentType: 'application/json',
            headers: {
                token: localStorage.getItem("access-token-new"),
                "Content-Type": "application/json"
            },
            beforeSend: function () {
                $('.loader').show();
            },
            success: function (response) {
                $('.loader').hide();
                var approverData = response.data.approverData;
                $('#rejectedemployeeapprover').text(approverData.totalApproverPendingData);
                $('#approverEmployeeTbody').empty();

                approverData.indiApproverPendingData.forEach(function (employee, index) {
                    var row = '<tr>' +
                        '<td>' + (index + 1) + '</td>' +
                        '<td>' + (employee.full_name || "N/A") + '</td>' +
                        '<td>' + employee.payIdNo + '</td>' +
                        '<td>' + employee.absentee_date + '</td>' +
                        '<td>' + employee.checker_remarks + '</td>' +
                        '<td>' +
                        '<button ' +
                        'type="button" ' +
                        'class="btn btn-primary" ' +
                        'onclick="viewData(\'' + employee.payIdNo + '\', \'' + employee.absentee_date + '\')">' +
                        'View' +
                        '</button>' +
                        '</td>' +
                        '</tr>';
                    $('#approverEmployeeTbody').append(row);
                });

                $('#approverEmployeeDetails').show();
                $('#checkerEmployeeDetails').hide();
            },
            error: function (xhr, status, error) {
                console.error('AJAX Error: ' + status + error);
            }
        });
    });
    $(document).ready(function () {
        $('#btnsubmit').on('click', function (event) {
            event.preventDefault();
            console.log('Button clicked');
            const data = {
                payIdNo: $('#salaryNo').val(),
                basicSalary: $('#basicSalary').val(),
                basicSalaryFixed: $('#basicSalaryFixed').val(),
                hr: $('#hra').val(),
                cta: $('#cta').val(),
                da: $('#da').val(),
                medicalAllowance: $('#medicalAllowance').val(),
                npsOpted: $('#npsOpted').val(),
                nps: $('#nps').val(),
                specialOtherAllowance: $('#specialOtherAllowance').val(),
                incomeTax: $('#incomeTax').val(),
                pf: $('#pf').val(),
                pfLoan: $('#pfLoan').val(),
                lic: $('#lic').val(),
                gip: $('#gip').val(),
                lastPaymentWithdrawnSalary: $('#lastPaymentWithdrawnSalary').val(),
                lastPaymentWithdrawnSalaryDeduction: $('#lastPaymentWithdrawnSalarydeduction').val(),
                absenteeDate: $('#absentee_date').val(),
                otherDeduction: $('#otherdeduction').val() || '',
                grossSalary: $('#grosssalary').val() || '',
                netPaySalary: $('#netpaysalary').val() || '',
                professionalTax: $('#professionalTax').val() || '',
                ctaPercentage: $('#ctapercentage').val() || '',
                daPercentage: $('#daPercentage').val() || '',
                hraPercentage: $('#HRAPercentage').val() || '',
                otherDeduction1: $('#otherDeduction1').val() || '',
                otherDeduction2: $('#otherDeduction2').val() || '',
                otherDeduction3: $('#otherDeduction3').val() || '',
                annualOptedDate: $('#annualOptedDate').val()
            };
            $.ajax({
                url: apiBaseUrl + "v1/maker/update-rejected-data",
                type: 'POST',
                contentType: 'application/json',
                data: JSON.stringify(data),
                headers: {
                    "token": localStorage.getItem("access-token-new")
                },
                success: function (response) {
                    Swal.fire({
                        icon: 'success',
                        title: 'Success',
                        text: 'Request submitted successfully to Payment Checker!',
                    });
                    $('#modalresolved').modal('hide');

                    setTimeout(function () {
                        location.reload();
                    }, 3000);
                },
                error: function (error) {
                    alert('Error submitting request.');
                    console.log(error);
                }
            });
        });
    });
});
    </script>
    <script>
        function isTokenExpired(token) {
            if (!token) return true;
            try {
                // Decode JWT payload
                const payloadBase64 = token.split('.')[1];
                const payload = JSON.parse(atob(payloadBase64));
                // Check if current time is past the expiration time
                const isExpired = payload.exp * 1000 < Date.now();
                return isExpired;
            } catch (error) {
                console.error("Failed to decode token:", error);
                return true;
            }
        }
        // Check token expiration on load
        $(function () {
            const token = localStorage.getItem("access-token-new");
            if (token && userData) {
                // Check role and token expiration
                if (usersData.role !== "Maker-Login" || isTokenExpired(token)) {
                    alert("Session expired or unauthorized access. Redirecting to login.");
                    window.location = "/";
                } else {
                    setInterval(() => {
                        if (isTokenExpired(token)) {
                            alert("Session expired. Please login again.");
                            window.location = "/";
                        }
                    }, 60000);
                }
            } else {
                window.location = "/";
            }
        });
    </script>