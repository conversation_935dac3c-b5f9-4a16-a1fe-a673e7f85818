<!DOCTYPE html>
<html lang="en">

<head>
	<meta charset="UTF-8" />
	<meta http-equiv="X-UA-Compatible" content="IE=edge" />
	<meta name="viewport" content="width=device-width, initial-scale=1.0" />
	<title>
		<%= title %>
	</title>

	<link rel="stylesheet" href="/stylesheets/reset-password.css" />
	<!-- Google Font: Source Sans Pro -->
	<link rel="stylesheet"
		href="https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,400i,700&display=fallback" />
	<!-- Font Awesome -->
	<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css" />
	<!-- Ionicons -->
	<link rel="stylesheet" href="https://code.ionicframework.com/ionicons/2.0.1/css/ionicons.min.css" />

	<!-- Tempusdominus Bootstrap 4 -->
	<link rel="stylesheet"
		href="https://cdnjs.cloudflare.com/ajax/libs/tempusdominus-bootstrap-4/5.39.0/css/tempusdominus-bootstrap-4.min.css" />

	<!-- iCheck -->
	<link rel="stylesheet"
		href="https://cdnjs.cloudflare.com/ajax/libs/icheck-bootstrap/3.0.1/icheck-bootstrap.min.css" />
	<!-- Select2 -->
	<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
	<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/select2@4.0.13/dist/css/select2.min.css" />
	<!-- Theme style -->
	<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/admin-lte/3.1.0/css/adminlte.min.css" />
</head>
<nav class="header fixed-top">
	<div class="container-fluid no-padding d-flex justify-content-between align-items-center">
		<div class="logo-left">
			<img src="/images/BIHARSARKAR.png" alt="Right Logo" height="90px" width="100px">
		</div>
		<div class="logo-center">
			<div class="logo-center  mt-2">
				<div class="text-center">
					<h3 id="h1"><a href="/" style="font-size: larger; font-weight: bolder;">EDUCATION DEPARTMENT</a>
					</h3>
					<h3 id="h1"><a href="/" style="font-size: larger; font-weight: bolder;">शिक्षा विभाग</a></h3>
				</div>
			</div>
		</div>
		<div class="logo-right">
			<img src="/images/gov1.png" alt="Left Logo" height="90px" width="80px">
		</div>
	</div>
</nav>
<body class="hold-transition login-page">
	<div class="login-box">
		<!-- /.login-logo -->
		<div id="step1" >
			<div class="card card-outline card-primary"> 
<form class="form" id="frmlogin" name="frmlogin" method="post">
    <p class="title">Reset Password </p>
    <p class="message" style="color: red; font-size: 18px;">OTP is Required To Reset Your Password . </p>  
    <label>
        <input required="" placeholder="" type="text" class="input" id="username" name="username">
        <span>Username</span>
    </label>    
    <label>
        <input required="" placeholder="" type="password" id="password" name="password" class="input" >
        <span>Old Password</span>
    </label>
    <button class="submit" type="submit" id="btnlogin" name="btnlogin">Send OTP</button>
</form>
		</div>
</div>
		<div id="step2" style="display: none;">
			<form action="" id="frmotp" name="frmotp" method="post">
				<span class="mainHeading">Enter OTP For Verification</span>
				<p class="otpSubheading">We have sent a 6 digit otp code to your mobile number</p>
				<div class="input-wrapper">
					<input type="number" class="form-control" id="otpbox" name="otpbox" placeholder="Enter 6 digit otp"
						onKeyPress="if(this.value.length==6) return false;" />
			
				</div>
				<button type="submit" id="btnverify" name="btnverify" class="btn btn-primary btn-block">
					Verify OTP
				</button>
				<p class="resendNote">Didn't receive the code? <button type="button" class="resendBtn">Resend
						Code</button></p>
                        <input type="hidden" name="university" id="university" />
			</form>
		</div>
	</div>

                <div id="step3" style="display: none;">
                    <div class="container">
                        <form class="form" id="frmpassword" name="frmpassword" action="">
                        <div class="heading">Enter Your New Password:</div>
                    				
                            <div class="input-field">
                                <input
                                    required
                                    autocomplete="off"
                                    type="password"
                                    name="setpassword"
                                    id="setpassword"
                                    onkeyup="validatePasswordStrength()"
                                />
                                <label for="setpassword">Set New Password</label>
                                <p id="password-strength"></p> <!-- Display password strength -->
                            </div>
                            <div class="input-field">
                                <input
                                    required
                                    autocomplete="off"
                                    type="text"
                                    name="confirmpassword"
                                    id="confirmpassword"
                                />
                                <label for="confirmpassword">Confirm New Password</label>
                                <p id="password-match"></p> <!-- Display password match status -->
                            </div>
                            

                            <div class="btn-container">
                                <button type="submit" class="btn" onclick="validateForm()">Final Submit</button>
                            </div>
            
                </form>
                </div>
        </div>


	<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
	<script src="https://cdnjs.cloudflare.com/ajax/libs/twitter-bootstrap/4.6.0/js/bootstrap.bundle.min.js"></script>
	<script src="https://cdnjs.cloudflare.com/ajax/libs/admin-lte/3.1.0/js/adminlte.min.js"></script>
	<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-validate/1.19.3/jquery.validate.min.js"></script>
	<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-validate/1.19.3/additional-methods.min.js"></script>
	<script src="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.js"></script>
             <!-- SweetAlert2 -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script>
function validatePasswordStrength() {
        const password = document.getElementById("setpassword").value;
        const strengthText = document.getElementById("password-strength");
        const strengthRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/;
        
        if (strengthRegex.test(password)) {
            strengthText.textContent = "Strong Password";
            strengthText.style.color = "green";
        } else {
            strengthText.textContent = "Weak Password.";
            strengthText.style.color = "red";
        }
    }
    function validateForm() {
    const password = document.getElementById("setpassword").value;
    const confirmPassword = document.getElementById("confirmpassword").value;
    const matchText = document.getElementById("password-match");
    
    // Check if passwords match
    if (password === confirmPassword) {
        matchText.textContent = "Passwords match";
        matchText.style.color = "green";
        if (document.getElementById("password-strength").textContent === "Strong Password") {
            return true;
        } else {
            alert("Password is not Strong enough Please choose a Stronger password");
            return false; // Stop form submission if password is weak
        }
    } else {
        matchText.textContent = "Passwords do not match";
        matchText.style.color = "red";
        return false; // Stop form submission if passwords do not match
    }
}
    </script>
	<script>
		var apiBaseUrl = "<%= process.env.apiBaseUrl %>";
	
		$(function () {
			$("#frmlogin").validate({
				rules: {
					username: {
						required: true,
					},
                    password: {
						required: true,
					},
				},
				messages: {
					username: {
						required: "Please enter username",
					},
                    password: {
						required: "Please enter password",
					},
				},
				errorElement: "span",
				errorPlacement: function (error, element) {
					error.addClass("invalid-feedback");
					element.closest(".input-group").append(error);
				},
				highlight: function (element, errorClass, validClass) {
					$(element).addClass("is-invalid");
				},
				unhighlight: function (element, errorClass, validClass) {
					$(element).removeClass("is-invalid");
				},
			});
			$("#frmotp").validate({
				rules: {
					otpbox: {
						required: true,
						digits: true,
						minlength: 6,
						maxlength: 6
					},
				},
				messages: {
					otpbox: {
						required: "Please enter a six-digit OTP",
						digits: "Please enter only digits",

					},
				},

				errorElement: "span",
				errorPlacement: function (error, element) {
					error.addClass("invalid-feedback");
					element.closest(".input-group").append(error);
				},
				highlight: function (element, errorClass, validClass) {
					$(element).addClass("is-invalid");
				},
				unhighlight: function (element, errorClass, validClass) {
					$(element).removeClass("is-invalid");
				},
			});

            $("#frmpassword").validate({
				rules: {
					setpassword: {
						required: true,
					},
                    confirmpassword: {
						required: true,
					},
				},
				messages: {
					setpassword: {
						required: "Please enter new password",
					},
                    confirmpassword: {
						required: "Enter Confirm new password",
					},
				},
				errorElement: "span",
				errorPlacement: function (error, element) {
					error.addClass("invalid-feedback");
					element.closest(".input-group").append(error);
				},
				highlight: function (element, errorClass, validClass) {
					$(element).addClass("is-invalid");
				},
				unhighlight: function (element, errorClass, validClass) {
					$(element).removeClass("is-invalid");
				},
			});

	$("#frmlogin").on("submit", function (event) {
    event.preventDefault();
    username = event.target["username"].value;
    password = event.target["password"].value;

    // Assuming you have jQuery validation plugin set up
    if ($("#frmlogin").valid()) {
        $.ajax({
            url: apiBaseUrl + "v1/auth/login-otp",
            method: "POST",
            headers: {
                "Content-Type": "application/json",
            },
            data: JSON.stringify({
                userId: username,
                orignalPassword: password,
            }),
            dataType: "JSON",

            success: function (res) {
                console.log(res);
                if (res.status == "success") {
                    $("#step1").hide();   // Hides step 1
                    $("#step2").show();   // Shows step 2
                    $(document).Toasts("create", {
                        class: "bg-success",
                        autohide: true,
                        delay: 2000,
                        title: "Success",
                        body: "OTP sent successfully",
                    });
                }
            },
            error: function (res) {
                $("#step1").show();
                $("#step2").hide();
                $(document).Toasts("create", {
                    class: "bg-danger",
                    autohide: true,
                    delay: 2000,
                    title: "Error",
                    body: `${res.responseJSON.message}`,
                });
            },
        });
    }
});
$("#frmotp").on("submit", function (event) {
				event.preventDefault();
				otp = event.target["otpbox"].value;

				if ($("#frmotp").valid()) {
					$.ajax({
						url: apiBaseUrl + "v1/auth/verify-login-otp",
						method: "POST",
						headers: {
							"Content-Type": "application/json",
						},
						data: JSON.stringify({
							otp: otp,
							university: $("#username").val(),
						}),
						dataType: "JSON",
					success: function (res) {
                console.log(res);
                if (res.status == "success") {
                if (res.data) {
			     localStorage.setItem("access-token-password", res.data.token);
				
                }
                    $("#step1").hide();   // Hides step 1
                    $("#step2").hide();
                    $("#step3").show();
                    $("#university").val(res.data.data.department_username);
                  
                    $(document).Toasts("create", {
                        class: "bg-success",
                        autohide: true,
                        delay: 2000,
                        title: "Success",
                        body: "OTP verified successfully",
                    });
                }
            },
            error: function (res) {
                $("#step1").show();
                $("#step2").hide();
                $(document).Toasts("create", {
                    class: "bg-danger",
                    autohide: true,
                    delay: 2000,
                    title: "Error",
                    body: `${res.responseJSON.message}`,
                });
            },
        });
    }
});
// Form submission handler
$("#frmpassword").on("submit", function (event) {
    event.preventDefault();
    
    // Trigger the validation function before proceeding
    if (validateForm()) {
        let setPassword = event.target["setpassword"].value;
   
        if ($("#frmpassword").valid()) {
            $.ajax({
                url: apiBaseUrl + "v1/auth/reset-password",
                method: "POST",
                headers: {
                "token": localStorage.getItem("access-token-password"),
                "Content-Type": "application/json",
                                        },
                data: JSON.stringify({
                    password: setPassword, 
                    userId:$("#university").val(),
                }),
                dataType: "JSON",
                success: function (res) {
                    if (res.status === "success") {
                        Swal.fire({
                            icon: 'success',
                            title: 'Password Changed Successfully!',
                            text: 'Your password has been Changed successfully.',
                        }).then((result) => {
                            if (result.isConfirmed) {
                                // Redirect to login page after success
                                window.location.href = "/";
                            }
                        });
                    } else {
                        Swal.fire({
                            icon: 'error',
                            title: 'Invalid OTP!',
                            text: 'Please try again.',
                        });
                    }
                },
                error: function () {
                    Swal.fire({
                        icon: 'error',
                        title: 'Error',
                        text: 'Something went wrong. Please try again later.',
                    });
                }
            });
        }
}
	});
		});
	</script>
</body>

</html>