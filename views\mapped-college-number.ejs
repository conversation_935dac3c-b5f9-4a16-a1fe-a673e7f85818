<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>
        <%= title %>
    </title>
    <!-- Google Font: Source Sans Pro -->
    <link rel="stylesheet"
        href="https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,400i,700&display=fallback" />

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css" />

    <!-- Ionicons -->
    <link rel="stylesheet" href="https://code.ionicframework.com/ionicons/2.0.1/css/ionicons.min.css" />


    <!-- Tempusdominus Bootstrap 4 -->
    <link rel="stylesheet"
        href="https://cdnjs.cloudflare.com/ajax/libs/tempusdominus-bootstrap-4/5.39.0/css/tempusdominus-bootstrap-4.min.css" />

    <!-- iCheck -->
    <link rel="stylesheet"
        href="https://cdnjs.cloudflare.com/ajax/libs/icheck-bootstrap/3.0.1/icheck-bootstrap.min.css" />

    <!-- Select2 -->
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/select2@4.0.13/dist/css/select2.min.css" />

    <!-- Theme style -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/admin-lte/3.1.0/css/adminlte.min.css" />

    <!-- overlayScrollbars -->
    <link rel="stylesheet"
        href="https://cdnjs.cloudflare.com/ajax/libs/overlayscrollbars/1.13.0/css/OverlayScrollbars.min.css" />



    <!-- Baoxicon -->
    <link href='https://unpkg.com/boxicons@2.1.4/css/boxicons.min.css' rel='stylesheet'>
    <style type="text/css">
        input::-webkit-outer-spin-button,
        input::-webkit-inner-spin-button {
            -webkit-appearance: none;
            margin: 0;
        }

        #h1 {

            margin-top: -60px;
            padding: 3px;
        }
    </style>
</head>

<body class="hold-transition sidebar-mini layout-fixed sidebar-collapse">
    <div class="wrapper">
        <!-- Preloader -->
        <div class="preloader flex-column justify-content-center align-items-center">
            <img class="animation__shake" src="/images/loader.gif" alt="CodeBucket" height="60" width="60" />
        </div>

        <!-- Navbar -->
        <%- include('partials/header'); %>
            <!-- /.navbar -->

            <!-- Main Sidebar Container -->
            <%- include('partials/sidebar'); %>

                <!-- Content Wrapper. Contains page content -->
                <div class="content-wrapper">
                    <!-- Content Header (Page header) -->
                    <div class="content-header">
                        <div class="container-fluid">
                            <div class="row mb-2">

                                <!-- /.col -->

                                <!-- /.col -->
                            </div>
                            <!-- /.row -->
                        </div>
                        <!-- /.container-fluid -->
                    </div>
                    <section class="content">
                        <div class="container-fluid">
                            <div class="row">
                                <section class="col-12">
                                    <div class="container">
                                        <div class="row">
                                            <div class="col-sm-9 mt-5">
                                                <h2 id="h1" class="alert alert-danger text-center">Please Urgently provide the details for OTP login and password change for the college.</h2>
                                                <div class="mb-3 mt-5">
                                                    <label for="firstName" class="form-label">Full Name:</label>
                                                    <input type="text" class="form-control" id="firstName"
                                                        name="firstName" placeholder="Enter Full Name"
                                                        onkeyup="this.value = this.value.toUpperCase();" required>
                                                </div>

                                                <div class="mb-3">
                                                    <label for="designation" class="form-label">Enter
                                                        Designation:</label>
                                                    <input type="email" class="form-control" id="designation"
                                                        name="designation" placeholder="Enter Designation"
                                                        onkeyup="this.value = this.value.toUpperCase();" required>
                                                </div>
                                                <div class="mb-3">
                                                    <label for="phone" class="form-label">Mobile Number For OTP:</label>
                                                    <input type="number" class="form-control" id="phone" name="phone"
                                                        oninput="javascript: if (this.value.length > this.maxLength) this.value = this.value.slice(0, this.maxLength);"
                                                        maxlength="10" placeholder="Enter Mobile Number For OTP" required>
                                                </div>



                                                <div class="mb-3">
                                                    <button id="submitBtn" type="submit" class="btn btn-primary">SUBMIT
                                                        DETAILS </button>
                                                </div>

                                            </div>
                                        </div>
                                    </div>
                                </section>
                            </div>
                        </div>
                        <!-- Control Sidebar -->
                        <aside class="control-sidebar control-sidebar-dark">
                            <!-- Control sidebar content goes here -->
                        </aside>
                        <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
                        <!-- jQuery UI -->
                        <script src="https://code.jquery.com/ui/1.12.1/jquery-ui.min.js"></script>
                        <!-- Bootstrap 4 -->
                        <script
                            src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.bundle.min.js"></script>

                        <!-- Moment.js -->
                        <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.1/moment.min.js"></script>
                        <!-- Tempusdominus Bootstrap 4 -->
                        <script
                            src="https://cdnjs.cloudflare.com/ajax/libs/tempusdominus-bootstrap-4/5.1.2/js/tempusdominus-bootstrap-4.min.js"></script>

                        <!-- OverlayScrollbars -->
                        <script
                            src="https://cdnjs.cloudflare.com/ajax/libs/overlayscrollbars/1.13.0/js/jquery.overlayScrollbars.min.js"></script>

                        <!-- AdminLTE App -->
                        <script
                            src="https://cdnjs.cloudflare.com/ajax/libs/admin-lte/3.2.0/js/adminlte.min.js"></script>
                        <!-- jQuery Validation -->
                        <script
                            src="https://cdnjs.cloudflare.com/ajax/libs/jquery-validate/1.19.3/jquery.validate.min.js"></script>
                        <script
                            src="https://cdnjs.cloudflare.com/ajax/libs/jquery-validate/1.19.3/additional-methods.min.js"></script>
                        <!-- Toastr -->
                        <script src="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/js/toastr.min.js"></script>
                        <!-- SweetAlert2 -->
                        <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
                        <!-- boxicon -->
                        <script src="https://unpkg.com/boxicons@2.1.4/dist/boxicons.js"></script>
                        <script>
                            var apiBaseUrl = "<%= process.env.apiBaseUrl %>";
                            const token = localStorage.getItem("access-token-new");
		                  var userDetails = localStorage.getItem('user-data');
		                   var userDetailsObj = JSON.parse(userDetails);
                        const college = usersData.college;
                        const university = usersData.university;
		                 $('#college').text(college);
                         $('#college1').text(college);
                            $(document).ready(function () {
                                $('#submitBtn').click(function () {
                                    var firstName = $('#firstName').val();
                                    var phone = $('#phone').val();
                                    var designation = $('#designation').val();


                                    if (firstName === '' || phone === '' || designation === '') {
                                        // Display alert using SweetAlert
                                        Swal.fire({
                                            icon: 'warning',
                                            title: 'Oops...',
                                            text: 'Please fill in all the required fields! then try to submit ',
                                        });
                                        return;
                                    }
                                    var userData = JSON.parse(localStorage.getItem("user-data"));
                                    var data = {
                                        name: firstName,
                                        designation: designation,
                                        mobile: parseInt(phone)
                                    };
                                    const apiBaseUrl = "<%= process.env.apiBaseUrl %>";
                                    $.ajax({
                                        url: apiBaseUrl + "v1/auth/map-number",
                                        type: 'POST',
                                        headers: {
                                            "token": localStorage.getItem("access-token-new"),
                                            "Content-Type": "application/json",
                                        },
                                        data: JSON.stringify(data),
                                        success: function (response) {
                                            Swal.fire({
                                                icon: 'success',
                                                title: 'Success',
                                                text: 'Thank You For Providing  Details!',
                                            });
                                        },
                                        error: function () {
                                            Swal.fire({
                                                icon: 'error',
                                                title: 'Error',
                                                text: 'Unable to Submit Details  or You have Already Submitted. .'
                                            });
                                        }
                                    });
                                });
                            });
                        </script>
                        <script>
                            $(function () {
                                if (localStorage.getItem("access-token-new") && localStorage.getItem("user-data")) {
                                    const usersData = JSON.parse(localStorage.getItem("user-data"));
                                    if (usersData.role != "College-Admin") {
                                        alert("You are not authorized");
                                        window.location = "/";
                                    }
                                }
                                else window.location = "/";
                            });
                        </script>