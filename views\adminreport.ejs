<!DOCTYPE html>
<html lang="en">

<head>
	<meta charset="UTF-8" />
	<meta http-equiv="X-UA-Compatible" content="IE=edge" />
	<meta name="viewport" content="width=device-width, initial-scale=1.0" />
	<title>
		<%= title %>
	</title>
		<!-- Google Font: Source Sans Pro -->
        <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,400i,700&display=fallback" />

        <!-- Font Awesome -->
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css" />
        <!-- Ionicons -->
        <link rel="stylesheet" href="https://code.ionicframework.com/ionicons/2.0.1/css/ionicons.min.css" />
    
    
        <!-- Tempusdominus Bootstrap 4 -->
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/tempusdominus-bootstrap-4/5.39.0/css/tempusdominus-bootstrap-4.min.css" />
    
        <!-- iCheck -->
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/icheck-bootstrap/3.0.1/icheck-bootstrap.min.css" />
    
        <!-- Select2 -->
        <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
        <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/select2@4.0.13/dist/css/select2.min.css" />
        
        <!-- Theme style -->
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/admin-lte/3.1.0/css/adminlte.min.css" />
    
        <!-- overlayScrollbars -->
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/overlayscrollbars/1.13.0/css/OverlayScrollbars.min.css" />
    
        <!-- Daterange picker -->
        <link rel="stylesheet" href="https://cdn.datatables.net/1.10.24/css/dataTables.bootstrap4.min.css">
    
    
        <!-- DataTables -->
        <link rel="stylesheet" href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap4.min.css">
    
        <link rel="stylesheet" href="https://cdn.datatables.net/responsive/2.2.9/css/responsive.bootstrap4.min.css">
    
        <link rel="stylesheet" href="https://cdn.datatables.net/buttons/2.0.1/css/buttons.bootstrap4.min.css">
        <!-- Baoxicon -->
	<link href='https://unpkg.com/boxicons@2.1.4/css/boxicons.min.css' rel='stylesheet'>
	<style type="text/css">
	</style>
</head>

<body class="hold-transition sidebar-mini layout-fixed sidebar-collapse">
	<div class="wrapper">
		<!-- Preloader -->
		<div class="preloader flex-column justify-content-center align-items-center">
			<img class="animation__shake" src="/images/loader.gif" alt="CodeBucket" height="100"
				width="100" />
		</div>

		<!-- Navbar -->
		<%- include('partials/header'); %>
			<!-- /.navbar -->

			<!-- Main Sidebar Container -->
			<%- include('partials/sidebar'); %>

			

                    <div class="content-wrapper">
                        <div class="content-header">
                          <div class="container-fluid">
                            <div class="row mb-2">
                              <div class="col-sm-6">
                                <h1 style="color: coral;" class="m-0">Admin University&College Wise Report</h1>
                              </div>
                              <div class="col-sm-6">
                                <ol class="breadcrumb float-sm-right">
                                  <li class="breadcrumb-item"><a href="/adminreport">Home</a></li>
                                  <li class="breadcrumb-item"><a href="/logout">Signout</a></li>
                                </ol>
                              </div>
                            </div>
                          </div>
                        </div>
                        <section class="content">
                          <div class="container-fluid">
                            <div class="row">
                              <section class="col-12">
                               
                        
                                  <div class="card-header">
                                    <button
                                      type="button"
                                      id="btnexcel"
                                      class="btn btn-primary"
                                      onclick="doExport()"
                                    >
                                      Export to Excel
                                    </button>
                                  </div>
                                  <div class="card-body">
                                    <div class="col-md-12">
                                      <table id="mainTable" class="table table-bordered">
                                        <thead id="thead"></thead>
                                        <tbody id="tbody"></tbody>
                                      </table>
                                    </div>
                                  </div>
                                </div>
                              </section>
                              <div class="card card-default" id="seatdetails" style="display: none;">
                                <div class="card-header">
                                  <h3>Deatils</h3>
                                </div>
                                <div class="card-header">
                                  <button
                                    type="button"
                                    id="btnexcel"
                                    class="btn btn-primary"
                                    onclick="doExportList()"
                                  >
                                    Export to Excel
                                  </button>
                                </div>
                                <div class="card-body">
                                  <div class="col-md-12" style="overflow-x: scroll">
                                    <table
                                      class="table table-bordered"
                                      id="mainTable2"
                                    >
                                      <thead id="thead2"></thead>
                                      <tbody id="tbody2"></tbody>
                                    </table>
                                  </div>
                                </div>
                                    
                                  </div>
                                  
                                 
                              </div>
                            </div>
                          </div>
                        </section>
                      </div>
                      <%- include('./partials/footer'); %>
                      <aside class="control-sidebar control-sidebar-dark"></aside>
                    </div>
                <!-- jQuery -->
                <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
                
                <!-- jQuery UI -->
                <script src="https://code.jquery.com/ui/1.12.1/jquery-ui.min.js"></script>
                
                <!-- Bootstrap 4 -->
                <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.bundle.min.js"></script>      
                <!-- Select2 -->
                <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
                
                <!-- Moment.js -->
                <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.1/moment.min.js"></script>
                
                <!-- Date Range Picker -->
                <script src="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.min.js"></script>
                
                <!-- Tempusdominus Bootstrap 4 -->
                <script src="https://cdnjs.cloudflare.com/ajax/libs/tempusdominus-bootstrap-4/5.1.2/js/tempusdominus-bootstrap-4.min.js"></script>
                
                <!-- OverlayScrollbars -->
                <script src="https://cdnjs.cloudflare.com/ajax/libs/overlayscrollbars/1.13.0/js/jquery.overlayScrollbars.min.js"></script>
                
                <!-- AdminLTE App -->
                <script src="https://cdnjs.cloudflare.com/ajax/libs/admin-lte/3.2.0/js/adminlte.min.js"></script>
                
                <!-- jQuery Validation -->
                <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-validate/1.19.3/jquery.validate.min.js"></script>
                <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-validate/1.19.3/additional-methods.min.js"></script>
                
                <!-- Toastr -->
                <script src="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/js/toastr.min.js"></script>
                
                <!-- SweetAlert2 -->
                <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
                
                <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
                <!-- boxicon -->
                <script src="https://unpkg.com/boxicons@2.1.4/dist/boxicons.js"></script>
                <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
                <!-- FileSaver.js -->
                <script src="https://cdnjs.cloudflare.com/ajax/libs/FileSaver.js/2.0.5/FileSaver.min.js"></script>
                <!-- jsPDF -->
                <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
                <!-- js-xlsx -->
                <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.core.min.js"></script>
                <!-- tableExport.js -->
                <script src="https://cdn.jsdelivr.net/npm/tableexport.jquery.plugin@1.30.0/tableExport.min.js"></script>
                <script>
             
    function doExport() {
      $('#mainTable').tableExport({
        type: 'excel',
        mso: {
          styles: ['background-color', 'color', 'font-family', 'font-size', 'font-weight', 'text-align']
        }
      });
    }

    function doExportList() {
      $('#mainTable2').tableExport({
        type: 'excel',
        mso: {
          styles: ['background-color', 'color', 'font-family', 'font-size', 'font-weight', 'text-align']
        }
      });
    }
 
   function getData() {
    var apiBaseUrl = "<%= process.env.apiBaseUrl %>";
    $.ajax({
        url: apiBaseUrl + "v1/stage-two/get-university-wise-summary",
        type: "POST",
        headers: {
            token: localStorage.getItem("access-token-new"),
            "Content-Type": "application/json"
        },
        dataType: "json",
        success: function(res) {
            if (res.status == "success") {
                let thead =
                    "<tr><th colspan='5' style='text-align:center'>University Form Filling Summary Report</th></tr>" +
                    "<tr><th>S No.</th><th>University Name</th><th>Starting Form Filling College</th><th>Final Submit</th><th>Save</th></tr>";

                let tbody = "";
                let totalStartingFormFillingCollege = 0;
                let totalFinalSubmit = 0;
                let totalSave = 0;

                res.data.forEach(function(item, index) {
                    totalStartingFormFillingCollege += item.starting_form_filling_college;
                    totalFinalSubmit += item.final_submit;
                    totalSave += item.save;
                    tbody += "<tr>";
                    tbody += "<td style='text-align: center;'>" + (index + 1) + "</td>";
                    tbody += "<td>" + item.currentUniversityName + "</td>";
                    tbody += "<td style='text-align: center;'><a href='#seatdetails' class='starting-form-link' data-university-name='" + item.currentUniversityName + "' data-type='starting_form_filling_college'>" + item.starting_form_filling_college + "</a></td>";
                    tbody += "<td style='text-align: center;'><a href='#seatdetails' class='final-submit-link' data-university-name='" + item.currentUniversityName + "' data-type='final_submit'>" + item.final_submit + "</a></td>";
                    tbody += "<td style='text-align: center;'><a href='#seatdetails' class='save-link' data-university-name='" + item.currentUniversityName + "' data-type='save'>" + item.save + "</a></td>";
                    tbody += "</tr>";
                });

                tbody += `<tr><td>#</td><td>Grand Total</td><td style='text-align: center;'>${totalStartingFormFillingCollege}</td><td style='text-align: center;'>${totalFinalSubmit}</td><td style='text-align: center;'>${totalSave}</td></tr>`;

                $("#thead").html(thead);
                $("#tbody").html(tbody);

                $(".starting-form-link, .final-submit-link, .save-link").on("click", function(e) {
                    e.preventDefault();
                    const universityName = $(this).data("university-name");
                    const type = $(this).data("type");

                    let dataPayload = { universityName: universityName };

                    if (type === "final_submit") {
                        dataPayload.finalSubmit = "YES";
                    } else if (type === "save") {
                        dataPayload.finalSubmit = "NO";
                    }

                    fetchDetails(dataPayload, type);
                });
            }
        }
    });
}

function fetchDetails(dataPayload, type) {
  var apiBaseUrl = "<%= process.env.apiBaseUrl %>";
    $.ajax({
      url: apiBaseUrl + "v1/stage-two/get-university-details",
        type: "POST",
        headers: {
            token: localStorage.getItem("access-token-new"),
            "Content-Type": "application/json"
        },
        data: JSON.stringify(dataPayload),
        dataType: "json",
        success: function(res) {
            if (res.status == "success") {
                let details = res.data;
                let thead2, tbody2 = "";
                
                if (type === "starting_form_filling_college") {
                    thead2 = "<tr><th>UniversityName</th><th>Starting Form Filling College</th></tr>";
                    details.forEach(function(detail) {
                        tbody2 += "<tr>";
                        tbody2 += "<td>" + detail.currentUniversityName + "</td>";
                        tbody2 += "<td>" + detail.currentlyPostedAtUniversityOfficeDepartmentCollege + "</td>";
                        tbody2 += "</tr>";
                    });
                } else if (type === "final_submit") {
                    thead2 = "<tr><th>Serail No.</th><th>Full Name</th><th>PayID No</th><th>Mobile No.</th><th>EmployeeType</th><th>currentDesignation</th><th>College Name</th></tr>";
                    details.forEach(function(detail ,index) {
                        tbody2 += "<tr>";
                        tbody2 += "<td>" + (index + 1) + "</td>" ;
                        tbody2 +=   '<td>' + ' ' + detail.firstName + ' ' + detail.lastName + '</td>';
                        tbody2 += "<td>" + detail.payIdNo + "</td>";
                       tbody2 += "<td>" + (detail.personalMobileNumber ? detail.personalMobileNumber.slice(-3).padStart(detail.personalMobileNumber.length, 'x') : '') + "</td>";
                        tbody2 += "<td>" + detail.employeeType + "</td>";
                        tbody2 += "<td>" + detail.currentDesignation + "</td>";
                        tbody2 += "<td>" + detail.currentlyPostedAtUniversityOfficeDepartmentCollege + "</td>";
                        tbody2 += "</tr>";
                    });
                } else if (type === "save") {
                    thead2 = "<tr><th>Serail No.</th><th>Full Name</th><th>PayID No</th><th>Mobile No.</th><th>EmployeeType</th><th>currentDesignation</th><th>College Name</th></tr>";
                    details.forEach(function(detail ,index) {
                        tbody2 += "<tr>";
                        tbody2 += "<td>" + (index + 1) + "</td>" ;
                        tbody2 +=   '<td>' + ' ' + detail.firstName + ' ' + detail.lastName + '</td>';
                        tbody2 += "<td>" + detail.payIdNo + "</td>";
                        tbody2 += "<td>" + (detail.personalMobileNumber ? detail.personalMobileNumber.slice(-3).padStart(detail.personalMobileNumber.length, 'x') : '') + "</td>";
                        tbody2 += "<td>" + detail.employeeType + "</td>";
                        tbody2 += "<td>" + detail.currentDesignation + "</td>";
                        tbody2 += "<td>" + detail.currentlyPostedAtUniversityOfficeDepartmentCollege + "</td>";
                        tbody2 += "</tr>";
                    });
                }

                $("#thead2").html(thead2);
                $("#tbody2").html(tbody2);
                $("#seatdetails").show();
                $('#seatdetails')[0].scrollIntoView();
            }
        }
    });
}

$(document).ready(function() {
    getData();
});
</script>
              	<script>
                    $(function () {
                        if (localStorage.getItem("access-token-new") && localStorage.getItem("user-data")) {
                            const usersData = JSON.parse(localStorage.getItem("user-data"));
                            if (usersData.role != "Admin"){
                                alert("You are not authorized to Access This Page");
                                window.location = "/";
                            }
                        }
                        else window.location = "/";
                    });
                </script>