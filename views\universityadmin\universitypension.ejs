<!DOCTYPE html>
<html lang="en">

<head>
	<meta charset="UTF-8" />
	<meta http-equiv="X-UA-Compatible" content="IE=edge" />
	<meta name="viewport" content="width=device-width, initial-scale=1.0" />
	<title>
		<%= title %>
	</title>
	<!-- Google Font: Source Sans Pro -->
	<!-- Google Font: Source Sans Pro -->
	<link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,400i,700&display=fallback" />

	<!-- Font Awesome -->
	<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css" />

	<!-- Ionicons -->
	<link rel="stylesheet" href="https://code.ionicframework.com/ionicons/2.0.1/css/ionicons.min.css" />


	<!-- Tempusdominus Bootstrap 4 -->
	<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/tempusdominus-bootstrap-4/5.39.0/css/tempusdominus-bootstrap-4.min.css" />

	<!-- iCheck -->
	<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/icheck-bootstrap/3.0.1/icheck-bootstrap.min.css" />

	<!-- Select2 -->
	<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
	<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/select2@4.0.13/dist/css/select2.min.css" />
	
	<!-- Theme style -->
	<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/admin-lte/3.1.0/css/adminlte.min.css" />

	<!-- overlayScrollbars -->
	<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/overlayscrollbars/1.13.0/css/OverlayScrollbars.min.css" />

	<!-- Daterange picker -->
	<link rel="stylesheet" href="https://cdn.datatables.net/1.10.24/css/dataTables.bootstrap4.min.css">


	<!-- DataTables -->
	<link rel="stylesheet" href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap4.min.css">

	<link rel="stylesheet" href="https://cdn.datatables.net/responsive/2.2.9/css/responsive.bootstrap4.min.css">

	<link rel="stylesheet" href="https://cdn.datatables.net/buttons/2.0.1/css/buttons.bootstrap4.min.css">
<!-- Baoxicon -->
	<link href='https://unpkg.com/boxicons@2.1.4/css/boxicons.min.css' rel='stylesheet'>
    <style type="text/css">
		.seat {
			width: 50px;
		}

		input::-webkit-outer-spin-button,
		input::-webkit-inner-spin-button {
			-webkit-appearance: none;
			margin: 0;
		}
        .card-title{
            font-weight: bold !important;
        }
        em{
            color: red;
        }
        .card-primary1 {
            background-color: green;
        }
	</style>
</head>

<body class="hold-transition sidebar-mini layout-fixed sidebar-collapse">
	<div class="wrapper">
		<!-- Preloader -->
		<div class="preloader flex-column justify-content-center align-items-center">
			<img class="animation__shake" src="/images/loader.gif" alt="CodeBucket" height="100"
				width="100" />
		</div>
    <%- include('../partials/header'); %> <%- include('../partials/sidebar'); %>


<!-- Content Wrapper. Contains page content -->
<div class="content-wrapper">
    <!-- Content Header (Page header) -->
    <div class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1 class="m-0" style="color: green;">Pensioner Filling  Details</h1>
                </div>
                <!-- /.col -->
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="/">Home</a></li>
                        <li class="breadcrumb-item active">Pensioner Details & Update Pension</li>
                    </ol>
                </div>
                <!-- /.col -->
            </div>
            <!-- /.row -->
        </div>
        <!-- /.container-fluid -->
    </div>

    <section class="content">
        <div class="container-fluid">
            <div class="row">
                <section class="col-12">
                    <form id="frmsearch" name="frmsearch" action="" method="post">
                        <div class="card card-primary">
                            <!-- /.card-header -->
                            <div class="card-header">
                                <h3 class="card-title">Search Pensioner Deatils</h3>
                            </div>
                            <div class="card-body">
                                <div class="form-group d-flex align-items-center">
                                    <label for="payeeid" class="mr-2" style="color: red;">PPO Number</label>
                                    <input type="text" class="form-control mr-2" name="ppoNo" id="ppoNo" placeholder="Enter PPO Number to Fetch Deatils" style="flex: 1"  >
                                  </div>
                            </div>
                            <div class="card-footer">
                                <button type="submit" id="btnsearch"
                                    class="btn btn-info float-right mr-1">
                                    Search Pensioner
                                </button>
                            </div>
                        </div>
                    </form>
                    <div id="pensionDetails" style="display: none;">
                        <form id="frmregister" name="frmregister" action="" method="post">
                            <div class="card card-primary 1">
                                <div class="card-header">
                                    <h3 class="card-title">Pensioner Personal Details</h3>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label>Prefix<em>*</em></label>
                                                <select class="form-control select2" name="prefix"
                                                    id="prefix" style="width: 100%">
                                                    <option value="Mr.">Mr.</option>
                                                    <option value="Ms." >Ms.</option>
                                                    <option value="Mrs.">Mrs.</option>
                                                    <option value="Dr.">Dr.</option>
                                                    <option value="Prof.">Prof.</option>
                                                    <option value="Shri">Shri</option>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label>First Name<em>*</em></label>
                                                <input type="text" class="form-control" name="firstName"
                                                    id="firstName" placeholder="Enter First Name" 	onkeyup="this.value = this.value.toUpperCase();"   />
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label>Middle Name</label>
                                                <input type="text" class="form-control" name="middleName"
                                                    id="middleName" placeholder="Enter Middle Name" 	onkeyup="this.value = this.value.toUpperCase();" />
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label>Last Name</label>
                                                <input type="text" class="form-control" name="lastName"
                                                    id="lastName" placeholder="Enter Last Name" 	onkeyup="this.value = this.value.toUpperCase();"  />
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label>Gender<em>*</em></label>
                                                <select class="form-control select2" name="gender"
                                                    id="gender" style="width: 100%"  required>
                                                    <option value="">Select Gender</option>
                                                    <option value="MALE">MALE</option>
                                                    <option value="FEMALE">FEMALE</option>
                                                    <option value="Other">Other</option>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label>Date of Birth<em>*</em></label>
                                                <input type="text"class="form-control" id="dateOfBirth" name="dateOfBirth" placeholder="DD/MM/YYYY" >
                                            </div>
                                        </div>
                                        
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label>Father's Name<em>*</em></label>
                                                <input type="text" class="form-control" name="fatherName"
                                                    id="fatherName" placeholder="Enter Father's Name" 	onkeyup="this.value = this.value.toUpperCase();" />
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label>Mother's Name</label>
                                                <input type="text" class="form-control" name="motherName"
                                                    id="motherName" placeholder="Enter Mother's Name"  	onkeyup="this.value = this.value.toUpperCase();"  />
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label>Spouse Name (Husband/Wife)<em>*</em></label>
                                                <input type="text" class="form-control" name="spouseName"
                                                    id="spouseName" placeholder="Enter Spouse Name" 	onkeyup="this.value = this.value.toUpperCase();"  />
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label>Personal Mobile Number<em>*</em></label>
                                                <!-- <input type="number" class="form-control" name="personalMobileNumber"
                                                    id="personalMobileNumber" placeholder="Enter Mobile Number"  minlength="10"   maxlength="10" required  /> -->
                                                    <input
                                                
                                                    class="form-control"
                                                    autocomplete="off"
                                                    type="number"
                                                    data-val="true"
                                                    data-val-length="Invalid Mobile Number"
                                                    data-val-length-max="10"
                                                    data-val-length-min="10"
                                                    data-val-regex="Entered Mobile Number format is not valid."
                                                    data-val-regex-pattern="^\(?([6-9]{1})\)?[-. ]?([0-9]{5})[-. ]?([0-9]{4})$"
                                                    data-val-required="Required"
                                                    id="personalMobileNumber"
                                                    maxlength="10"
                                                    name="personalMobileNumber"
                                                    value=""
                                                    
                                                  />
                                                  <span
                                                    class="text-danger field-validation-valid"
                                                    data-valmsg-for="personalMobileNumber"
                                                    data-valmsg-replace="true"
                                                  ></span>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label>Personal Email</label>
                                                <input type="email" class="form-control" name="personalEmail"
                                                    id="personalEmail" placeholder="Enter Email"   />
                                            </div>
                                        </div>
                                        <div class="col-md-6" id="pranNoContainer" >
                                            <div class="form-group">
                                                <label>PRAN No</label>
                                                <input type="number" class="form-control" name="pranNo" id="pranNo" placeholder="Enter PRAN" />
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label>PAN No</label>
                                                <input type="text" class="form-control" name="panNo"
                                                    id="panNo" placeholder="Enter PAN Number" 	onkeyup="this.value = this.value.toUpperCase();"  />
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label>Aadhar Card </label>
                                                <!-- <input type="number" class="form-control" name="aadharCardNo"
                                                    id="aadharCardNo" placeholder="Enter Aadhar Number" required /> -->
                                                    <input
                                                    maxlength="12"
                                                    class="form-control"
                                                    onkeyup="this.value = this.value.toUpperCase();"
                                                    autocomplete="off"
                                                    type="text"
                                                    data-val="true"
                                                    data-val-regex="Invalid Aadhar number."
                                                    data-val-regex-pattern="^[0-9]{12}$"
                                                    data-val-required="Required"
                                                    id="aadharCardNo"
                                                    name="aadharCardNo"
                                                    value=""
                                                    
                                                  />
                                                  <span
                                                    class="text-danger field-validation-valid"
                                                    data-valmsg-for="aadharCardNo"
                                                    data-valmsg-replace="true"
                                                  ></span>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label>Pay ID No.</label>
                                                <input type="text" class="form-control" name="payIdNo"
                                                    id="payIdNo" placeholder="Enter Pay ID" 	onkeyup="this.value = this.value.toUpperCase();"  />
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label>Employee Type<em>*</em></label>
                                                <select class="form-control select2" name="employeeType"
                                                    id="employeeType" style="width: 100%" >
                                                    <option value="">Select Employee Type</option>
                                                    <option value="T">Teaching</option>
                                                    <option value="NT">Non-Teaching</option>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label>PPO No.<em>*</em></label>
                                                <input type="text" class="form-control" name="ppoNO"
                                                    id="ppoNO" placeholder="Enter PPO NO" 	onkeyup="this.value = this.value.toUpperCase();"  />
                                            </div>
                                        </div>
                                    </div>
                                </div>
                               
                           
        <!-- New Fields -->
        <div class="card card-primary">
            <div class="card-header">
                <h3 class="card-title">Employee Official Details</h3>
            </div>
            <div class="card-body">
                <div class="row">
                 
                    <div class="col-md-6">
                        <div class="form-group">
                            <label>Appointment Letter-Order No    </label>
                            <input type="text" class="form-control" id="appointmentLetter" name="appointmentLetter" placeholder="Enter Appointment Letter-Order No" >
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label>Effective date of joining</label>
                            <input type="text" class="form-control" id="effectiveDateJoining" name="effectiveDateJoining" placeholder="Enter Effective Date Of Joining" onkeyup="this.value = this.value.toUpperCase();" >
                        </div>
                    </div>
        
                    <div class="col-md-6">
                        <div class="form-group">
                            <label>Date of Retirement<em>*</em></label>
                            <input type="text" class="form-control" name="dateRetirement"
                                id="dateRetirement" placeholder="Enter Date of Retirement(DD/MM/YYYY)"  />
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label>University Name at the time of retirement *<em>*</em></label>
                            <select class="form-control select2" name="universityNameRetirement"
                                id="universityNameRetirement" style="width: 100%" >
                                <option value="">Select University Name</option>
                                </select>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label>College Name At the Retirement  *<em>*</em></label>
                            <select class="form-control select2" name="collegeNameRetirement"
                                id="collegeNameRetirement" style="width: 100%" >
                                <option value="">Select College Name</option>
                                </select>
                        </div>
                    </div>
                    
                </div>
        
                            </div>
                            <section class="col-12">
                                <!-- <form id="frmregister" name="frmregister" action="" method="post"> -->
                                    <div class="card card-primary">
                                        <!-- /.card-header -->
                                        <div class="card-header">
                                            <h3 class="card-title">Bank Details</h3>
                                        </div>
                                        <div class="card-body">
                                            <div class="row">
                                            
                                                <div class="col-md-6">
                                                    <div class="form-group">
                                                        <label>Bank Account No<em>*</em></label>
                                                        <input type="number" class="form-control" name="bankAccountNo"
                                                            id="bankAccountNo" placeholder="Enter Bank Account No As Par CFMS"   />
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="form-group">
                                                        <label>IFSC Code<em>*</em></label>
                                                        <input type="text" class="form-control" name="ifscCode"
                                                            id="ifscCode" placeholder="Enter Bank IFSC Code" onkeyup="this.value = this.value.toUpperCase();"  />
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="form-group">
                                                        <label>Bank Name<em>*</em></label>
                                                        <input type="text" class="form-control" name="bankName"
                                                            id="bankName" placeholder="Enter Bank Name" onkeyup="this.value = this.value.toUpperCase();"   />
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="form-group">
                                                        <label>Branch Name<em>*</em></label>
                                                        <input type="text" class="form-control" name="branchName"
                                                            id="branchName" placeholder="Enter Bank Branch" onkeyup="this.value = this.value.toUpperCase();"    />
                                                    </div>
                                                </div>
                                              
                                            </div>
                                        </div>
                                    </div>
                                    <div class="card card-primary">
                                        <!-- /.card-header -->
                                        <div class="card-header">
                                            <h3 class="card-title">Pay Entitlement Details</h3>
                                        </div>
                                        <div class="card-body">
                                            <div class="row">
                                                <div class="col-md-6">
                                                    <div class="form-group">
                                                        <label>Basic Pension.*<em>*</em></label>
                                                        <input type="text" class="form-control" name="basicPension"
                                                            id="basicPension" placeholder="Enter Basic Pension " />
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="form-group">
                                                        <label>Last Basic Pay.*<em>*</em></label>
                                                        <input type="text" class="form-control" name="lastBasicPay"
                                                            id="lastBasicPay" placeholder="Enter LAST Basic Pay " />
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="form-group">
                                                        <label>Dearness Relief (@ % of basic pension)*.*<em>*</em></label>
                                                        <input type="number" class="form-control" name="dearnessReliefPercentage"
                                                            id="dearnessReliefPercentage" placeholder="Enter Dearness Relief Percentage"  />
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="form-group">
                                                        <label>Medical Allowance*<em>*</em></label>
                                                        <input type="number" class="form-control" name="medicalAllowance"
                                                            id="medicalAllowance" placeholder="Enter Medical Allowance"  />
                                                    </div>
                                                </div>
                                             
                                                <div class="col-md-6">
                                                    <div class="form-group">
                                                        <label>Gratuity (if any in the relevant month).*<em>*</em></label>
                                                        <input type="number" class="form-control" name="gratuity"
                                                            id="gratuity" placeholder="Enter  Gratuity"   />
                                                    </div>
                                                </div>
                                               
                                                <div class="col-md-6">
                                                    <div class="form-group">
                                                        <label>Leave Encashment (if any in the relevant month)<em>*</em></label>
                                                        <input type="number" class="form-control" name="leaveEncashment"
                                                            id="leaveEncashment" placeholder="Enter Leave Encashment"   />
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="form-group">
                                                        <label>Arrears (if any in the relevant month)<em>*</em></label>
                                                        <input type="number" class="form-control" name="arrears"
                                                            id="arrears" placeholder="Enter Arrears"  />
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="form-group">
                                                        <label>All One time Benefit .<em>*</em></label>
                                                        <input type="text" class="form-control" name="allOneTimeBenifit"
                                                            id="allOneTimeBenifit" placeholder="Enter All One time Benefit"  />
                                                    </div>
                                                </div>
                                           

                                            </div>
                                        </div>
                                    </div>

                            <div class="card-footer">
                                <!-- <button type="button" id="btnsubmitedit"
                                    class="btn btn-danger float-right mr-1">
                                 Edit Details 
                                </button> -->
                                <button type="button" id="btnsubmit"
                                class="btn btn-success float-right mr-1">
                      Submit Pension 
                            </button>
                            </div>
                        </form>
                    </div>
                </section>
            </div>
            <!-- Main row -->
        </div>
        <!-- /.container-fluid -->
    </section>
    <!-- /.content -->
</div>
<!-- /.content-wrapper -->

<%- include('../partials/footer'); %>
    <!-- Control Sidebar -->
    <aside class="control-sidebar control-sidebar-dark">
        <!-- Control sidebar content goes here -->
    </aside>
    <!-- /.control-sidebar -->
</div>
<!-- ./wrapper -->

                  
                <!-- jQuery -->
                <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
                
                <!-- jQuery UI -->
                <script src="https://code.jquery.com/ui/1.12.1/jquery-ui.min.js"></script>
                
                <!-- Bootstrap 4 -->
                <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.bundle.min.js"></script>
              <!-- Sparkline -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-sparklines/2.1.2/jquery.sparkline.min.js"></script>

<!-- Select2 -->
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>

<!-- Moment.js -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.1/moment.min.js"></script>

<!-- Date Range Picker -->
<script src="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.min.js"></script>

<!-- Tempusdominus Bootstrap 4 -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/tempusdominus-bootstrap-4/5.1.2/js/tempusdominus-bootstrap-4.min.js"></script>

<!-- OverlayScrollbars -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/overlayscrollbars/1.13.0/js/jquery.overlayScrollbars.min.js"></script>

<!-- AdminLTE App -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/admin-lte/3.2.0/js/adminlte.min.js"></script>

<!-- jQuery Validation -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-validate/1.19.3/jquery.validate.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-validate/1.19.3/additional-methods.min.js"></script>

<!-- Toastr -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/js/toastr.min.js"></script>

<!-- SweetAlert2 -->
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

<script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>

<script src="https://unpkg.com/boxicons@2.1.4/dist/boxicons.js"></script>
        <script>
        const apiBaseUrl = "<%= process.env.apiBaseUrl %>";
        const token = localStorage.getItem("access-token-new");
		var userDetails = localStorage.getItem('user-data');
		var userDetailsObj = JSON.parse(userDetails);
        var universityName= userDetailsObj.university
        $('#university1').append(universityName);
        $('#college1').append(universityName);
        console.log(universityName)
  </script>
 <script>
$("#btnsearch").on("click", function (e) {
    e.preventDefault();
    const ppoNo = $("#ppoNo").val();
    $("#pensionDetails").show();
});
$(function () {
    $('.select2').select2()
})
</script>
<script>
$(document).ready(function() {
    $.ajax({
   url: apiBaseUrl + "v1/stage-two/get-university-data",
    type: 'get',
    dataType: 'json',
    success: function(response) {

    if (response.status === "success") {
  
        var universities = response.data;
        $('#universityNameRetirement').append($('<option>', {
            value: '',
                text: 'Select a university'
                 }));
        $.each(universities, function(index, item) {
            $('#universityNameRetirement').append($('<option>', {
                value: item.university_name,
                text: item.university_name
            }));
        });
     
    } else {
        console.error('API request failed with status:', response.status);
    }
},
error: function(xhr, status, error) {
    console.error('Error fetching data from API:', error);
}
});

})

</script>

<script>
    $(document).ready(function() {
        $.ajax({
        url: apiBaseUrl + "v1/stage-two/get-college-data",
        type: 'get',
        dataType: 'json',
        success: function(response) {
    
        if (response.status === "success") {
      
            var universities = response.data;
            $('#collegeNameRetirement').append($('<option>', {
                value: '',
                    text: 'Select a College'
                     }));
            $.each(universities, function(index, item) {
                $('#collegeNameRetirement').append($('<option>', {
                    value:  item.college_name,
                    text: item.college_name
                }));
            });
          
        } else {
            console.error('API request failed with status:', response.status);
        }
    },
    error: function(xhr, status, error) {
        console.error('Error fetching data from API:', error);
    }
    });
    
    })
    
    </script>

<script>
$(document).ready(function() {
    $('#btnsubmit').on('click', function(event) {
        event.preventDefault();
        var requiredFields = [
        '#prefix',
        '#firstName',
        '#gender',
        '#dateOfBirth',
        '#fatherName',
        '#ppoNo',
        '#employeeType',
        '#effectiveDateJoining',
        '#dateRetirement',
        '#universityNameRetirement',
        '#collegeNameRetirement',
        '#bankAccountNo',
        '#bankName',
        '#ifscCode',
        '#branchName',
        '#basicPension',
        '#dearnessReliefPercentage',
        '#medicalAllowance',
        '#gratuity',
        '#leaveEncashment',
        '#arrears',
        '#spouseName',
        '#allOneTimeBenifit'
    ];

    var allFilled = true;
    var emptyFields = [];

    requiredFields.forEach(function(selector) {
        var value = $(selector).val();
        if (value === undefined || value.trim() === '') {
            allFilled = false;
            emptyFields.push(selector);
        }
    });

    if (!allFilled) {
        var errorMessage = 'Please fill out the following fields: \n' + emptyFields.map(function(selector) {
            return $(selector).attr('name') || selector; 
        }).join(', ');
        alert(errorMessage);
        return; 
    }
    let formData = {
    prefix: $('#prefix').val() || '',
    firstName: $('#firstName').val() || '',
    middleName: $('#middleName').val() || '',
    lastName: $('#lastName').val() || '',
    gender: $('#gender').val() || '',
    dateOfBirth: $('#dateOfBirth').val() || '',
    fathersName: $('#fatherName').val() || '',
    mothersName: $('#motherName').val() || '',
    mobileNo: $('#personalMobileNumber').val() || '',
    emailId: $('#personalEmail').val() || '',
    pranNo: $('#pranNo').val() || '',
    ppoNo: $('#ppoNo').val() || '',
    panNo: $('#panNo').val() || '',
    aadharNo: $('#aadharCardNo').val() || '',
    payIdNo: $('#payIdNo').val() || '',
    employeeType: $('#employeeType').val() || '',
    appointmentLetter: $('#appointmentLetter').val() || '',
    effectiveDateJoining: $('#effectiveDateJoining').val() || '',
    dateRetirement: $('#dateRetirement').val() || '',
    universityNameRetirement: $('#universityNameRetirement').val() ,
    collegeNameRetirement: $('#collegeNameRetirement').val() ,
    bankAccountNo: $('#bankAccountNo').val() || '',
    bankName: $('#bankName').val() || '',
    ifscCode: $('#ifscCode').val() || '',
    branchName: $('#branchName').val() || '',
    basicPension: $('#basicPension').val() || '',
    dearnessReliefPercentage: $('#dearnessReliefPercentage').val() || '',
    medicalAllowance: $('#medicalAllowance').val() || '',
    gratuity: $('#gratuity').val() || '',
    leaveEncashment: $('#leaveEncashment').val() || '',
    arrears: $('#arrears').val() || '',
    dateRetiremenDeathPensioner: "N/A",
    updatedBy: universityName ,
    spouseName: $('#spouseName').val() || '',
    allOneTimeBenifit: $('#allOneTimeBenifit').val() || '',
    lastBasicPay: $('#lastBasicPay').val() || ''
};

        $.ajax({
          url: apiBaseUrl + "v1/stage-two/edit-pension",
            type: 'POST',
            contentType: 'application/json',
            data: JSON.stringify(formData),
            headers: {
                    token: localStorage.getItem("access-token-new"),
                    "Content-Type": "application/json"
                },
            success: function(response) {
                Swal.fire({
                    icon: 'success',
                    title: 'Success',
                    text: 'Pensioner details submitted successfully!',
                });
                generatePDF(formData); 
                setTimeout(() => {
                location.reload();
                  }, 3000);
            },
            error: function(xhr, status, error) {
                let errorMessage = 'An error occurred while submitting the form. Please try again.';
                      if (xhr.responseJSON && xhr.responseJSON.message) {
                            errorMessage = xhr.responseJSON.message;
                                   }
                                   Swal.fire({
        icon: 'error',
        title: 'Error',
        text: errorMessage,
    });
}

 });
    });
function generatePDF(formData) {
    const { jsPDF } = window.jspdf;
    const doc = new jsPDF();

    // Custom styles configuration
    const titleFont = "helvetica";
    const titleFontSize = 14;
    const titleColor = [0, 0, 0]; 

    const fieldFont = "helvetica";
    const fieldFontSize = 10;
    const fieldLabelColor = [0, 0, 0]; 
    const fieldValueColor = [50, 50, 50]; 

    const lineHeight = 7;
    const pageWidth = doc.internal.pageSize.getWidth();
    const borderMargin = 15;


    doc.setFont(titleFont, "bold");
    doc.setFontSize(titleFontSize);
    doc.setTextColor(...titleColor);

  
    const additionalText = "(Pensioner Details)";
    const titleText = (formData.updatedBy || "University Name") + additionalText;

    const titleX = (pageWidth - doc.getTextWidth(titleText)) / 2;
    doc.text(titleText, titleX, 10); 
    doc.text("Employee Personal Details", 10, 20);
    doc.setFont(fieldFont, "bold");
    doc.setFontSize(fieldFontSize);
    doc.setTextColor(...fieldLabelColor);

    let y = 30;
    function addTwoFieldsOnSameLineWithBorder(key1, value1, key2, value2, x, y) {
        const totalWidth = pageWidth - borderMargin * 2;
        const separatorX = x + totalWidth / 2;
        doc.setDrawColor(0, 0, 0); 
        doc.setLineWidth(0.5); 
        doc.rect(x - 2, y - lineHeight, totalWidth, lineHeight + 4);

       
        doc.line(separatorX, y - lineHeight, separatorX, y + 4);

        doc.setTextColor(...fieldLabelColor);
        doc.text(`${key1}:`, x + 2, y);
        doc.setTextColor(...fieldValueColor);
        doc.text(value1, x + 2 + doc.getTextWidth(`${key1}: `), y);

        doc.setTextColor(...fieldLabelColor);
        doc.text(`${key2}:`, separatorX + 2, y);
        doc.setTextColor(...fieldValueColor);
        doc.text(value2, separatorX + 2 + doc.getTextWidth(`${key2}: `), y);
    }
    function addContentWithBorder(key, value, x, y, valueXOffset = 50) {
        const totalWidth = pageWidth - borderMargin * 2;
        doc.setDrawColor(0, 0, 0); 
        doc.setLineWidth(0.5); // Border thickness
        doc.rect(x - 2, y - lineHeight, totalWidth, lineHeight + 4);


        doc.setTextColor(...fieldLabelColor);
        doc.text(`${key}:`, x + 2, y);
        doc.setTextColor(...fieldValueColor);
        doc.text(value, x + valueXOffset, y);
    }
    addTwoFieldsOnSameLineWithBorder("Payee ID", formData.payIdNo, "Prefix", formData.prefix, borderMargin, y);
    y += lineHeight + 5;
    addTwoFieldsOnSameLineWithBorder("First Name", formData.firstName, "Middle Name", formData.middleName, borderMargin, y);
    y += lineHeight + 5;
    addTwoFieldsOnSameLineWithBorder("Last Name", formData.lastName, "Gender", formData.gender, borderMargin, y);
    y += lineHeight + 5;
    addTwoFieldsOnSameLineWithBorder("Date of Birth", formData.dateOfBirth, "Father's Name", formData.fathersName, borderMargin, y);
    y += lineHeight + 5;
    addTwoFieldsOnSameLineWithBorder("Mother's Name", formData.mothersName,"PRAN No", formData.pranNo, borderMargin, y);
    y += lineHeight + 5;
    addTwoFieldsOnSameLineWithBorder("Personal Mobile Number", formData.mobileNo, "Email", formData.emailId, borderMargin, y);
    y += lineHeight + 5;
    
    addTwoFieldsOnSameLineWithBorder("PAN No", formData.panNo, "Aadhar Card No", formData.aadharNo, borderMargin, y);
    y += lineHeight + 5;
  
    // New section: Employee Official Details
    doc.setFontSize(titleFontSize);
    doc.setTextColor(...titleColor);
    doc.text("Employee Official Details", 10, y);
    y += lineHeight + 5;

    doc.setFont(fieldFont, "bold");
    doc.setFontSize(fieldFontSize);
    doc.setTextColor(...fieldLabelColor);

    addTwoFieldsOnSameLineWithBorder("Appointment Letter-Order No & Date*", formData.appointmentLetter, "Employee Type", formData.employeeType, borderMargin, y);
    y += lineHeight + 5;
	addTwoFieldsOnSameLineWithBorder("Effective Date of Joining", formData.effectiveDateJoining,  "Date of Retirement*", formData.dateRetirement, borderMargin, y);
    y += lineHeight + 5;
	addContentWithBorder( "University Name", formData.universityNameRetirement, borderMargin, y);
    y += lineHeight + 5;
  
	if (y >= 250) {
        doc.addPage();
        y = 20;
    } else {
        y += lineHeight * 2;
    }
    // Add bank details heading
    doc.setFontSize(titleFontSize);
    doc.setTextColor(...titleColor);
    doc.text("Bank Details", 10, y);
    y += lineHeight + 5;

    doc.setFont(fieldFont, "bold");
    doc.setFontSize(fieldFontSize);
    doc.setTextColor(...fieldLabelColor);

    addTwoFieldsOnSameLineWithBorder("Bank Account No", formData.bankAccountNo, "Bank Name", formData.bankName, borderMargin, y);
    y += lineHeight + 5;
    addTwoFieldsOnSameLineWithBorder("Branch Name", formData.branchName, "IFSC Code", formData.ifscCode, borderMargin, y);
    y += lineHeight + 5;

	// doc.addPage();
    // y = 20;
    doc.setFontSize(titleFontSize);
    doc.setTextColor(...titleColor);
    doc.text("Pension Details", 10, y);
    y += lineHeight + 5;

    doc.setFont(fieldFont, "bold");
    doc.setFontSize(fieldFontSize);
    doc.setTextColor(...fieldLabelColor);
    
    addTwoFieldsOnSameLineWithBorder("Basic Pension", formData.basicPension, "Dearness Relief (@ % of basic pension)*", formData.dearnessReliefPercentage, borderMargin, y);
    y += lineHeight + 5;
    addTwoFieldsOnSameLineWithBorder("Medical Allowance", formData.medicalAllowance, "Gratuity (if any in the relevant month)", formData.gratuity, borderMargin, y);
    y += lineHeight + 5;
	
	addContentWithBorder("Leave Encashment (if any in the relevant month)", formData.leaveEncashment, borderMargin, y,100);
    y += lineHeight + 5;
    addContentWithBorder("Arrears (if any in the relevant month)", formData.arrears, borderMargin, y,100);
    y += lineHeight + 5;
    // Save the PDF
    doc.save('Employeepension.pdf');
}
const formData = {
};
});
</script>
<script>
    function isTokenExpired(token) {
        if (!token) return true;
        try {
            // Decode JWT payload
            const payloadBase64 = token.split('.')[1];
            const payload = JSON.parse(atob(payloadBase64));
            // Check if current time is past the expiration time
            const isExpired = payload.exp * 1000 < Date.now();
            return isExpired;
        } catch (error) {
            console.error("Failed to decode token:", error);
            return true; 
        }
    }
    // Check token expiration on load
    $(function () {
       const token = localStorage.getItem("access-token-new");
        const userData = JSON.parse(userDetails);
        if (token && userData) {
            // Check role and token expiration
            if (usersData.role !== "University-Admin" || isTokenExpired(token)) {
                alert("Session expired or unauthorized access. Redirecting to login.");
                window.location = "/";
            } else {
                setInterval(() => {
                    if (isTokenExpired(token)) {
                        alert("Session expired. Please login again.");
                        window.location = "/";
                    }
                }, 60000);
            }
        } else {
            window.location = "/";
        }
    });
                    </script> 