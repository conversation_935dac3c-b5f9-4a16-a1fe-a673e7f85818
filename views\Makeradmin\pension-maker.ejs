<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>
        <%= title %>
    </title>
    <!-- Google Font: Source Sans Pro -->
    <link rel="stylesheet"
        href="https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,400i,700&display=fallback" />
    <!-- Font Awesme -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css" />
    <!-- Ionicons -->
    <link rel="stylesheet" href="https://code.ionicframework.com/ionicons/2.0.1/css/ionicons.min.css" />

    <!-- Tempusdominus Bootstrap 4 -->
    <link rel="stylesheet"
        href="https://cdnjs.cloudflare.com/ajax/libs/tempusdominus-bootstrap-4/5.39.0/css/tempusdominus-bootstrap-4.min.css" />
    <!-- iCheck -->
    <link rel="stylesheet"
        href="https://cdnjs.cloudflare.com/ajax/libs/icheck-bootstrap/3.0.1/icheck-bootstrap.min.css" />

    <!-- Select2 -->
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/select2@4.0.13/dist/css/select2.min.css" />

    <!-- Theme style -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/admin-lte/3.1.0/css/adminlte.min.css" />

    <!-- overlayScrollbars -->
    <link rel="stylesheet"
        href="https://cdnjs.cloudflare.com/ajax/libs/overlayscrollbars/1.13.0/css/OverlayScrollbars.min.css" />

    <!-- Daterange picker -->
    <link rel="stylesheet" href="https://cdn.datatables.net/1.10.24/css/dataTables.bootstrap4.min.css">
    <!-- DataTables -->
    <link rel="stylesheet" href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap4.min.css">
    <link rel="stylesheet" href="https://cdn.datatables.net/responsive/2.2.9/css/responsive.bootstrap4.min.css">
    <link rel="stylesheet" href="https://cdn.datatables.net/buttons/2.0.1/css/buttons.bootstrap4.min.css">
    <!-- Baoxicon -->
    <link href='https://unpkg.com/boxicons@2.1.4/css/boxicons.min.css' rel='stylesheet'>
    <style type="text/css">
        .seat {
            width: 50px;
        }

        input::-webkit-outer-spin-button,
        input::-webkit-inner-spin-button {
            -webkit-appearance: none;
            margin: 0;
        }

        .card-title {
            font-weight: bold !important;
        }

        em {
            color: red;
        }
        .custom-checkbox {
            display: inline-block;
            width: 25px;
            height: 25px;
            background-color: white;
            border: 2px solid #ddd;
            border-radius: 7px;
            margin-right: 15px;
            vertical-align: middle;
            position: relative;
        }
        .form-check-input:checked+.form-check-label .custom-checkbox {
            background-color: blue;

        }
        .form-check-input:checked+.form-check-label .custom-checkbox::after {
            content: '';
            position: absolute;
            top: 5px;
            left: 9px;
            width: 6px;
            height: 12px;
            border: solid white;
            border-width: 0 2px 2px 0;
            transform: rotate(45deg);
        }
        .form-check-label {
            cursor: pointer;
        }

        .is-invalid {
            border-color: red;
        }

        .modal-fullscreen {
            max-width: 90%;
            width: 90%;
            height: 220vh;
            margin: 0;
            margin-left: 60px;
        }

        .modal-content {
            height: 100%;
        }
    </style>
</head>

<body class="hold-transition sidebar-mini layout-fixed sidebar-collapse">
    <div class="wrapper">
        <!-- Preloader -->
        <div class="preloader flex-column justify-content-center align-items-center">
            <img class="animation__shake" src="/images/loader.gif" alt="CodeBucket" height="100" width="100" />
        </div>
        <!-- Navbar -->
        <%- include('../partials/header'); %>
            <!-- /.navbar -->
            <!-- Main Sidebar Container -->
            <%- include('../partials/sidebar'); %>
                <!-- Content Wrapper. Contains page content -->
                <div class="content-wrapper">
                    <!-- Content Header (Page header) -->
                    <div class="content-header">
                        <div class="container-fluid">
                            <div class="row mb-2">
                                <div class="col-sm-6">
                                    <h1 class="m-0" style="color:black; font-size: xx-large; font-weight: bolder;">Welcome to Pension Maker Action Dashboard:</h1>
                                </div>
                                <!-- /.col -->
                                <div class="col-sm-6">
                                    <ol class="breadcrumb float-sm-right">
                                        <li class="breadcrumb-item"><a href="/logout">Signout</a></li>

                                    </ol>
                                </div>
                                <!-- /.col -->
                            </div>
                            <!-- /.row -->
                        </div>
                        <!-- /.container-fluid -->
                    </div>
                    <section class="content">
                        <div class="container-fluid">
                            <div class="row">
                                <section class="col-12">
                                    <div class="card card-success">
                                        <div class="card-header">
                                            <h3 class="card-title" style="font-weight: bolder;">Pension Maker View</h3>
                                        </div>
                                        <div class="card-body">
                                            <div class="row">
                                                <div class="col-12">
                                                    <div class="card card-primary">
                                                        <div class="card-body">
                                                            <div class="form-group row">
                                                            
                                                                <!-- Second dropdown -->
                                                                <div class="col-md-6">
                                                                    <label for="employeeTypeDropdown"
                                                                        style="color: red; font-weight: bolder;">Choose
                                                                        the Employee Type:</label>
                                                                    <select id="employeeTypeDropdown"
                                                                        class="form-control select2" required>
                                                                        <option value="">Select a Employee Type</option>
                                                                        <option value="T">Teaching</option>
                                                                        <option value="NT">Non-Teaching</option>
                                                                    </select>
                                                                </div>
                                                         
                                                            <div class="col-md-6">
                                                                <label for="monthYearDropdown"
                                                                    style="color: red; font-weight: bolder;">Choose
                                                                    your Absentee Month:</label>
                                                                <select id="monthYearDropdown"
                                                                    class="form-control select2" required>
                                                                    <option value="">Select a month</option>

                                                                </select>
                                                            </div>
                                                        </div>
                                                            <button type="button" id="btnsearch"
                                                                class="btn btn-info float-right mr-1">Submit</button>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </section>
                            </div>
                        </div>
                    </section>

                    <div class="row">
                        <section class="col-12">
                            <div class="card-header">
                                <button type="button" id="btnexcel" class="btn btn-primary" onclick="doExport()">
                                    Export to Excel
                                </button>
                            </div>
                            <div class="card-body">
                                <div class="col-md-12">
                                    <table id="mainTable" class="table table-bordered">
                                        <thead id="thead"></thead>
                                        <tbody id="tbody"></tbody>
                                    </table>
                                </div>
                            </div>

                        </section>
                        <div class="modal fade" id="modalresolved" tabindex="-1" role="dialog" aria-labelledby="modal1">
                            <div class="modal-dialog modal-fullscreen" role="document">
                                <div class="modal-content">
                                    <div class="modal-header">
                                        <!-- Close button -->
                                        <button type="button" class="close close-red" data-dismiss="modal"
                                            aria-label="Close">
                                            <span aria-hidden="true">&times;</span>
                                        </button>
                                    </div>
                                    <div class="modal-body">
                                        <div class="row">
                                            <!-- Salary Details Section -->
                                            <div class="col-md-10">
                                                <h5><b> Details to Verify By Payment Maker:</b></h5>
                                                <form id="frmupdate" name="frmupdate" action="" method="post"
                                                    enctype="multipart/form-data">
                                                    <table class="table table-bordered">
                                                        <!-- Personal Details Section -->
                                                        <thead>
                                                            <tr>
                                                                <th colspan="3" style="color: red; font-weight: bold; font-size: larger;">
                                                                    Pensioner   Personal Details to Verify</th>
                                                            </tr>
                                                        </thead>
                                                        <tbody>
                                                            <tr>
                                                                <td><b>PPO No: </b><input type="text" class="form-control" id="ppoNo" readonly></td>
                                                                <td><b>First Name: </b><input type="text" class="form-control" id="firstName" readonly></td>
                                                                <td><b>Middle Name: </b><input type="text" class="form-control" id="middleName" readonly></td>
                                                            </tr>
                                                            <tr>
                                                                <td><b>Last Name: </b><input type="text" class="form-control" id="lastName" readonly></td>
                                                                <td><b>Gender: </b><input type="text" class="form-control" id="gender" readonly></td>
                                                                <td><b>Date of Birth: </b><input type="text" class="form-control" id="dateOfBirth" readonly></td>
                                                            </tr>
                                                            <tr>
                                                                <td><b>Father's Name: </b><input type="text" class="form-control" id="fathersName" readonly></td>
                                                                <td><b>Spouse Name: </b><input type="text" class="form-control" id="spouseName" readonly></td>
                                                                <td><b>PAN No: </b><input type="text" class="form-control" id="panNo" readonly></td>
                                                            </tr>
                                                            <tr>
                                                                <td><b>Mobile No: </b><input type="text" class="form-control" id="mobileNo" readonly></td>
                                                                <td><b>Aadhar Card No: </b><input type="text" class="form-control" id="aadharNo" readonly></td>
                                                                <td><b>PRAN No: </b><input type="text" class="form-control" id="pranNo" readonly></td>
                                                            </tr>
                                                            <tr>
                                                                <td><b>Employee Type: </b><input type="text" class="form-control" id="employeeType" readonly></td>
                                                                <td><b>Appointment Letter Date: </b><input type="text" class="form-control" id="appointmentLetter" readonly></td>
                                                                <td><b>Effective Date of Joining: </b><input type="text" class="form-control" id="effectiveDateJoining" readonly></td>
                                                            </tr>
                                                            <tr>
                                                                <td><b>Date of Retirement: </b><input type="text" class="form-control" id="dateRetirement" readonly></td>
                                                                <td><b>University at Retirement: </b><input type="text" class="form-control" id="universityRetirement" readonly></td>
                                                                <td><b>College at Retirement: </b><input type="text" class="form-control" id="collegeRetirement" readonly></td>
                                                            </tr>
                                                        </tbody>
                                                        <thead>
                                                            <tr>
                                                                <th colspan="3" style="color: blue; font-weight: bold; font-size: larger;">
                                                                   Pensioner Salary & Pension Details to Verify</th>
                                                            </tr>
                                                        </thead>
                                                        <tbody>
                                                            <tr>
                                                                <td><b>Bank Account No: </b><input type="text" class="form-control" id="bankAccountNo" readonly></td>
                                                                <td><b>Bank Name: </b><input type="text" class="form-control" id="bankName" readonly></td>
                                                                <td><b>Branch Name: </b><input type="text" class="form-control" id="branchName" readonly></td>
                                                            </tr>
                                                            <tr>
                                                                <td><b>IFSC Code: </b><input type="text" class="form-control" id="ifscCode" ></td>
                                                                <td><b>Basic Pension: </b><input type="text" class="form-control" id="basicPension" ></td>
                                                                <td><b>Dearness Relief %: </b><input type="text" class="form-control" id="dearnessRelief" ></td>
                                                            </tr>
                                                            <tr>
                                                                <td><b>Medical Allowance: </b><input type="text" class="form-control" id="medicalAllowance" ></td>
                                                                <td><b>Gratuity: </b><input type="text" class="form-control" id="gratuity" ></td>
                                                                <td><b>Leave Encashment: </b><input type="text" class="form-control" id="leaveEncashment" ></td>
                                                            </tr>
                                                            <tr>
                                                                <td><b>Arrears: </b><input type="text" class="form-control" id="arrears" ></td>
                                                                <td><b>One-Time Benefits Received: </b><input type="text" class="form-control" id="oneTimeBenefits" ></td>
                                                                <td><b>Pay Level: </b><input type="text" class="form-control" id="payLevel" ></td>
                                                            </tr>
                                                        </tbody>

                                                        <tr>
                                                            <td colspan="5">
                                                                <p style="font-style: italic; color:rgb(128, 0, 0); font-size: larger;">
                                                                    Note: Please Verify All Payment Details   and then Submit Request To Payment Maker
                                                               
                                                                </p>
                                                            </td>
                                                        </tr>
                                                        <tr>
                                                            <td colspan="5" align="center">
                                                                <button type="submit" class="btn btn-primary"
                                                                    id="btnsubmit">Submit Request</button>
                                                            </td>
                                                        </tr>
                                                    </table>

                                                </form>
                                            </div>
                                       
                                                </table>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Main row -->

                        <!-- /.container-fluid -->

                        <!-- /.content -->
                    </div>


                    <!-- Control Sidebar -->
                    <aside class="control-sidebar control-sidebar-dark">
                        <!-- Control sidebar content goes here -->
                    </aside>
                    <!-- /.control-sidebar -->
                </div>
                <!-- ./wrapper -->
                <script>
                    $.widget.bridge("uibutton", $.ui.button);
                </script>
                <!-- jQuery -->
                <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
                <!-- jQuery UI -->
                <script src="https://code.jquery.com/ui/1.12.1/jquery-ui.min.js"></script>
                <!-- Bootstrap 4 -->
                <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.bundle.min.js"></script>
                <!-- Select2 -->
                <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>

                <!-- Moment.js -->
                <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.1/moment.min.js"></script>

                <!-- Tempusdominus Bootstrap 4 -->
                <script
                    src="https://cdnjs.cloudflare.com/ajax/libs/tempusdominus-bootstrap-4/5.1.2/js/tempusdominus-bootstrap-4.min.js"></script>

                <!-- OverlayScrollbars -->
                <script
                    src="https://cdnjs.cloudflare.com/ajax/libs/overlayscrollbars/1.13.0/js/jquery.overlayScrollbars.min.js"></script>

                <!-- AdminLTE App -->
                <script src="https://cdnjs.cloudflare.com/ajax/libs/admin-lte/3.2.0/js/adminlte.min.js"></script>

                <!-- jQuery Validation -->
                <script
                    src="https://cdnjs.cloudflare.com/ajax/libs/jquery-validate/1.19.3/jquery.validate.min.js"></script>
                <script
                    src="https://cdnjs.cloudflare.com/ajax/libs/jquery-validate/1.19.3/additional-methods.min.js"></script>

                <!-- Toastr -->
                <script src="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/js/toastr.min.js"></script>

                <!-- SweetAlert2 -->
                <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

                <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
                <!-- boxicon -->
                <script src="https://unpkg.com/boxicons@2.1.4/dist/boxicons.js"></script>

                <!-- FileSaver.js -->
                <script src="https://cdnjs.cloudflare.com/ajax/libs/FileSaver.js/2.0.5/FileSaver.min.js"></script>
                <!-- jsPDF -->
                <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
                <!-- js-xlsx -->
                <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.core.min.js"></script>
                <!-- tableExport.js -->
                <script src="https://cdn.jsdelivr.net/npm/tableexport.jquery.plugin@1.30.0/tableExport.min.js"></script>
                <script>
                    const token = localStorage.getItem("access-token-new");
                    var userDetails = localStorage.getItem('user-data');
                    var userDetailsObj = JSON.parse(userDetails);
                    var departmentUsername = userDetailsObj.department_username
                    console.log(departmentUsername)
                    var universityName = userDetailsObj.university
                    $('#university1').append(universityName);
                    $('#college1').append(universityName);
                    const apiBaseUrl = "<%= process.env.apiBaseUrl %>";
                    function doExport() {
                        $('#mainTable').tableExport({
                            type: 'excel',
                            mso: {
                                styles: ['background-color', 'color', 'font-family', 'font-size', 'font-weight', 'text-align']
                            }
                        });
                    }
                    function doExportList() {
                        $('#mainTable2').tableExport({
                            type: 'excel',
                            mso: {
                                styles: ['background-color', 'color', 'font-family', 'font-size', 'font-weight', 'text-align']
                            }
                        });
                    }
                    $(document).ready(function () {
                        const apiBaseUrl = "<%= process.env.apiBaseUrl %>";
                        $.ajax({

                            url: apiBaseUrl + "v1/absentee/get-month",
                            type: 'get',
                            dataType: 'json',
                            headers: {
                                token: localStorage.getItem("access-token-new"),
                                "Content-Type": "application/json"
                            },
                            success: function (response) {
                                if (response.status === "success") {
                                    var months = response.data;
                                    $.each(months, function (index, monthData) {
                                        var monthYearText = monthData.month_name + " " + monthData.year;
                                        $('#monthYearDropdown').append($('<option>', {
                                            value: monthYearText,
                                            text: monthYearText
                                        }));
                                    });
                                } else {
                                    console.error('API request failed with status:', response.status);
                                }
                            },
                            error: function (xhr, status, error) {
                                console.error('Error fetching data from API:', error);
                            }
                        });
                    })
                </script>
                <script>
                    $(function () {
                        $('.select2').select2();
                    });
                    $(document).ready(function () {
                        const apiBaseUrl = "<%= process.env.apiBaseUrl %>";
                        $('#btnsearch').click(function () {
                            const apiBaseUrl = "<%= process.env.apiBaseUrl %>";
                            var employeeType = $('#employeeTypeDropdown').val();
                            var monthYearDropdown = $('#monthYearDropdown').val();
                         
                            if (!employeeType) {
                                alert("Please select the Employee Type.");
                                return;
                            }
                            $.ajax({
                                url: apiBaseUrl + "v1/maker/get-pension-maker-data",
                                method: "POST",
                                contentType: "application/json",
                                data: JSON.stringify({
                                    "employeeType": employeeType,
                                   "date": monthYearDropdown
                                }),
                                headers: {
                                    "token": localStorage.getItem("access-token-new")
                                },
                                success: function (response) {
                                    if (response.status === "success") {
                                        var absenteeData = response.data;
                                        $('#thead').empty();
                                        $('#tbody').empty();
                                        $('#thead').append(`
                     <tr>
                             <th>Sr. No</th>
                            <th>PPO No.</th>
                            <th>Full Name</th>
                            <th>College Name</th>
                            <th>Action Taken</th>
                        </tr>
                    `);
                    absenteeData.forEach(function (employee, index) {
                                        $('#tbody').append(`
                            <tr>
                                 <td>${index + 1}</td>
                                <td>${employee.ppo_no}</td>
                                <td>${employee.full_name}</td>
                                <td>${employee.college_name_retirement}</td>
                                <td>
                                    <button 
                                        type="button" 
                                        class="btn btn-primary" 
                                        onclick="viewData('${employee.ppo_no}')">
                                        View
                                    </button>
                                </td>
                            </tr>
                        `);
                                        });
                                    } else {
                                        alert("Error fetching data");
                                    }
                                },
                                error: function (error) {
                                    alert("An error occurred while fetching data");
                                }
                            });
                        });
                    });
                    function refreshTable() {
                        var employeeType = $('#employeeTypeDropdown').val();

                        $.ajax({
                            url: apiBaseUrl + "v1/maker/get-pension-maker-data",
                            method: "POST",
                            contentType: "application/json",
                            data: JSON.stringify({
                                "employeeType": employeeType,
                            
                            }),
                            headers: {
                                "token": localStorage.getItem("access-token-new")
                            },
                            success: function (response) {
                                if (response.status === "success") {
                                    var absenteeData = response.data;
                                    $('#thead').empty();
                                    $('#tbody').empty();
                                    $('#thead').append(`
                        <tr>
                             <th>Sr. No</th>
                            <th>PPO No</th>
                            <th>Full Name</th>
                            <th>College Name</th>
                            <th>Action Taken</th>
                        </tr>
                    `);
                    absenteeData.forEach(function (employee, index) {
                                        $('#tbody').append(`
                            <tr>
                                 <td>${index + 1}</td>
                                  <td>${employee.ppo_no}</td>
                                <td>${employee.full_name}</td>
                                <td>${employee.college_name_retirement}</td>
                                <td>
                                    <button 
                                        type="button" 
                                        class="btn btn-primary" 
                                        onclick="viewData('${employee.ppo_no}')">
                                        View
                                    </button>
                                </td>
                            </tr>
                        `);
                                    });
                                } else {
                                    alert("Error fetching data");
                                }
                            },
                            error: function (error) {
                                alert("An error occurred while fetching data");
                            }
                        });
                    }
                    function viewData(ppo_no) {
                        var date = $('#monthYearDropdown').val();
                        $.ajax({
                            url: apiBaseUrl + "v1/maker/get-indi-pension-data",
                            method: "POST",
                            contentType: "application/json",
                            data: JSON.stringify({
                                ppoNo: ppo_no,
                                date: date
                            }),
                            headers: {
                                "token": localStorage.getItem("access-token-new")
                            },
                            success: function (response) {
                                const pensionDetails = response.data[0];
                    
                                $('#payIdNo').val(pensionDetails.pay_id_no);
        $('#firstName').val(pensionDetails.first_name);
        $('#middleName').val(pensionDetails.middle_name);
        $('#lastName').val(pensionDetails.last_name);
        $('#gender').val(pensionDetails.gender);
        $('#dateOfBirth').val(pensionDetails.date_of_birth);
        $('#fathersName').val(pensionDetails.fathers_name);
        $('#mothersName').val(pensionDetails.mothers_name);
        $('#spouseName').val(pensionDetails.spouse_name);
        $('#emailId').val(pensionDetails.email_id);
        $('#mobileNo').val(pensionDetails.mobile_no);
        $('#aadharNo').val(pensionDetails.aadhar_no);
        $('#panNo').val(pensionDetails.pan_no);
        $('#ppoNo').val(pensionDetails.ppo_no);
        $('#pranNo').val(pensionDetails.pran_no);
        $('#employeeType').val(pensionDetails.employee_type);
        $('#appointmentLetter').val(pensionDetails.appointment_letter);
        $('#effectiveDateJoining').val(pensionDetails.effective_date_joining);
        $('#dateRetirement').val(pensionDetails.date_retirement);
        $('#universityRetirement').val(pensionDetails.university_name_retirement);
        $('#collegeRetirement').val(pensionDetails.college_name_retirement);
        $('#bankAccountNo').val(pensionDetails.bank_account_no);
        $('#bankName').val(pensionDetails.bank_name);
        $('#branchName').val(pensionDetails.branch_name);
        $('#ifscCode').val(pensionDetails.ifsc_code);
        $('#basicPension').val(pensionDetails.basic_pension);
        $('#dearnessRelief').val(pensionDetails.dearness_relief_percentage);
        $('#medicalAllowance').val(pensionDetails.medical_allowance);
        $('#gratuity').val(pensionDetails.gratuity);
        $('#leaveEncashment').val(pensionDetails.leave_encashment);
        $('#arrears').val(pensionDetails.arrears);
        $('#oneTimeBenefits').val(pensionDetails.all_one_time_benifit_recieved);
        $('#payLevel').val(pensionDetails.pay_level);
                                $('#modalresolved').modal('show');
                            
                             
                            },
                            error: function (error) {
                                alert("An error occurred while fetching absentee details.");
                            }
                        });
                    }
                    $('#frmupdate').on('submit', function (event) {
                        event.preventDefault();
                        const data = {
                            payIdNo: $('#salaryNo').val(),
                            fullName: $('#fullName').val(),
                            dateOfBirth: $('#dateOfBirth').val(),
                            gender: $('#gender').val(),
                            aadharCardNo: $('#aadharCardNo').val(),
                            employeeType: $('#employeeType').val(),
                            dateOfRetirement: $('#dateOfRetirement').val(),
                            collegeName: $('#currentlyPostedAtUniversityOfficeDepartmentCollege').val(),
                            branchName: $('#branchName').val(),
                            bankAccountNo: $('#bankAccountNo').val(),
                            bankName: $('#bankName').val(),
                            ifscCode: $('#ifscCode').val(),
                            payLevel: $('#payLevel').val(),
                            basicSalary: $('#basicSalary').val(),
                            basicSalaryFixed: $('#basicSalaryFixed').val(),
                            hr: $('#hra').val(),
                            cta: $('#cta').val(),
                            da: $('#da').val(),
                            medicalAllowance: $('#medicalAllowance').val(),
                            npsOpted: $('#npsOpted').val(),
                            nps: $('#nps').val(),
                            specialOtherAllowance: $('#specialOtherAllowance').val(),
                            incomeTax: $('#incomeTax').val(),
                            pf: $('#pf').val(),
                            pfLoan: $('#pfLoan').val(),
                            lic: $('#lic').val(),
                            gip: $('#gip').val(),
                            lastPaymentWithdrawnSalary: $('#lastPaymentWithdrawnSalary').val(),
                            lastPaymentWithdrawnSalaryDeduction: $('#lastPaymentWithdrawnSalarydeduction').val(),
                            annualOptedDate: $('#annualOptedDate').val(),
                            date: $('#absentee_date').val(),
                            otherdeduction: $('#otherdeduction').val() || '',
                             grosssalary: $('#grosssalary').val() || '',
                             netpaysalary: $('#netpaysalary').val() || '',
                              professionalTax: $('#professionalTax').val() || '',
                              ctapercentage: $('#ctapercentage').val() || '',
                              daPercentage: $('#daPercentage').val() || '',
                              HRAPercentage: $('#HRAPercentage').val() || '',
                            approvedBy: "MAKER",
                            universityName: universityName,
                            submittedBy: departmentUsername
                        };
                        $.ajax({
                            url: apiBaseUrl + "v1/maker/save-maker-data",
                            type: 'POST',
                            contentType: 'application/json',
                            data: JSON.stringify(data),
                            headers: {
                                "token": localStorage.getItem("access-token-new")
                            },
                            success: function (response) {
                                Swal.fire({
                                    icon: 'success',
                                    title: 'Success',
                                    text: 'Request submitted successfully to Payment Checker!',
                                });
                                $('#modalresolved').modal('hide');
                                refreshTable();
                            },
                            error: function (error) {
                                alert('Error submitting request.');
                                console.log(error);
                            }
                        });
                    });
                </script>
             <script>
                function isTokenExpired(token) {
                    if (!token) return true;
                    try {
                        // Decode JWT payload
                        const payloadBase64 = token.split('.')[1];
                        const payload = JSON.parse(atob(payloadBase64));
                        // Check if current time is past the expiration time
                        const isExpired = payload.exp * 1000 < Date.now();
                        return isExpired;
                    } catch (error) {
                        console.error("Failed to decode token:", error);
                        return true; 
                    }
                }
                // Check token expiration on load
                $(function () {
                    const token = localStorage.getItem("access-token-new");
                    const userData = JSON.parse(userDetails);
                    if (token && userData) {
                        // Check role and token expiration
                        if (usersData.role !== "Maker-Login" || isTokenExpired(token)) {
                            alert("Session expired or unauthorized access. Redirecting to login.");
                            window.location = "/";
                        } else {
                            setInterval(() => {
                                if (isTokenExpired(token)) {
                                    alert("Session expired. Please login again.");
                                    window.location = "/";
                                }
                            }, 60000);
                        }
                    } else {
                        window.location = "/";
                    }
                });
                                </script> 