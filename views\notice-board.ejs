<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title><%= title %></title>
    <!-- Google Font: Source Sans Pro -->
    <link
      rel="stylesheet"
      href="https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,400i,700&display=fallback"
    />
    <!-- Font Awesome -->
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css"
    />
    <!-- Ionicons -->
    <link
      rel="stylesheet"
      href="https://code.ionicframework.com/ionicons/2.0.1/css/ionicons.min.css"
    />
    <!-- Tempusdominus Bootstrap 4 -->
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/tempusdominus-bootstrap-4/5.39.0/css/tempusdominus-bootstrap-4.min.css"
    />
    <!-- iCheck -->
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/icheck-bootstrap/3.0.1/icheck-bootstrap.min.css"
    />
    <link
      rel="stylesheet"
      href="https://cdn.jsdelivr.net/npm/select2@4.0.13/dist/css/select2.min.css"
    />
    <!-- Theme style -->
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/admin-lte/3.1.0/css/adminlte.min.css"
    />
    <!-- overlayScrollbars -->
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/overlayscrollbars/1.13.0/css/OverlayScrollbars.min.css"
    />
    <!-- Baoxicon -->
    <link
      href="https://unpkg.com/boxicons@2.1.4/css/boxicons.min.css"
      rel="stylesheet"
    />
    <style type="text/css">
    </style>
  </head>

  <body class="hold-transition sidebar-mini layout-fixed sidebar-collapse">
    <div class="wrapper">
      <!-- Preloader -->
      <div
        class="preloader flex-column justify-content-center align-items-center"
      >
        <img
          class="animation__shake"
          src="/images/loader.gif"
          alt="CodeBucket"
          height="100"
          width="100"
        />
      </div>
      <!-- Navbar -->
      <%- include('./partials/header'); %>
      <!-- Main Sidebar Container -->
      <%- include('./partials/sidebar'); %>
      <!-- Content Wrapper. Contains page content -->
      <div class="content-wrapper">
        <!-- Content Header (Page header) -->
        <div class="content-header">
          <div class="container-fluid">
            <div class="row mb-2">
              <div class="col-sm-6"></div>
              <div class="col-sm-6">
                <ol class="breadcrumb float-sm-right">
                  <li class="breadcrumb-item"><a href="/logout">Signout</a></li>
                </ol>
              </div>
            </div>
          </div>
        </div>
        <section class="content">
          <div class="container-fluid">
            <div class="row">
              <section class="col-12">
                <div
                  class="modal fade"
                  id="noticeModal"
                  tabindex="-1"
                  role="dialog"
                  aria-labelledby="noticeModalLabel"
                  aria-hidden="true"
                >
                  <div class="modal-dialog" role="document">
                    <div class="modal-content">
                      <div class="modal-header">
                        <h3 class="modal-title" id="noticeModalLabel">
                          Notice:
                        </h3>
                        <button
                          type="button"
                          class="close"
                          data-dismiss="modal"
                          aria-label="Close"
                        >
                          <span aria-hidden="true">&times;</span>
                        </button>
                      </div>
                      <div class="modal-body">
                        <img
                          src="/images/images.jpg"
                          alt="Notice Image"
                          class="img-fluid"
                        />
                        <p style="color: red; font-weight: bolder">
                          Now adding name of employees in portal can be done by
                          University Admin only. Colleges can view record and
                          upload Monthly Attendance. Now login is possible with
                          OTP and OTP will go to the registered mobile number of
                          principal of the college. If OTPs are not coming
                          please email the phone number of Principal of the
                          college on this email id
                          <a href="mailto:<EMAIL>"
                            ><EMAIL></a
                          >.
                        </p>
                        <br />
                        <p>
                          <b
                            >अब पोर्टल में कर्मचारियों के नाम केवल विश्वविद्यालय
                            प्रशासन द्वारा ही जोड़े जा सकते हैं। कॉलेज रिकॉर्ड
                            देख सकते हैं और मासिक उपस्थिति अपलोड कर सकते हैं। अब
                            लॉगिन ओटीपी के माध्यम से संभव है, और ओटीपी कॉलेज के
                            प्राचार्य के पंजीकृत मोबाइल नंबर पर भेजा जाएगा। यदि
                            ओटीपी नहीं आ रहे हैं, तो कृपया कॉलेज के प्राचार्य का
                            फोन नंबर इस ईमेल आईडी पर भेजें
                            <a href="mailto:<EMAIL>"
                              ><EMAIL></a
                            ></b
                          >
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </section>
            </div>
          </div>
        </section>
      </div>
      <%- include('./partials/footer'); %>
      <!-- Control Sidebar -->
      <aside class="control-sidebar control-sidebar-dark">
      </aside>
    </div>
    <script>
      $.widget.bridge("uibutton", $.ui.button);
    </script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- jQuery UI -->
    <script src="https://code.jquery.com/ui/1.12.1/jquery-ui.min.js"></script>
    <!-- Bootstrap 4 -->
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.bundle.min.js"></script>
    <!-- Select2 -->
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
    <!-- Moment.js -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.1/moment.min.js"></script>
    <!-- Tempusdominus Bootstrap 4 -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/tempusdominus-bootstrap-4/5.1.2/js/tempusdominus-bootstrap-4.min.js"></script>
    <!-- OverlayScrollbars -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/overlayscrollbars/1.13.0/js/jquery.overlayScrollbars.min.js"></script>
    <!-- AdminLTE App -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/admin-lte/3.2.0/js/adminlte.min.js"></script>
    <!-- jQuery Validation -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-validate/1.19.3/jquery.validate.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-validate/1.19.3/additional-methods.min.js"></script>
    <!-- Toastr -->
    <script src="https://unpkg.com/boxicons@2.1.4/dist/boxicons.js"></script>
    <script>
      var apiBaseUrl = "<%= process.env.apiBaseUrl %>";
      $(document).ready(function () {
        $("#noticeModal").modal("show");
      });
    </script>
  </body>
</html>
