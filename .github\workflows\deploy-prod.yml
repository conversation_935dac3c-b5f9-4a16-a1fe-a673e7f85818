name: Build and Push Docker Image

on:
  push:
    branches:
      - main

env:
  DOCKER_REPO: university_hrms_frontend
  DOCKER_IMAGE: prod

jobs:
  build:
    runs-on: ${{ vars.DOCKER_BUILD_RUNNER }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Log in to Harbor
        run: echo "${{ secrets.HARBOR_PASSWORD }}" | docker login https://harbor.internal.codebuckets.in -u '${{ secrets.HARBOR_USER }}' --password-stdin

      - name: Build Docker image
        run: docker build -t harbor.internal.codebuckets.in/${{ env.DOCKER_REPO }}/${{ env.DOCKER_IMAGE }}:latest . -f Dockerfile

      - name: Push Docker image
        run: docker push harbor.internal.codebuckets.in/${{ env.DOCKER_REPO }}/${{ env.DOCKER_IMAGE }}:latest


  deploy:
    runs-on: sdc-education-1
    needs: build
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Log in to Harbor
        run: echo "${{ secrets.HARBOR_PASSWORD }}" | docker login https://harbor.internal.codebuckets.in -u '${{ secrets.HARBOR_USER }}' --password-stdin

      - name: Pull new Image
        run: docker compose -f /home/<USER>/University-HRMS-Frontend/docker-compose.yml pull

      - name: Remove old containers
        run: docker compose -f /home/<USER>/University-HRMS-Frontend/docker-compose.yml down || true

      - name: Start Container
        run: docker compose -f /home/<USER>/University-HRMS-Frontend/docker-compose.yml up -d
