.card1 {
	overflow: hidden;
	position: fixed;
	background-color: wheat;
	backdrop-filter: blur(8px);
	margin-left: 1150px;
	margin-top: 450px;
	}
	
	.wrap {
	display: flex;
	flex-direction: column;
	gap: 1rem;
	position: relative;
	z-index: 10;
	border: 0.5px solid #525252;
	border-radius: 8px;
	overflow: hidden;
	}
	
	.terminal {
	display: flex;
	flex-direction: column;
	font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas,
	"Liberation Mono", "Courier New", monospace;
	}
	
	.head {
	display: flex;
	align-items: center;
	justify-content: space-between;
	overflow: hidden;
	min-height: 40px;
	padding-inline: 12px;
	border-top-left-radius: 8px;
	border-top-right-radius: 8px;
	background-color: #202425;
	}
	
	.title {
	display: flex;
	align-items: center;
	gap: 8px;
	height: 2.5rem;
	user-select: none;
	font-weight: 600;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
	color: wheat;
	}
	
	.title > svg {
	height: 18px;
	width: 18px;
	margin-top: 2px;
	color: #006adc;
	}
	
	.copy_toggle {
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 0.25rem;
	border: 0.65px solid #c1c2c5;
	margin-left: auto;
	border-radius: 6px;
	background-color: #202425;
	color: #8e8e8e;
	cursor: pointer;
	}
	
	.copy_toggle > svg {
	width: 20px;
	height: 20px;
	}
	
	.copy_toggle:active > svg > path,
	.copy_toggle:focus-within > svg > path {
	animation: clipboard-check 500ms linear forwards;
	}
	
	.body {
	display: flex;
	flex-direction: column;
	position: relative;
	border-bottom-right-radius: 8px;
	border-bottom-left-radius: 8px;
	overflow-x: auto;
	padding: 1rem;
	line-height: 19px;
	color: white;
	background-color: black;
	white-space: nowrap;
	}
	
	.pre {
	display: flex;
	flex-direction: row;
	align-items: center;
	text-wrap: nowrap;
	white-space: pre;
	background-color: transparent;
	overflow: hidden;
	box-sizing: border-box;
	}
	
	.pre code:nth-child(1) {
	color: white;
	}
	
	.pre code:nth-child(2) {
	color: white;
	}
	
	.cmd {
	height: 19px;
	position: relative;
	display: flex;
	align-items: center;
	flex-direction: row;
	}
	
	.cmd::before {
	content: attr(data-cmd);
	position: relative;
	display: block;
	white-space: nowrap;
	overflow: hidden;
	background-color: transparent;
	animation: inputs 8s steps(22) infinite;
	}
	
	.cmd::after {
	content: "";
	position: relative;
	display: block;
	height: 100%;
	overflow: hidden;
	background-color: transparent;
	border-right: 0.15em solid white;
	animation: cursor 0.5s step-end infinite alternate, blinking 0.5s infinite;
	}
	
	@keyframes blinking {
	20%,
	80% {
	transform: scaleY(1);
	}
	
	50% {
	transform: scaleY(0);
	}
	}
	
	@keyframes cursor {
	50% {
	border-right-color: transparent;
	}
	}
	
	@keyframes inputs {
	0%,
	100% {
	width: 0;
	}
	
	10%,
	90% {
	width: 58px;
	}
	
	30%,
	70% {
	width: 215px;
	max-width: max-content;
	}
	}
	
	@keyframes clipboard-check {
	100% {
	color: #fff;
	d: path(
	"M 9 5 H 7 a 2 2 0 0 0 -2 2 v 12 a 2 2 0 0 0 2 2 h 10 a 2 2 0 0 0 2 -2 V 7 a 2 2 0 0 0 -2 -2 h -2 M 9 5 a 2 2 0 0 0 2 2 h 2 a 2 2 0 0 0 2 -2 M 9 5 a 2 2 0 0 1 2 -2 h 2 a 2 2 0 0 1 2 2 m -6 9 l 2 2 l 4 -4"
	);
	}
	}
	
	.button {
	width: fit-content;
	gap: 0.4rem;
	border: none;
	font-weight: bold;
	padding: 10px;
	border-radius: 30px;
	cursor: pointer;
	text-shadow: 2px 2px 3px rgb(136 0 136 / 50%);
	background: linear-gradient(
	15deg,
	#880088,
	#aa2068,
	#cc3f47,
	#de6f3d,
	#f09f33,
	#de6f3d,
	#cc3f47,
	#aa2068,
	#880088
	)
	no-repeat;
	background-size: 300%;
	background-position: left center;
	transition: background 0.3s ease;
	color: #fff;
	margin-top: 25px;
	margin-left: 45px;
	}
	
	.button:hover {
	background-size: 320%;
	background-position: right center;
	}
	
	.button:hover svg {
	fill: #fff;
	}
	
	.button svg {
	width: 23px;
	fill: #f09f33;
	transition: 0.3s ease;
	}
	
	.alert {
	color: #3c763d;
	padding: 15px;
	margin-bottom: 20px;
	border: 1px solid transparent;
	border-radius: 4px;
	}
	
	.alert a {
	text-decoration: none;
	}
	
	#h1 {
	margin-top: 0;
	margin-bottom: 0;
	}
	
	@media only screen and (max-width: 600px) {
	.alert {
	font-size: 14px;
	padding: 10px;
	}
	
	.alert a {
	text-decoration: none;
	}
	
	#h1 {
	font-size: 13px;
	}
	}
	
	.input-group-text {
	color: red;
	}
	
	#btnlogin {
	font-size: large;
	font-weight: 700;
	color: white;
	margin-left: 170px;
	margin-top: 15px;
	height: 18%;
	background: linear-gradient(144deg, #af40ff, #5b42f3 50%, #00ddeb);
	border-radius: 8px;
	border: none;
	padding-top: 10px;
	padding-bottom: 10px;
	box-shadow: inset 1px 3px 3px #ffffffbd, inset -4px -4px 3px #00000046;
	background-size: 150% 150%;
	animation: input 5s infinite;
	transition: all 900ms ease-in;
	}
	
	#btnlogin:hover {
	position: relative;
	bottom: 3px;
	background: linear-gradient(144deg, #9706ff, #2f0fff 50%, #18f0ff);
	}
	
	.card-footer {
	background-color: black;
	color: white;
	}
	
	.card-footer a {
	color: red;
	}
	#frmotp {
	width: 370px;
	height: 370px;
	background-color: rgb(255, 255, 255);
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 20px 30px;
	gap: 20px;
	position: relative;
	box-shadow: 0px 0px 20px rgba(0, 0, 0, 0.082);
	border-radius: 15px;
	}
	
	.mainHeading {
	font-size: 1.1em;
	color: rgb(15, 15, 15);
	font-weight: 700;
	}
	
	.otpSubheading {
	color: RED;
	line-height: 17px;
	text-align: center;
	}
	
	.inputContainer {
	width: 100%;
	display: flex;
	flex-direction: row;
	gap: 10px;
	align-items: center;
	justify-content: center;
	}
	
	.otp-input {
	background-color: rgb(228, 228, 228);
	width: 30px;
	height: 30px;
	text-align: center;
	border: none;
	border-radius: 7px;
	caret-color: rgb(127, 129, 255);
	color: rgb(44, 44, 44);
	outline: none;
	font-weight: 600;
	}
	
	.otp-input:focus,
	.otp-input:valid {
	background-color: rgba(127, 129, 255, 0.199);
	transition-duration: 0.3s;
	}
	
	#btnverify {
	width: 100%;
	border: none;
	color: white;
	font-weight: 600;
	cursor: pointer;
	border-radius: 10px;
	transition-duration: 0.2s;
	}
	
	.verifyButton:hover {
	background-color: rgb(144, 145, 255);
	transition-duration: 0.2s;
	}
	
	.exitBtn {
	position: absolute;
	top: 5px;
	right: 5px;
	box-shadow: 0px 0px 20px rgba(0, 0, 0, 0.171);
	background-color: rgb(255, 255, 255);
	border-radius: 50%;
	width: 25px;
	height: 25px;
	border: none;
	color: black;
	font-size: 1.1em;
	cursor: pointer;
	}
	
	.resendNote {
	color: black;
	width: 100%;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	gap: 5px;
	}
	
	.resendBtn {
	background-color: transparent;
	border: none;
	color: rgb(127, 129, 255);
	cursor: pointer;
	font-size: 1.1em;
	font-weight: 700;
	}
	
	.input-wrapper {
	position: relative;
	width: 50%;
	max-width: 300px;
	padding: 3px;
	border-radius: 1.7rem;
	overflow: hidden;
	}
	
	.input-wrapper input {
	background-color: #f5f5f5;
	border: 2px solid #ddd;
	padding: 1.2rem 1rem 1.2rem 3rem;
	font-size: 1.1rem;
	width: 100%;
	border-radius: 1.5rem;
	color: #ff7f7f;
	box-shadow: 0 0.4rem #dfd9d9, inset 0 0 0 transparent;
	transition: all 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
	position: relative;
	z-index: 2;
	}
	
	.input-wrapper input:focus {
	outline: none;
	border-color: #4a90e2;
	box-shadow: 0 0.6rem #dfd9d9, 0 0 15px rgba(74, 144, 226, 0.7);
	transform: translateY(-3px) scale(1.01);
	}
	
	.input-wrapper input::placeholder {
	color: #a0c0e8;
	transition: all 0.3s ease;
	}
	
	.input-wrapper input:focus::placeholder {
	opacity: 0;
	transform: translateX(10px);
	}
	
	.input-wrapper::after {
	content: "😎";
	position: absolute;
	left: 1rem;
	top: 50%;
	transform: translateY(-50%);
	font-size: 1.2rem;
	z-index: 3;
	transition: all 0.3s ease;
	}
	
	@keyframes dance {
	0%,
	100% {
	transform: translateY(-50%) rotate(0deg);
	}
	
	25% {
	transform: translateY(-50%) rotate(-20deg) scale(1.1);
	}
	
	75% {
	transform: translateY(-50%) rotate(20deg) scale(1.1);
	}
	}
	
	.input-wrapper:hover::after {
	animation: dance 0.5s ease infinite;
	}
	
	.input-wrapper:focus-within::after {
	content: "🙈";
	animation: dance 0.3s ease infinite;
	}
	
	.input-wrapper input::placeholder {
	color: #ccc;
	transition: all 0.3s ease;
	}
	
	.input-wrapper input:focus::placeholder {
	opacity: 0;
	transform: translateX(10px);
	}
	
	.input-wrapper::before {
	content: "";
	position: absolute;
	top: -50%;
	left: -50%;
	width: 200%;
	height: 200%;
	background: conic-gradient(
	from 0deg,
	#4a90e2,
	#6aa9e9,
	#8bc1f0,
	#add9f7,
	#d0f0ff,
	#add9f7,
	#8bc1f0,
	#6aa9e9,
	#4a90e2
	);
	animation: rotate 4s linear infinite;
	opacity: 0;
	transition: opacity 0.3s ease;
	z-index: 1;
	}
	
	.input-wrapper:hover::before,
	.input-wrapper:focus-within::before {
	opacity: 1;
	}
	
	@keyframes rotate {
	100% {
	transform: rotate(360deg);
	}
	}
	
	@keyframes shockwave {
	0% {
	transform: scale(1);
	box-shadow: 0 0 0 0 rgba(255, 127, 127, 0.4);
	}
	
	70% {
	transform: scale(1.02);
	box-shadow: 0 0 0 20px rgba(255, 127, 127, 0);
	}
	
	100% {
	transform: scale(1);
	box-shadow: 0 0 0 0 rgba(255, 127, 127, 0);
	}
	}
	
	.input-wrapper:focus-within {
	animation: shockwave 0.5s ease-out;
	}
	
	.input-wrapper {
	--label-size: 0.8rem;
	--label-transform: translateY(-170%) scale(0.8);
	}
	.input-wrapper input:placeholder-shown + label {
	transform: translateY(-50%);
	font-size: 1rem;
	}
	.input-wrapper label {
	position: absolute;
	left: 1rem;
	top: 50%;
	transform: var(--label-transform);
	font-size: var(--label-size);
	color: #ff7f7f;
	transition: all 0.3s ease;
	pointer-events: none;
	z-index: 3;
	}
	
	.input-wrapper input:not(:placeholder-shown) + label,
	.input-wrapper input:focus + label {
	transform: var(--label-transform);
	font-size: var(--label-size);
	}
	.login-box {
	margin-top: 50px;
	}
	.sticky-header {
	margin-top: 90px;
	top: 0;
	z-index: 1030; 
	border-bottom: 1px solid #ccc; 
	}
	.logo-left {
	flex: 0 0 auto; 
	margin-right: 350px; 
	}
	
	.logo-center {
	flex: 1 1 auto; 
	}
	
	.logo-right {
	flex: 0 0 auto;
	margin-left: 350px;
	}
	
	.page-wrapper {
	bottom: 0;
	width: 100%;
	z-index: 11111;
	top: 35px;
	}
	
	footer p,
	footer strong,
	footer b,
	footer {
	color: #b0b0b0;
	}
	
	.footer-top {
	background: #303030;
	background-size: cover;
	font-family: rubik;
	}
	
	.footer-top,
	.footer-bottom {
	background-color: #1c1f2f;
	}
	
	.footer-bottom {
	padding: 15px 0;
	border-top: 1px solid #313646;
	background-color: #181828 !important;
	color: #b0b0b0;
	font-family: rubik;
	}
	
	.footer-site-info {
	font-size: 92.86%;
	}
	#footer-navigation,
	#footer-navigation li a:hover,
	.custom-footer,
	.custom-footer li a:hover {
	color: white;
	}
	
	#footer-navigation,
	#footer-navigation li a,
	.custom-footer,
	.custom-footer li a {
	color: #99a9b5;
	padding-top: 15px;
	}
	
	.footer-bottom ul {
	margin: 0;
	}
	.inline-inside {
	font-size: 0;
	line-height: 0;
	}
	.clearfix:after,
	.clearfix:before {
	content: "";
	display: table;
	}
	#footer-menu li {
	display: inline-block;
	padding: 0 21px;
	position: relative;
	line-height: 1;
	}
	
	#footer-navigation,
	#footer-navigation li a,
	.custom-footer,
	.custom-footer li a {
	color: #99a9b5;
	padding-top: 15px;
	}
	
	#footer-navigation,
	#footer-navigation li a,
	.custom-footer,
	.custom-footer li a {
	color: white;
	padding-top: 15px;
	}
	#footer-menu li + li:before {
	content: "";
	width: 0;
	height: 100%;
	position: absolute;
	left: -1px;
	top: 0;
	font-size: 0;
	border-left: 1px solid #232234;
	border-right: 1px solid #333146;
	}
	
	navigation li a,
	.custom-footer,
	.custom-footer li a {
	color: #99a9b5;
	padding-top: 15px;
	}
	
	#footer-socials {
	text-align: right;
	}
	
	#footer-socials .socials {
	text-align: right;
	margin: 0 -7px;
	display: inline-block;
	vertical-align: middle;
	}
	
	a.socials-item {
	display: inline-block;
	vertical-align: top;
	text-align: center;
	-o-transition: all 0.3s;
	-webkit-transition: all 0.3s;
	transition: all 0.3s;
	margin: 0 5px;
	line-height: 16px;
	padding: 10px;
	border-radius: 50%;
	background-color: #141421;
	border: 1px solid #2e2e4c;
	box-shadow: 3px 9px 16px rgb(0, 0, 0, 0.4),
	-3px -3px 10px rgba(255, 255, 255, 0.06), inset 14px 14px 26px rgb(0, 0, 0, 0.3),
	inset -3px -3px 15px rgba(255, 255, 255, 0.05);
	}
	
	.socials-item i {
	display: inline-block;
	font-weight: normal;
	width: 1em;
	height: 1em;
	line-height: 1;
	font-size: 16px;
	text-align: center;
	vertical-align: top;
	font-feature-settings: normal;
	font-kerning: auto;
	font-language-override: normal;
	font-size-adjust: none;
	font-stretch: normal;
	font-style: normal;
	font-synthesis: weight style;
	font-variant: normal;
	font-weight: normal;
	text-rendering: auto;
	}
	
	.facebook {
	color: #4e64b5;
	}
	
	.twitter {
	color: #00aced;
	}
	.instagram {
	color: #9a8f62;
	}
	.youtube {
	color: #c82929;
	}
	
	.telegram {
	color: #2ca5e0;
	}
	
	a.socials-item:hover {
	box-shadow: 0 0px 20px rgba(84, 1, 74, 0.7);
	border-color: rgba(255, 6, 224, 0.61);
	background: linear-gradient(
	to right,
	rgba(255, 9, 9, 0.12941176470588237),
	#c000ffb5,
	rgba(255, 0, 94, 0.14)
	);
	}
	
	.footer-bottom a:hover {
	color: white;
	}
	
	footer p,
	footer li {
	font-size: 15px;
	line-height: 22px;
	}
	
	.widget {
	margin-bottom: 50px;
	}
	
	.footer-title {
	margin-bottom: 40px;
	color: #fff;
	font-weight: 500;
	text-transform: capitalize;
	padding-bottom: 15px;
	font-size: 16px;
	position: relative;
	}
	
	.footer-title:after {
	width: 50px;
	background: #fff;
	opacity: 0.2;
	height: 1px;
	content: "";
	position: absolute;
	bottom: 0;
	left: 0;
	}
	
	.gem-contacts-item {
	padding-top: 10px;
	font-size: 15px;
	}
	
	.gem-contacts-item i {
	padding-right: 10px;
	}
	
	footer .widget ul {
	list-style: none;
	margin-top: 5px;
	}
	
	.posts li {
	border-bottom: 1px solid #393d50;
	padding-bottom: 12px;
	padding-top: 6px;
	}
	
	footer p,
	footer li {
	font-size: 15px;
	line-height: 22px;
	}
	
	.gem-pp-posts-date {
	color: #00bcd4;
	font-size: 89.5%;
	}
	
	footer p {
	line-height: 24px;
	margin-bottom: 10px;
	font-size: 18px;
	font-weight: bolder;
	}
	
	.wpcf7-form-control-wrap .wpcf7-form-control {
	padding: 7px !important;
	width: 100%;
	}
	
	.wpcf7-form-control-wrap input {
	background: #1c1f2f;
	overflow: hidden;
	border: 1px solid #2e344d;
	background-color: #1c1f2f;
	box-shadow: 10px 10px 36px rgb(0, 0, 0, 0.5),
	-13px -13px 23px rgba(255, 255, 255, 0.03);
	border-radius: 5px;
	transition: all 0.3s ease-in-out 0s;
	}
	
	.wpcf7-form-control-wrap input:hover {
	background-color: transparent;
	box-shadow: 10px 10px 36px rgb(0, 0, 0, 0.5),
	-13px -13px 23px rgba(255, 255, 255, 0.03),
	inset 14px 14px 70px rgb(0, 0, 0, 0.2),
	inset -15px -15px 30px rgba(255, 255, 255, 0.04);
	border-color: rgba(255, 255, 255, 0.2);
	color: white;
	}
	
	.wpcf7 .wpcf7-form .contact-form-footer textarea {
	height: 160px;
	width: 100%;
	}
	
	.wpcf7-form-control-wrap textarea:hover {
	background-color: transparent;
	box-shadow: 10px 10px 36px rgb(0, 0, 0, 0.5),
	-13px -13px 23px rgba(255, 255, 255, 0.03),
	inset 14px 14px 70px rgb(0, 0, 0, 0.2),
	inset -15px -15px 30px rgba(255, 255, 255, 0.04);
	border-color: rgba(255, 255, 255, 0.2);
	color: white;
	}
	
	.wpcf7-form-control-wrap textarea {
	background: #1c1f2f;
	overflow: hidden;
	border: 1px solid #2e344d;
	background-color: #1c1f2f;
	box-shadow: 10px 10px 36px rgb(0, 0, 0, 0.5),
	-13px -13px 23px rgba(255, 255, 255, 0.03);
	border-radius: 5px;
	transition: all 0.3s ease-in-out 0s;
	}
	
	textarea {
	overflow: auto;
	resize: vertical;
	}
	
	.wpcf7 .wpcf7-form .contact-form-footer .wpcf7-submit {
	width: 100%;
	padding: 11px;
	margin: 0;
	line-height: 0;
	}
	.wpcf7-form .contact-form-footer .wpcf7-submit {
	background-color: #394050;
	color: #99a9b5;
	border: none;
	cursor: pointer;
	padding: 15px 40px;
	font-size: 14px;
	font-weight: 400;
	transition: all 0.5s;
	letter-spacing: 2px;
	color: rgba(255, 255, 255, 0.5);
	box-shadow: none;
	text-transform: uppercase;
	outline: none !important;
	background-color: #1c1f2f;
	border-radius: 5px;
	min-width: 140px;
	/* box-shadow: 10px 10px 36px rgb(0,0,0,0.5), -13px -13px 23px rgba(255,255,255, 0.03), inset 14px 14px 70px rgb(0,0,0,0.2), inset -15px -15px 30px rgba(255,255,255, 0.04); */
	box-shadow: 3px 9px 16px rgb(0, 0, 0, 0.4),
	-3px -3px 10px rgba(255, 255, 255, 0.06), inset 14px 14px 26px rgb(0, 0, 0, 0.3),
	inset -3px -3px 15px rgba(255, 255, 255, 0.05);
	border-width: 1px 0px 0px 1px;
	border-style: solid;
	border-color: #2e344d;
	transition: all 0.3s ease-in-out 0s;
	}
	
	.wpcf7-form input[type="submit"] {
	height: 40px;
	line-height: 21px;
	padding: 10px 40px;
	font-size: 14px;
	}
	
	.posts li a {
	color: #99a9b5;
	}
	
	.wpcf7-form .contact-form-footer .wpcf7-submit:hover {
	box-shadow: 0 0px 20px rgba(84, 1, 74, 0.7);
	border-color: rgba(255, 6, 224, 0.61);
	background: linear-gradient(
	to right,
	rgba(255, 9, 9, 0.12941176470588237),
	#c000ffb5,
	rgba(255, 0, 94, 0.14)
	);
	color: white;
	}
	
	img {
	border-style: none;
	height: auto;
	max-width: 100%;
	vertical-align: middle;
	}
	.widget_gallery a {
	display: inline-block;
	}
	footer .widget ul {
	list-style: none;
	margin-top: 5px;
	}
	.widget_gallery ul {
	padding-left: 0;
	display: table;
	}
	
	.widget_gallery li {
	display: inline-block;
	width: 33.33%;
	float: left;
	transition: all 0.5s;
	-webkit-transition: all 0.5s;
	-moz-transition: all 0.5s;
	-o-transition: all 0.5s;
	padding: 2px;
	}
	
	.widget_gallery.gallery-grid-4 li {
	width: 30%;
	}
	
	#waterdrop {
	height: 30px;
	}
	
	#waterdrop canvas {
	bottom: -70px !important;
	}
	
	.footer-site-info {
	padding-top: 10px;
	}
	

	@media (max-width: 768px) {
		.logo-left img,
		.logo-right img {
			height: 50px; 
			width: auto; 
		}
	
		.logo-center h3 {
			font-size: small;
		}
	
		.sticky-header {
			padding: 5px; 
		}
	
		.sticky-header {
			margin-top: 0px;
			top: 0;
			z-index: 1030;
			border-bottom: 1px solid #ccc;
			padding: 10px;
		}
		
		.container-fluid {
			display: flex;
			justify-content: space-between;
			align-items: center;
			flex-wrap: nowrap; 
		}
		
		.logo-left,
		.logo-right {
			flex: 0 0 auto; 
			margin: 0; 
		}
		
		.logo-center {
			flex: 1 1 auto;
			text-align: center;
		}
		.login-box{
			width: fit-content;
		}
		
	}


