<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>
        <%= title %>
    </title>
    <!-- Google Font: Source Sans Pro -->
    <link rel="stylesheet"
        href="https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,400i,700&display=fallback" />

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css" />

    <!-- Ionicons -->
    <link rel="stylesheet" href="https://code.ionicframework.com/ionicons/2.0.1/css/ionicons.min.css" />


    <!-- Tempusdominus Bootstrap 4 -->
    <link rel="stylesheet"
        href="https://cdnjs.cloudflare.com/ajax/libs/tempusdominus-bootstrap-4/5.39.0/css/tempusdominus-bootstrap-4.min.css" />

    <!-- iCheck -->
    <link rel="stylesheet"
        href="https://cdnjs.cloudflare.com/ajax/libs/icheck-bootstrap/3.0.1/icheck-bootstrap.min.css" />

    <!-- Select2 -->
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/select2@4.0.13/dist/css/select2.min.css" />

    <!-- Theme style -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/admin-lte/3.1.0/css/adminlte.min.css" />

    <!-- overlayScrollbars -->
    <link rel="stylesheet"
        href="https://cdnjs.cloudflare.com/ajax/libs/overlayscrollbars/1.13.0/css/OverlayScrollbars.min.css" />

    <!-- Daterange picker -->
    <link rel="stylesheet" href="https://cdn.datatables.net/1.10.24/css/dataTables.bootstrap4.min.css">


    <!-- DataTables -->
    <link rel="stylesheet" href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap4.min.css">

    <link rel="stylesheet" href="https://cdn.datatables.net/responsive/2.2.9/css/responsive.bootstrap4.min.css">

    <link rel="stylesheet" href="https://cdn.datatables.net/buttons/2.0.1/css/buttons.bootstrap4.min.css">
    <!-- Baoxicon -->
    <link href='https://unpkg.com/boxicons@2.1.4/css/boxicons.min.css' rel='stylesheet'>
    <style type="text/css">
        .seat {
            width: 50px;
        }

        input::-webkit-outer-spin-button,
        input::-webkit-inner-spin-button {
            -webkit-appearance: none;
            margin: 0;
        }

        .card-title {
            font-weight: bold !important;
        }

        em {
            color: red;
        }

        .custom-checkbox {
            display: inline-block;
            width: 25px;
            height: 25px;
            background-color: white;
            border: 2px solid #ddd;
            border-radius: 7px;
            margin-right: 15px;
            vertical-align: middle;
            position: relative;
        }

        .form-check-input:checked+.form-check-label .custom-checkbox {
            background-color: blue;

        }

        .form-check-input:checked+.form-check-label .custom-checkbox::after {
            content: '';
            position: absolute;
            top: 5px;
            left: 9px;
            width: 6px;
            height: 12px;
            border: solid white;
            border-width: 0 2px 2px 0;
            transform: rotate(45deg);
        }

        .form-check-label {
            cursor: pointer;
        }

        .is-invalid {
            border-color: red;
        }

        .modal-fullscreen {
            max-width: 90%;
            /* Full width */
            width: 90%;
            height: 180vh;
            /* Full viewport height */
            margin: 0;
            margin-left: 60px;
        }

        .modal-content {
            height: 100%;
            /* Full height for modal content */
        }
    </style>
</head>

<body class="hold-transition sidebar-mini layout-fixed sidebar-collapse">
    <div class="wrapper">
        <!-- Preloader -->
        <div class="preloader flex-column justify-content-center align-items-center">
            <img class="animation__shake" src="/images/loader.gif" alt="CodeBucket" height="100" width="100" />
        </div>

        <!-- Navbar -->
        <%- include('../partials/header'); %>
            <!-- /.navbar -->

            <!-- Main Sidebar Container -->
            <%- include('../partials/sidebar'); %>

                <!-- Content Wrapper. Contains page content -->
                <div class="content-wrapper">
                    <!-- Content Header (Page header) -->
                    <div class="content-header">
                        <div class="container-fluid">
                            <div class="row mb-2">
                                <div class="col-sm-6">
                                    <h1 class="m-0" style="color:blue; font-size: xx-large; font-weight: bolder;">Payment Checker Verefaction  Dashboard</h1>
                                </div>
                                <!-- /.col -->
                                <div class="col-sm-6">
                                    <ol class="breadcrumb float-sm-right">
                                        <li class="breadcrumb-item"><a href="/logout">Signout</a></li>

                                    </ol>
                                </div>
                                <!-- /.col -->
                            </div>
                            <!-- /.row -->
                        </div>
                        <!-- /.container-fluid -->
                    </div>

                    <section class="content">
                        <div class="container-fluid">
                            <div class="row">
                                <section class="col-12">
                                    <div class="card card-success">
                                        <div class="card-header">
                                            <h3 class="card-title" style="font-weight: bolder;">Payment Checker Verefaction  Dashboard(Verify Deatils Submitted by Payment Maker)
                                            </h3>
                                        </div>
                                        <div class="card-body">
                                            <div class="row">
                                                <div class="col-12">
                                                    <div class="card card-primary">
                                                        <div class="card-body">
                                                            <div class="form-group row">
                                                                <!-- First dropdown -->
                                                                <div class="col-md-6">
                                                                    <label for="collegeName" class="form-title"
                                                                        style="color: red; font-weight: bolder;">Choose
                                                                        your College:</label>
                                                                    <select id="collegeName"
                                                                        class="form-control select2" required>
                                                                        <option value="">Select a College</option>


                                                                    </select>
                                                                </div>
                                                                <!-- Second dropdown -->
                                                                <div class="col-md-6">
                                                                    <label for="monthYearDropdown"
                                                                        style="color: red; font-weight: bolder;">Choose
                                                                        your Attendance Month:</label>
                                                                    <select id="monthYearDropdown"
                                                                        class="form-control select2" required>
                                                                        <option value="">Select a month</option>

                                                                    </select>
                                                                </div>
                                                            </div>
                                                            <button type="button" id="btnsearch"
                                                                class="btn btn-info float-right mr-1">Submit</button>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </section>
                            </div>
                        </div>
                    </section>



                    <div class="row">
                        <div class="col-12">
                            <div class="card card-primary">
                                <div class="card-body">
                                    <div class="form-group row">
                                        <button type="button" id="btnexcel" class="btn btn-primary"
                                            onclick="doExport()">
                                            Export to Excel
                                        </button>
                                    </div>
                                    <div class="card-body">
                                        <div class="col-md-12">
                                            <button id="bulkVerifyBtn" class="btn btn-success">Unit/Bulk Verify</button>
                                             <button id="bulkRejectBtn" class="btn btn-danger">Unit/Bulk Reject</button>

                                            <table id="mainTable" class="table table-bordered">
                                                <thead id="thead"></thead>
                                                <tbody id="tbody"></tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>


                    <!-- Control Sidebar -->
                    <aside class="control-sidebar control-sidebar-dark">
                        <!-- Control sidebar content goes here -->
                    </aside>
                    <!-- /.control-sidebar -->

                    <!-- ./wrapper -->
                    <script>
                        $.widget.bridge("uibutton", $.ui.button);
                    </script>
                    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

                    <!-- jQuery UI -->
                    <script src="https://code.jquery.com/ui/1.12.1/jquery-ui.min.js"></script>

                    <!-- Bootstrap 4 -->
                    <script
                        src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.bundle.min.js"></script>
                    <!-- Select2 -->
                    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>

                    <!-- Moment.js -->
                    <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.1/moment.min.js"></script>

                    <!-- Date Range Picker -->
                    <script src="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.min.js"></script>

                    <!-- Tempusdominus Bootstrap 4 -->
                    <script
                        src="https://cdnjs.cloudflare.com/ajax/libs/tempusdominus-bootstrap-4/5.1.2/js/tempusdominus-bootstrap-4.min.js"></script>

                    <!-- OverlayScrollbars -->
                    <script
                        src="https://cdnjs.cloudflare.com/ajax/libs/overlayscrollbars/1.13.0/js/jquery.overlayScrollbars.min.js"></script>

                    <!-- AdminLTE App -->
                    <script src="https://cdnjs.cloudflare.com/ajax/libs/admin-lte/3.2.0/js/adminlte.min.js"></script>
                    <!-- jQuery Validation -->
                    <script
                        src="https://cdnjs.cloudflare.com/ajax/libs/jquery-validate/1.19.3/jquery.validate.min.js"></script>
                    <script
                        src="https://cdnjs.cloudflare.com/ajax/libs/jquery-validate/1.19.3/additional-methods.min.js"></script>
                    <!-- Toastr -->
                    <script src="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/js/toastr.min.js"></script>

                    <!-- SweetAlert2 -->
                    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

                    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>

                    <script src="https://unpkg.com/boxicons@2.1.4/dist/boxicons.js"></script>


                    <!-- FileSaver.js -->
                    <script src="https://cdnjs.cloudflare.com/ajax/libs/FileSaver.js/2.0.5/FileSaver.min.js"></script>
                    <!-- jsPDF -->
                    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
                    <!-- js-xlsx -->
                    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.core.min.js"></script>
                    <!-- tableExport.js -->
                    <script
                        src="https://cdn.jsdelivr.net/npm/tableexport.jquery.plugin@1.30.0/tableExport.min.js"></script>


                    <script>

                        const token = localStorage.getItem("access-token-new");
                        var userDetails = localStorage.getItem('user-data');
                        var userDetailsObj = JSON.parse(userDetails);
                        var departmentUsername = userDetailsObj.department_username
                        console.log(departmentUsername)
                        var universityName = userDetailsObj.university
                        $('#university1').append(universityName);
                        $('#college1').append(universityName);


                        function doExport() {
                            $('#mainTable').tableExport({
                                type: 'excel',
                                mso: {
                                    styles: ['background-color', 'color', 'font-family', 'font-size', 'font-weight', 'text-align']
                                }
                            });
                        }
                        function doExportList() {
                            $('#mainTable2').tableExport({
                                type: 'excel',
                                mso: {
                                    styles: ['background-color', 'color', 'font-family', 'font-size', 'font-weight', 'text-align']
                                }
                            });
                        }
                        $(document).ready(function () {
                            const apiBaseUrl = "<%= process.env.apiBaseUrl %>";
                            $.ajax({

                                url: apiBaseUrl + "v1/absentee/get-month",
                                type: 'get',
                                dataType: 'json',
                                headers: {
                                    token: localStorage.getItem("access-token-new"),
                                    "Content-Type": "application/json"
                                },
                                success: function (response) {
                                    if (response.status === "success") {

                                        var months = response.data;

                                        $.each(months, function (index, monthData) {
                                            var monthYearText = monthData.month_name + " " + monthData.year;

                                            $('#monthYearDropdown').append($('<option>', {
                                                value: monthYearText,
                                                text: monthYearText
                                            }));
                                        });
                                    } else {
                                        console.error('API request failed with status:', response.status);
                                    }
                                },
                                error: function (xhr, status, error) {
                                    console.error('Error fetching data from API:', error);
                                }
                            });

                        })

                    </script>

                    <script>
                        const apiBaseUrl = "<%= process.env.apiBaseUrl %>";
                        $(document).ready(function () {
                            $.ajax({
                                url: apiBaseUrl + "v1/maker/get-college",
                                type: 'get',
                                dataType: 'json',
                                headers: {
                                    token: localStorage.getItem("access-token-new"),
                                    "Content-Type": "application/json"
                                },
                                success: function (response) {
                                    if (response.status === "success") {

                                        var colleges = response.data.collegeName;




                                        $.each(colleges, function (index, college) {
                                            $('#collegeName').append($('<option>', {
                                                value: college.college_id,
                                                text: college.college_name
                                            }));
                                        });
                                    } else {
                                        console.error('API request failed with status:', response.status);
                                    }
                                },
                                error: function (xhr, status, error) {
                                    console.error('Error fetching data from API:', error);
                                }
                            });

                        })

                    </script>

                    <script>
                        $(function () {
                            $('.select2').select2();
                        });

                        $(document).ready(function () {
                            const apiBaseUrl = "<%= process.env.apiBaseUrl %>";

                            $('#btnsearch').click(function () {
                                var date = $('#monthYearDropdown').val();
                                var collegeName = $('#collegeName').val();

                                if (!date || !collegeName) {
                                    alert("Please select both the College and the Absentee Month.");
                                    return;
                                }

                                $.ajax({
                                    url: apiBaseUrl + "v1/checker/get-employee-details",
                                    method: "POST",
                                    contentType: "application/json",
                                    data: JSON.stringify({
                                        "date": date,
                                        "collegeName": collegeName
                                    }),
                                    headers: {
                                        "token": localStorage.getItem("access-token-new")
                                    },
                                    success: function (response) {
                                        if (response.status === "success") {
                                            var absenteeData = response.data.data;

                                            $('#thead').empty();
                                            $('#tbody').empty();

                                            // Table Headers
                                            $('#thead').append(`
                                 <tr>
                                <th><input type="checkbox" id="selectAll" />Select All</th>
                                <th>Pay ID No</th>
                                <th>Full Name</th>
                                <th>College Name</th>
                                <th>Attendance Month</th>
                                <th>Bank Account No</th>
                                <th>IFSC Code</th>
                                <th>Branch Name</th>
                                <th>Basic Salary</th>
                                <th>HRA</th>
                                <th>CTA</th>
                                <th>DA</th>
                                <th>Special Other Allowance</th>
                                <th>Medical Allowance</th>
                                <th>Income Tax</th>
                                 <th>Professional Tax</th>
                                <th>NPS Opted</th>
                                <th>PF</th>
                                <th>NPS</th>
                                <th>PF Loan</th>
                                <th>LIC</th>
                                <th>GIP</th>
                                <th>Other Deduction</th>
                                <th>Other Deduction 1</th>
                                <th>Other Deduction 2</th>
                                 <th>Other Deduction 3</th>
                                <th>Annual Opted Date</th>
                                <th>Last Payment Withdrawn Salary</th>
                                <th>Basic Salary Fixed</th>
                                <th>Last Payment Withdrawn Salary Deduction</th>
                                <th>Gross Salary(Without Deduction)</th>
                                <th>Net Pay Salary(After Deduction)</th>
                                <th>Action Verify</th>
                                <th>Action Reject</th>
                                <th>Reject Remarks</th>
                            </tr>
                        `);

                                            
                        absenteeData.forEach(function (employee) {
                                $('#tbody').append(`
                                <tr>
                                   <td> <input type="checkbox" class="selectRow" data-payid="${employee.payIdNo}" data-date="${employee.absentee_date}" /> </td>
                                    <td>${employee.payIdNo}</td>
                                    <td>${employee.full_name}</td>
                                    <td>${employee.college_name}</td>
                                    <td>${employee.absentee_date}</td>
                                    <td>${employee.bank_account_no ? employee.bank_account_no.slice(-4).padStart(employee.bank_account_no.length, 'x') : ''}</td>;
                                    <td>${employee.ifsc_code}</td>
                                    <td>${employee.branch_name}</td>
                                    <td>${employee.basic_salary}</td>
                                    <td>${employee.hr}</td>
                                    <td>${employee.cta}</td>
                                    <td>${employee.da}</td>
                                    <td>${employee.special_other_allowance}</td>
                                    <td>${employee.medical_allowance}</td>
                                    <td>${employee.income_tax}</td>
                                     <td>${employee.professional_tax}</td>
                                    <td>${employee.nps_opted}</td>
                                    <td>${employee.pf}</td>
                                    <td>${employee.nps}</td>
                                    <td>${employee.pfLoan}</td>
                                    <td>${employee.lic}</td>
                                    <td>${employee.gip}</td>
                                    <td>${employee.other_deduction}</td>
                                    <td>${employee.other_deduction1}</td>
                                    <td>${employee.other_deduction2}</td>
                                     <td>${employee.other_deduction3}</td>
                                    <td>${employee.annual_opted_date}</td>
                                    <td>${employee.last_payment_withdrawn_sallary}</td>
                                    <td>${employee.basic_salary_fixed}</td>
                                    <td>${employee.last_payment_withdrwan_sallary_deduction}</td>
                                    <td>${employee.gross_sallary}</td>
                                    <td>${employee.net_pay_sallary}</td>
                                       <td>
                <button type="button" class="btn btn-success verifyBtn" data-payid="${employee.payIdNo}" data-date="${employee.absentee_date}">Verify</button>
            </td>
            <td>
                <button type="button" class="btn btn-danger rejectBtn" data-payid="${employee.payIdNo}" data-date="${employee.absentee_date}">Reject</button>
            </td>
            <td>
                <input type="text" class="form-control remarksInput" id="remarks_${employee.payIdNo}" placeholder="Remarks" />
            </td>
                                </tr>
                            `);
                                            });
                                        } else {
                                            alert("Error fetching data");
                                        }
                                    },
                                    error: function (error) {
                                        alert("An error occurred while fetching data");
                                    }
                                });
                            });
                        });


 $(document).on('change', '#selectAll', function () {
    $('.selectRow').prop('checked', this.checked);
});
                        
    $('#bulkVerifyBtn').click(function () {
    var selectedEmployees = [];
    
    $('.selectRow:checked').each(function () {
        var payIdNo = $(this).data('payid');
        var date = $(this).data('date');
        var remarks = $('#remarks_' + payIdNo).val();

        selectedEmployees.push({
            "payIdNo": payIdNo,
            "date": date,
            "accepted": "CHECKER",
            "remarks": remarks
        });
    });

    if (selectedEmployees.length === 0) {
        alert('Please select at least one employee to verify.');
        return;
    }

    sendBulkRequest(selectedEmployees, "verify");
});

$('#bulkRejectBtn').click(function () {
    var selectedEmployees = [];
    
    $('.selectRow:checked').each(function () {
        var payIdNo = $(this).data('payid');
        var date = $(this).data('date');
        var remarks = $('#remarks_' + payIdNo).val();

        selectedEmployees.push({
            "payIdNo": payIdNo,
            "date": date,
            "rejected": "CHECKER",
            "remarks": remarks
        });
    });
    if (selectedEmployees.length === 0) {
        alert('Please select at least one employee to reject.');
        return;
    }
    sendBulkRequest(selectedEmployees, "reject");
});
function sendBulkRequest(data, actionType) {
    var apiUrl = apiBaseUrl + "v1/checker/checker-verified";
    $.ajax({
        url: apiUrl,
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify({
            "data": data
        }),
        success: function (response) {
            var message = actionType === "verify" ? "Verification successful and Data Sent To Payment Approver" : "Rejection successful and Data Reverted To Payment Maker";
            Swal.fire({
                icon: 'success',
                title: message,
                text: 'Action completed successfully!',
            });
            // Refresh the table or reload data
            refreshTable();
        },
        error: function (error) {
            var message = actionType === "verify" ? "Verification failed" : "Rejection failed";
            console.error(message, error);
            alert(message);
        }
    });
}
$(document).on('click', '.verifyBtn', function () {
    var payIdNo = $(this).data('payid');
    var date = $(this).data('date');
    var remarks = $('#remarks_' + payIdNo).val();
    var employeeData = [{
        "payIdNo": payIdNo,
        "date": date,
        "accepted": "CHECKER",
        "remarks": remarks
    }];
    sendBulkRequest(employeeData, "verify");
});
$(document).on('click', '.rejectBtn', function () {
    var payIdNo = $(this).data('payid');
    var date = $(this).data('date');
    var remarks = $('#remarks_' + payIdNo).val();

    var employeeData = [{
        "payIdNo": payIdNo,
        "date": date,
        "rejected": "CHECKER",
        "remarks": remarks
    }];
    sendBulkRequest(employeeData, "reject");
});
     function refreshTable() {
     var date = $('#monthYearDropdown').val();
     var collegeName = $('#collegeName').val();

                            $.ajax({
                                url: apiBaseUrl + "v1/checker/get-employee-details",
                                method: "POST",
                                contentType: "application/json",
                                data: JSON.stringify({
                                    "date": date,
                                    "collegeName": collegeName
                                }),
                                headers: {
                                    "token": localStorage.getItem("access-token-new")
                                },
                                success: function (response) {
                                    if (response.status === "success") {
                                        var absenteeData = response.data.data;
                                        $('#thead').empty();
                                        $('#tbody').empty();
                                        $('#thead').append(`
                                 <tr>
                                <th><input type="checkbox" id="selectAll" />Select All</th>
                                <th>Pay ID No</th>
                                <th>Full Name</th>
                                <th>College Name</th>
                                <th>Attendance Month</th>
                                <th>Bank Account No</th>
                                <th>IFSC Code</th>
                                <th>Branch Name</th>
                                <th>Basic Salary</th>
                                <th>HRA Percentage</th>
                                <th>HRA</th>
                                <th>CTA PERCENTAGE</th>
                                <th>CTA</th>
                                <th>DA PERCENTAGE</th>
                                <th>DA</th>
                                <th>Special Other Allowance</th>
                                <th>Medical Allowance</th>
                                <th>Income Tax</th>
                                 <th>Professional Tax</th>
                                <th>NPS Opted</th>
                                <th>PF</th>
                                <th>NPS</th>
                                <th>PF Loan</th>
                                <th>LIC</th>
                                <th>GIP</th>
                                <th>Other Deduction</th>
                                 <th>Other Deduction 1</th>
                                <th>Other Deduction 2</th>
                                 <th>Other Deduction 3</th>
                                <th>Annual Opted Date</th>
                                <th>Last Payment Withdrawn Salary</th>
                                <th>Basic Salary Fixed</th>
                                <th>Last Payment Withdrawn Salary Deduction</th>
                                <th>Gross Salary(Without Deduction)</th>
                                <th>Net Pay Salary(After Deduction)</th>
                                <th>Action Verify</th>
                                <th>Action Reject</th>
                                <th>Reject Remarks</th>
                        </tr>
                    `);


                                        absenteeData.forEach(function (employee) {
                                            $('#tbody').append(`
                                    <tr>
                                    <td><input type="checkbox" class="selectRow" data-payid="${employee.payIdNo}" data-date="${employee.absentee_date}" /></td>
                                    <td>${employee.payIdNo}</td>
                                    <td>${employee.full_name}</td>
                                    <td>${employee.college_name}</td>
                                    <td>${employee.absentee_date}</td>
                                    <td>${employee.bank_account_no ? employee.bank_account_no.slice(-4).padStart(employee.bank_account_no.length, 'x') : ''}</td>;
                                    <td>${employee.ifsc_code}</td>
                                    <td>${employee.branch_name}</td>
                                    <td>${employee.basic_salary}</td>
                                    <td>${employee.hra_percentage}</td>
                                    <td>${employee.hr}</td>
                                    <td>${employee.cta_percentage}</td>
                                    <td>${employee.cta}</td>
                                    <td>${employee.da_percentage}</td>
                                    <td>${employee.da}</td>
                                    <td>${employee.special_other_allowance}</td>
                                    <td>${employee.medical_allowance}</td>
                                    <td>${employee.income_tax}</td>
                                     <td>${employee.professional_tax}</td>
                                    <td>${employee.nps_opted}</td>
                                    <td>${employee.pf}</td>
                                    <td>${employee.nps}</td>
                                    <td>${employee.pfLoan}</td>
                                    <td>${employee.lic}</td>
                                    <td>${employee.gip}</td>
                                    <td>${employee.other_deduction}</td>
                                      <td>${employee.other_deduction1}</td>
                                    <td>${employee.other_deduction2}</td>
                                     <td>${employee.other_deduction3}</td>
                                    <td>${employee.annual_opted_date}</td>
                                    <td>${employee.last_payment_withdrawn_sallary}</td>
                                    <td>${employee.basic_salary_fixed}</td>
                                    <td>${employee.last_payment_withdrwan_sallary_deduction}</td>
                                    <td>${employee.gross_sallary}</td>
                                    <td>${employee.net_pay_sallary}</td>
                                       <td>
                <button type="button" class="btn btn-success verifyBtn" data-payid="${employee.payIdNo}" data-date="${employee.absentee_date}">Verify</button>
            </td>
            <td>
                <button type="button" class="btn btn-danger rejectBtn" data-payid="${employee.payIdNo}" data-date="${employee.absentee_date}">Reject</button>
            </td>
            <td>
                <input type="text" class="form-control remarksInput" id="remarks_${employee.payIdNo}" placeholder="Remarks" />
            </td>
                            </tr>
                        `);
                                        });
                                    } else {
                                        alert("Error fetching data");
                                    }
                                },
                                error: function (error) {
                                    alert("An error occurred while fetching data");
                                }
                            });
                        }
                    </script>
                    <script>
                        function isTokenExpired(token) {
                            if (!token) return true;
                            try {
                                // Decode JWT payload
                                const payloadBase64 = token.split('.')[1];
                                const payload = JSON.parse(atob(payloadBase64));
                                // Check if current time is past the expiration time
                                const isExpired = payload.exp * 1000 < Date.now();
                                return isExpired;
                            } catch (error) {
                                console.error("Failed to decode token:", error);
                                return true; 
                            }
                        }
                        // Check token expiration on load
                        $(function () {
                            const token = localStorage.getItem("access-token-new");
                            const userData = JSON.parse(userDetails);
                            if (token && userData) {
                    
                                if (usersData.role !== "Checker-Login" || isTokenExpired(token)) {
                                    alert("Session expired or unauthorized access. Redirecting to login.");
                                    window.location = "/";
                                } else {
                                    setInterval(() => {
                                        if (isTokenExpired(token)) {
                                            alert("Session expired. Please login again.");
                                            window.location = "/";
                                        }
                                    }, 60000);
                                }
                            } else {
                                window.location = "/";
                            }
                        });
                                        </script> 