<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>
        <%= title %>
    </title>
    <!-- Google Font: Source Sans Pro -->
    <link rel="stylesheet"
        href="https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,400i,700&display=fallback" />

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css" />

    <!-- Ionicons -->
    <link rel="stylesheet" href="https://code.ionicframework.com/ionicons/2.0.1/css/ionicons.min.css" />


    <!-- Tempusdominus Bootstrap 4 -->
    <link rel="stylesheet"
        href="https://cdnjs.cloudflare.com/ajax/libs/tempusdominus-bootstrap-4/5.39.0/css/tempusdominus-bootstrap-4.min.css" />

    <!-- iCheck -->
    <link rel="stylesheet"
        href="https://cdnjs.cloudflare.com/ajax/libs/icheck-bootstrap/3.0.1/icheck-bootstrap.min.css" />

    <!-- Select2 -->
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/select2@4.0.13/dist/css/select2.min.css" />

    <!-- Theme style -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/admin-lte/3.1.0/css/adminlte.min.css" />

    <!-- overlayScrollbars -->
    <link rel="stylesheet"
        href="https://cdnjs.cloudflare.com/ajax/libs/overlayscrollbars/1.13.0/css/OverlayScrollbars.min.css" />

    <!-- Daterange picker -->
    <link rel="stylesheet" href="https://cdn.datatables.net/1.10.24/css/dataTables.bootstrap4.min.css">


    <!-- DataTables -->
    <link rel="stylesheet" href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap4.min.css">

    <link rel="stylesheet" href="https://cdn.datatables.net/responsive/2.2.9/css/responsive.bootstrap4.min.css">

    <link rel="stylesheet" href="https://cdn.datatables.net/buttons/2.0.1/css/buttons.bootstrap4.min.css">
    <!-- Baoxicon -->
    <link href='https://unpkg.com/boxicons@2.1.4/css/boxicons.min.css' rel='stylesheet'>
    <style type="text/css">
        .seat {
            width: 50px;
        }

        input::-webkit-outer-spin-button,
        input::-webkit-inner-spin-button {
            -webkit-appearance: none;
            margin: 0;
        }

        .card-title {
            font-weight: bold !important;
        }

        em {
            color: red;
        }

        .custom-checkbox {
            display: inline-block;
            width: 25px;
            height: 25px;
            background-color: white;
            border: 2px solid #ddd;
            border-radius: 7px;
            margin-right: 15px;
            vertical-align: middle;
            position: relative;
        }

        .form-check-input:checked+.form-check-label .custom-checkbox {
            background-color: blue;

        }

        .form-check-input:checked+.form-check-label .custom-checkbox::after {
            content: '';
            position: absolute;
            top: 5px;
            left: 9px;
            width: 6px;
            height: 12px;
            border: solid white;
            border-width: 0 2px 2px 0;
            transform: rotate(45deg);
        }

        .form-check-label {
            cursor: pointer;
        }

        .is-invalid {
            border-color: red;
        }

        .modal-fullscreen {
            max-width: 90%;
            /* Full width */
            width: 90%;
            height: 220vh;
            /* Full viewport height */
            margin: 0;
            margin-left: 60px;
        }

        .modal-content {
            height: 100%;
            /* Full height for modal content */
        }
    </style>
</head>

<body class="hold-transition sidebar-mini layout-fixed sidebar-collapse">
    <div class="wrapper">
        <!-- Preloader -->
        <div class="preloader flex-column justify-content-center align-items-center">
            <img class="animation__shake" src="/images/loader.gif" alt="CodeBucket" height="100" width="100" />
        </div>

        <!-- Navbar -->
        <%- include('../partials/header'); %>
            <!-- /.navbar -->

            <!-- Main Sidebar Container -->
            <%- include('../partials/sidebar'); %>

                <!-- Content Wrapper. Contains page content -->
                <div class="content-wrapper">
                    <!-- Content Header (Page header) -->
                    <div class="content-header">
                        <div class="container-fluid">
                            <div class="row mb-2">
                                <div class="col-sm-6">
                                    <h1 class="m-0" style="color:black; font-size: xx-large; font-weight: bold;">Welcome to Payment Maker Action Dashboard:</h1>
                                </div>
                                <!-- /.col -->
                                <div class="col-sm-6">
                                    <ol class="breadcrumb float-sm-right">
                                        <li class="breadcrumb-item"><a href="/logout">Signout</a></li>

                                    </ol>
                                </div>
                                <!-- /.col -->
                            </div>
                            <!-- /.row -->
                        </div>
                        <!-- /.container-fluid -->
                    </div>

                    <section class="content">
                        <div class="container-fluid">
                            <div class="row">
                                <section class="col-12">
                                    <div class="card card-success">
                                        <div class="card-header">
                                            <h3 class="card-title" style="font-weight: bolder;">Payment Maker View</h3>
                                        </div>
                                        <div class="card-body">
                                            <div class="row">
                                                <div class="col-12">
                                                    <div class="card card-primary">
                                                        <div class="card-body">
                                                            <div class="form-group row">
                                                                <!-- First dropdown -->
                                                                <div class="col-md-6">
                                                                    <label for="collegeName" class="form-title"
                                                                        style="color: red; font-weight: bolder;">Choose
                                                                        your College:</label>
                                                                    <select id="collegeName"
                                                                        class="form-control select2" required>
                                                                        <option value="">Select a College</option>
                                                                        

                                                                    </select>
                                                                </div>
                                                                <!-- Second dropdown -->
                                                                <div class="col-md-6">
                                                                    <label for="monthYearDropdown"
                                                                        style="color: red; font-weight: bolder;">Choose
                                                                        your Absentee Month:</label>
                                                                    <select id="monthYearDropdown"
                                                                        class="form-control select2" required>
                                                                        <option value="">Select a month</option>

                                                                    </select>
                                                                </div>
                                                            </div>
                                                            <button type="button" id="btnsearch"
                                                                class="btn btn-info float-right mr-1">Submit</button>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </section>
                            </div>
                        </div>
                    </section>



                    <div class="row">
                        <section class="col-12">
                            <div class="card-header">
                                <button type="button" id="btnexcel" class="btn btn-primary" onclick="doExport()">
                                    Export to Excel
                                </button>
                            </div>
                            <div class="card-body">
                                <div class="col-md-12">
                                    <table id="mainTable" class="table table-bordered">
                                        <thead id="thead"></thead>
                                        <tbody id="tbody"></tbody>
                                    </table>
                                </div>
                            </div>

                        </section>
                        <div class="modal fade" id="modalresolved" tabindex="-1" role="dialog" aria-labelledby="modal1">
                            <div class="modal-dialog modal-fullscreen" role="document">
                                <div class="modal-content">
                                    <div class="modal-header">
                                        <!-- Close button -->
                                        <button type="button" class="close close-red" data-dismiss="modal"
                                            aria-label="Close">
                                            <span aria-hidden="true">&times;</span>
                                        </button>
                                    </div>
                                    <div class="modal-body">
                                        <div class="row">
                                            <!-- Salary Details Section -->
                                            <div class="col-md-10">
                                                <h5><b> Details to Verify By Payment Maker:</b></h5>
                                                <form id="frmupdate" name="frmupdate" action="" method="post"
                                                    enctype="multipart/form-data">
                                                    <table class="table table-bordered">
                                                        <!-- Personal Details Section -->
                                                        <thead>
                                                            <tr>
                                                                <th colspan="3"
                                                                    style="color: red ; font-weight: bold; font-size: larger;">
                                                                    Personal Details to Verify</th>
                                                            </tr>
                                                        </thead>
                                                        <tbody>
                                                            <tr>
                                                                <td><b>Pay ID No: </b><input type="text"
                                                                        class="form-control" id="salaryNo" readonly>
                                                                </td>
                                                                <td><b>Full Name: </b><input type="text"
                                                                        class="form-control" id="fullName" readonly>
                                                                </td>
                                                                <td><b>Date of Birth: </b><input type="text"
                                                                        class="form-control" id="dateOfBirth" readonly>
                                                                </td>
                                                            </tr>
                                                            <tr>
                                                                <td><b>Gender: </b><input type="text"
                                                                        class="form-control" id="gender" readonly></td>
                                                                <td><b>Aadhar Card No: </b><input type="text"
                                                                        class="form-control" id="aadharCardNo" readonly>
                                                                </td>
                                                                <td><b>Employee Type: </b><input type="text"
                                                                        class="form-control" id="employeeType" readonly>
                                                                </td>
                                                            </tr>
                                                            <tr>
                                                                <td><b>Date of Retirement: </b><input type="text"
                                                                        class="form-control" id="dateOfRetirement"
                                                                        readonly></td>
                                                                <td><b>Currently Posted Department/College: </b><input
                                                                        type="text" class="form-control"
                                                                        id="currentlyPostedAtUniversityOfficeDepartmentCollege"
                                                                        readonly></td>
                                                                <td><b>Branch Name: </b><input type="text"
                                                                        class="form-control" id="branchName" readonly>
                                                                </td>
                                                            </tr>
                                                            <tr>
                                                                <td><b>Bank Account No: </b><input type="text"
                                                                        class="form-control" id="bankAccountNo"
                                                                        readonly></td>
                                                                <td><b>Bank Name: </b><input type="text"
                                                                        class="form-control" id="bankName" readonly>
                                                                </td>
                                                                <td><b>IFSC Code: </b><input type="text"
                                                                        class="form-control" id="ifscCode" readonly>
                                                                </td>
                                                            </tr>
                                                        </tbody>

                                                        <!-- Salary Details Section -->
                                                        <thead>
                                                            <tr>
                                                                <th colspan="3"
                                                                    style="color: rgb(21, 0, 255) ; font-weight: bold; font-size: larger;">
                                                                    Salary Details to Verify</th>
                                                            </tr>
                                                        </thead>
                                                        <tbody>
                                                            <tr>
                                                                <td><b>Pay Level: </b><input type="text"
                                                                        class="form-control" id="payLevel"></td>
                                                                <td><b>Basic Salary: </b><input type="text"
                                                                        class="form-control" id="basicSalary"></td>
                                                                <td><b style="color: red;">Basic Salary Fixed:
                                                                    </b><input type="text" class="form-control"
                                                                        id="basicSalaryFixed"></td>
                                                            </tr>
                                                            <tr>


                                                                <td><label for="dapercentage"
                                                                        style="color:black; font-weight: bolder;">Choose
                                                                        your DA Percentage :</label><select
                                                                        id="daPercentage" class="form-control select2"
                                                                        >
                                                                        <option value="">Select DA Percentage</option>
                                                                        <!-- Options from 1 to 50 -->
                                                                        <option value="0">0</option>
                                                                        <option value="1">1</option>
                                                                        <option value="2">2</option>
                                                                        <option value="3">3</option>
                                                                        <option value="4">4</option>
                                                                        <option value="5">5</option>
                                                                        <option value="6">6</option>
                                                                        <option value="7">7</option>
                                                                        <option value="8">8</option>
                                                                        <option value="9">9</option>
                                                                        <option value="10">10</option>
                                                                        <option value="11">11</option>
                                                                        <option value="12">12</option>
                                                                        <option value="13">13</option>
                                                                        <option value="14">14</option>
                                                                        <option value="15">15</option>
                                                                        <option value="16">16</option>
                                                                        <option value="17">17</option>
                                                                        <option value="18">18</option>
                                                                        <option value="19">19</option>
                                                                        <option value="20">20</option>
                                                                        <option value="21">21</option>
                                                                        <option value="22">22</option>
                                                                        <option value="23">23</option>
                                                                        <option value="24">24</option>
                                                                        <option value="25">25</option>
                                                                        <option value="26">26</option>
                                                                        <option value="27">27</option>
                                                                        <option value="28">28</option>
                                                                        <option value="29">29</option>
                                                                        <option value="30">30</option>
                                                                        <option value="31">31</option>
                                                                        <option value="32">32</option>
                                                                        <option value="33">33</option>
                                                                        <option value="34">34</option>
                                                                        <option value="35">35</option>
                                                                        <option value="36">36</option>
                                                                        <option value="37">37</option>
                                                                        <option value="38">38</option>
                                                                        <option value="39">39</option>
                                                                        <option value="40">40</option>
                                                                        <option value="41">41</option>
                                                                        <option value="42">42</option>
                                                                        <option value="43">43</option>
                                                                        <option value="44">44</option>
                                                                        <option value="45">45</option>
                                                                        <option value="46">46</option>
                                                                        <option value="47">47</option>
                                                                        <option value="48">48</option>
                                                                        <option value="49">49</option>
                                                                        <option value="50">50</option>
                                                                    </select></td>
                                                                <td><b>DA: </b><input type="text" class="form-control"
                                                                        id="da" readonly></td>
                                                                <td><b>HRA Percentage: </b><select id="HRAPercentage"
                                                                        class="form-control select2" >
                                                                        <option value="">Select HRA Percentage</option>
                                                                        <!-- Options from 1 to 50 -->
                                                                        <option value="0">0</option>
                                                                        <option value="1">1</option>
                                                                        <option value="2">2</option>
                                                                        <option value="3">3</option>
                                                                        <option value="4">4</option>
                                                                        <option value="5">5</option>
                                                                        <option value="6">6</option>
                                                                        <option value="7">7</option>
                                                                        <option value="8">8</option>
                                                                        <option value="9">9</option>
                                                                        <option value="10">10</option>
                                                                        <option value="11">11</option>
                                                                        <option value="12">12</option>
                                                                        <option value="13">13</option>
                                                                        <option value="14">14</option>
                                                                        <option value="15">15</option>
                                                                        <option value="16">16</option>
                                                                        <option value="17">17</option>
                                                                        <option value="18">18</option>
                                                                        <option value="19">19</option>
                                                                        <option value="20">20</option>
                                                                        <option value="21">21</option>
                                                                        <option value="22">22</option>
                                                                        <option value="23">23</option>
                                                                        <option value="24">24</option>
                                                                        <option value="25">25</option>
                                                                        <option value="26">26</option>
                                                                        <option value="27">27</option>
                                                                        <option value="28">28</option>
                                                                        <option value="29">29</option>
                                                                        <option value="30">30</option>
                                                                        <option value="31">31</option>
                                                                        <option value="32">32</option>
                                                                        <option value="33">33</option>
                                                                        <option value="34">34</option>
                                                                        <option value="35">35</option>
                                                                        <option value="36">36</option>
                                                                        <option value="37">37</option>
                                                                        <option value="38">38</option>
                                                                        <option value="39">39</option>
                                                                        <option value="40">40</option>
                                                                        <option value="41">41</option>
                                                                        <option value="42">42</option>
                                                                        <option value="43">43</option>
                                                                        <option value="44">44</option>
                                                                        <option value="45">45</option>
                                                                        <option value="46">46</option>
                                                                        <option value="47">47</option>
                                                                        <option value="48">48</option>
                                                                        <option value="49">49</option>
                                                                        <option value="50">50</option>
                                                                    </select></td>


                                                            <tr>
                                                                <td><b>HRA: </b><input type="text" class="form-control"
                                                                        id="hra" readonly></td>
                                                              
                                                                       
                                                                <td><b>CTA: </b><input type="text" class="form-control"
                                                                        id="cta" ></td>

                                                            </tr>


                                                            </tr>




                                                            <tr>

                                                                <td><b>Medical Allowance: </b><input type="text"
                                                                        class="form-control" id="medicalAllowance"></td>
                                                                <td><b>Special Other Allowance: </b><input type="text"
                                                                        class="form-control" id="specialOtherAllowance">
                                                                </td>
                                                                <td><b>Last Payment Withdrawn Salary: </b><input
                                                                        type="text" class="form-control"
                                                                        id="lastPaymentWithdrawnSalary"></td>
                                                            </tr>
                                                            <tr>

                                                                <td><b style="color: red;">Last Payment Withdrawn Salary
                                                                        Deduction: </b><input type="text"
                                                                        class="form-control"
                                                                        id="lastPaymentWithdrawnSalarydeduction"></td>

                                                                <td><b style="color: red;">Gross Salary (Before
                                                                        Deduction) </b><input type="text"
                                                                        class="form-control" id="grosssalary"></td>
                                                                <td><b style="color: red;">Net Pay Salary (After All
                                                                        Deduction)</b><input type="text"
                                                                        class="form-control" id="netpaysalary"></td>
                                                            </tr>

                                                        </tbody>

                                                        <!-- Deduction Details Section -->
                                                        <thead>
                                                            <tr>
                                                                <th colspan="3"
                                                                    style="color: crimson ; font-weight: bold; font-size: larger;">
                                                                    Salary Deduction Details to Verify</th>
                                                            </tr>
                                                        </thead>
                                                        <tbody>
                                                            <tr>
                                                                <td><b>Income Tax: </b><input type="text"
                                                                        class="form-control" id="incomeTax"></td>
                                                                <td><b>P.F: </b><input type="text" class="form-control"
                                                                        id="pf"></td>
                                                                <td><b>P.F. Loan: </b><input type="text"
                                                                        class="form-control" id="pfLoan"></td>
                                                            </tr>
                                                            <tr>
                                                                <td><b>NPS Opted: </b><input type="text"
                                                                        class="form-control" id="npsOpted"></td>
                                                                <td><b>NPS: </b><input type="text" class="form-control"
                                                                        id="nps"></td>
                                                                <td><b>Professional Tax: </b><input type="text"
                                                                        class="form-control" id="professionalTax"></td>
                                                            </tr>

                                                            <tr>
                                                                <td><b>L.I.C: </b><input type="text"
                                                                        class="form-control" id="lic"></td>
                                                                <td><b>G.I.P: </b><input type="text"
                                                                        class="form-control" id="gip"></td>
                                                                <td><b>Any Other Deduction: </b><input type="text"
                                                                        class="form-control" id="otherdeduction"></td>

                                                                <input type="hidden" id="absentee_date" name="absentee_date">
                                                                <input type="hidden" id="annualOptedDate" name="annualOptedDate">
                                                                <input type="hidden" id="collegeId" name="collegeId">
                                                                <input type="hidden" id="universityid" name="universityid">
                                                            </tr>


                                                        </tbody>

                                                        <tr>
                                                            <td colspan="5">
                                                                <p style="font-style: italic; color:rgb(128, 0, 0); font-size: larger;">
                                                                    Note: Please Verify All Payment Details   and then Submit Request To Payment Maker
                                                               
                                                                </p>
                                                            </td>
                                                        </tr>
                                                        <tr>
                                                            <td colspan="5" align="center">
                                                                <button type="submit" class="btn btn-primary"
                                                                    id="btnsubmit">Submit Request</button>
                                                            </td>
                                                        </tr>
                                                    </table>

                                                </form>
                                            </div>


                                            <!-- Absentee Details Section -->
                                            <div class="col-md-2">
                                                <h5 style="color: red;"><b>Attendance Details </b></h5>
                                                <table class="table table-bordered">
                                                    <tr>
                                                        <td><b>Employee Name: </b><span id="employeeName"></span></td>
                                                        <td><b>Designation: </b><span id="designation"></span></td>
                                                    </tr>
                                                    <tr>
                                                        <td><b>Total CL: </b><span id="totalCL"></span></td>
                                                        <td><b>Total EL: </b><span id="totalEL"></span></td>
                                                    </tr>
                                                    <tr>
                                                        <td><b>Total Medical: </b><span id="totalMedical"></span></td>
                                                        <td><b>Total SL: </b><span id="totalSL"></span></td>
                                                    </tr>
                                                    <tr>
                                                        <td><b>Availed CL: </b><span id="availedCL"></span></td>
                                                        <td><b>Availed EL: </b><span id="availedEL"></span></td>
                                                    </tr>
                                                    <tr>
                                                        <td><b>Balance CL: </b><span id="balanceCL"></span></td>
                                                        <td><b>Balance EL: </b><span id="balanceEL"></span></td>
                                                    </tr>
                                                    <tr>
                                                        <td><b>Total Presence: </b><span id="totalPresence"></span></td>

                                                        <td><b>total working in month: </b><span
                                                                id="total_working_in_month"></span></td>
                                                    </tr>
                                                    <tr>
                                                        <td><b>Absentee date": </b><span id="absentee_date1"></span>
                                                        </td>
                                                        <td><b>Biometric Report Attached: </b><span
                                                                id="biometricReport"></span></td>
                                                    </tr>
                                                </table>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Main row -->

                        <!-- /.container-fluid -->

                        <!-- /.content -->
                    </div>


                    <!-- Control Sidebar -->
                    <aside class="control-sidebar control-sidebar-dark">
                        <!-- Control sidebar content goes here -->
                    </aside>
                    <!-- /.control-sidebar -->
                </div>
                <!-- ./wrapper -->
                <script>
                    $.widget.bridge("uibutton", $.ui.button);
                </script>
                <!-- jQuery -->
                <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
                <!-- jQuery UI -->
                <script src="https://code.jquery.com/ui/1.12.1/jquery-ui.min.js"></script>
                <!-- Bootstrap 4 -->
                <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.bundle.min.js"></script>
                <!-- Select2 -->
                <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>

                <!-- Moment.js -->
                <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.1/moment.min.js"></script>

                <!-- Tempusdominus Bootstrap 4 -->
                <script
                    src="https://cdnjs.cloudflare.com/ajax/libs/tempusdominus-bootstrap-4/5.1.2/js/tempusdominus-bootstrap-4.min.js"></script>

                <!-- OverlayScrollbars -->
                <script
                    src="https://cdnjs.cloudflare.com/ajax/libs/overlayscrollbars/1.13.0/js/jquery.overlayScrollbars.min.js"></script>

                <!-- AdminLTE App -->
                <script src="https://cdnjs.cloudflare.com/ajax/libs/admin-lte/3.2.0/js/adminlte.min.js"></script>

                <!-- jQuery Validation -->
                <script
                    src="https://cdnjs.cloudflare.com/ajax/libs/jquery-validate/1.19.3/jquery.validate.min.js"></script>
                <script
                    src="https://cdnjs.cloudflare.com/ajax/libs/jquery-validate/1.19.3/additional-methods.min.js"></script>

                <!-- Toastr -->
                <script src="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/js/toastr.min.js"></script>

                <!-- SweetAlert2 -->
                <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

                <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
                <!-- boxicon -->
                <script src="https://unpkg.com/boxicons@2.1.4/dist/boxicons.js"></script>

                <!-- FileSaver.js -->
                <script src="https://cdnjs.cloudflare.com/ajax/libs/FileSaver.js/2.0.5/FileSaver.min.js"></script>
                <!-- jsPDF -->
                <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
                <!-- js-xlsx -->
                <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.core.min.js"></script>
                <!-- tableExport.js -->
                <script src="https://cdn.jsdelivr.net/npm/tableexport.jquery.plugin@1.30.0/tableExport.min.js"></script>
                <script>
                    const token = localStorage.getItem("access-token-new");
                    var userDetails = localStorage.getItem('user-data');
                    var userDetailsObj = JSON.parse(userDetails);
                    var departmentUsername = userDetailsObj.department_username
                    console.log(departmentUsername)
                    var universityName = userDetailsObj.university
                    $('#university1').append(universityName);
                    $('#college1').append(universityName);

                    function doExport() {
                        $('#mainTable').tableExport({
                            type: 'excel',
                            mso: {
                                styles: ['background-color', 'color', 'font-family', 'font-size', 'font-weight', 'text-align']
                            }
                        });
                    }
                    function doExportList() {
                        $('#mainTable2').tableExport({
                            type: 'excel',
                            mso: {
                                styles: ['background-color', 'color', 'font-family', 'font-size', 'font-weight', 'text-align']
                            }
                        });
                    }


                    $(document).ready(function () {
                        const apiBaseUrl = "<%= process.env.apiBaseUrl %>";
                        $.ajax({

                            url: apiBaseUrl + "v1/absentee/get-month",
                            type: 'get',
                            dataType: 'json',
                            headers: {
                                token: localStorage.getItem("access-token-new"),
                                "Content-Type": "application/json"
                            },
                            success: function (response) {
                                if (response.status === "success") {
                                    var months = response.data;
                                    $.each(months, function (index, monthData) {
                                        var monthYearText = monthData.month_name + " " + monthData.year;
                                        $('#monthYearDropdown').append($('<option>', {
                                            value: monthYearText,
                                            text: monthYearText
                                        }));
                                    });
                                } else {
                                    console.error('API request failed with status:', response.status);
                                }
                            },
                            error: function (xhr, status, error) {
                                console.error('Error fetching data from API:', error);
                            }
                        });
                    })
                </script>
                <script>
                    const apiBaseUrl = "<%= process.env.apiBaseUrl %>";
                    $(document).ready(function () {
                        $.ajax({
                            url: apiBaseUrl + "v1/maker/get-college",
                            type: 'get',
                            dataType: 'json',
                            headers: {
                                token: localStorage.getItem("access-token-new"),
                                "Content-Type": "application/json"
                            },
                            success: function (response) {
                                if (response.status === "success") {
                                    var colleges = response.data.collegeName;
                                    $.each(colleges, function (index, college) {
                                        $('#collegeName').append($('<option>', {
                                            value: college.college_id,
                                            text: college.college_name.trim()
                                        }));
                                    });
                                } else {
                                    console.error('API request failed with status:', response.status);
                                }
                            },
                            error: function (xhr, status, error) {
                                console.error('Error fetching data from API:', error);
                            }
                        });
                    })
                </script>
                <script>
                    $(function () {
                        $('.select2').select2();
                    });
                    $(document).ready(function () {
                        const apiBaseUrl = "<%= process.env.apiBaseUrl %>";
                        $('#btnsearch').click(function () {
                            const apiBaseUrl = "<%= process.env.apiBaseUrl %>";
                            var date = $('#monthYearDropdown').val();
                            var collegeName = $('#collegeName').val();
                            if (!date || !collegeName) {
                                alert("Please select both the College and the Absentee Month.");
                                return;
                            }
                            $.ajax({
                                url: apiBaseUrl + "v1/maker/get-maker-data",
                                method: "POST",
                                contentType: "application/json",
                                data: JSON.stringify({
                                    "date": date,
                                    "collegeName": collegeName
                                }),
                                headers: {
                                    "token": localStorage.getItem("access-token-new")
                                },
                                success: function (response) {
                                    if (response.status === "success") {
                                        var absenteeData = response.data;
                                        $('#thead').empty();
                                        $('#tbody').empty();
                                        $('#thead').append(`
                     <tr>
                             <th>Sr. No</th>
                            <th>Pay ID No</th>
                            <th>Full Name</th>
                            <th>College Name</th>
                            <th>Action Taken</th>
                        </tr>
                    `);
                    absenteeData.forEach(function (employee, index) {
                                        $('#tbody').append(`
                            <tr>
                                 <td>${index + 1}</td>
                                <td>${employee.payIdNo}</td>
                                <td>${employee.full_name}</td>
                                <td>${employee.currentlyPostedAtUniversityOfficeDepartmentCollege}</td>
                                <td>
                                    <button 
                                        type="button" 
                                        class="btn btn-primary" 
                                        onclick="viewData('${employee.payIdNo}')">
                                        View
                                    </button>
                                </td>
                            </tr>
                        `);
                                        });
                                    } else {
                                        alert("Error fetching data");
                                    }
                                },
                                error: function (error) {
                                    alert("An error occurred while fetching data");
                                }
                            });
                        });
                    });
                    function refreshTable() {
                        var date = $('#monthYearDropdown').val();
                        var collegeName = $('#collegeName').val();

                        $.ajax({
                            url: apiBaseUrl + "v1/maker/get-maker-data",
                            method: "POST",
                            contentType: "application/json",
                            data: JSON.stringify({
                                "date": date,
                                "collegeName": collegeName
                            }),
                            headers: {
                                "token": localStorage.getItem("access-token-new")
                            },
                            success: function (response) {
                                if (response.status === "success") {
                                    var absenteeData = response.data;
                                    $('#thead').empty();
                                    $('#tbody').empty();
                                    $('#thead').append(`
                        <tr>
                             <th>Sr. No</th>
                            <th>Pay ID No</th>
                            <th>Full Name</th>
                            <th>College Name</th>
                            <th>Action Taken</th>
                        </tr>
                    `);
                    absenteeData.forEach(function (employee, index) {
                                        $('#tbody').append(`
                            <tr>
                                 <td>${index + 1}</td>
                                <td>${employee.payIdNo}</td>
                                <td>${employee.full_name}</td>
                                <td>${employee.currentlyPostedAtUniversityOfficeDepartmentCollege}</td>
                                <td>
                                    <button 
                                        type="button" 
                                        class="btn btn-primary" 
                                        onclick="viewData('${employee.payIdNo}' )">
                                        View
                                    </button>
                                </td>
                            </tr>
                        `);
                                    });
                                } else {
                                    alert("Error fetching data");
                                }
                            },
                            error: function (error) {
                                alert("An error occurred while fetching data");
                            }
                        });
                    }
                    function viewData(payIdNo) {
                        var date = $('#monthYearDropdown').val();
                        $.ajax({
                            url: apiBaseUrl + "v1/maker/indi-maker-data",
                            method: "POST",
                            contentType: "application/json",
                            data: JSON.stringify({
                                payIdNo: payIdNo,
                                date: date
                            }),
                            headers: {
                                "token": localStorage.getItem("access-token-new")
                            },
                            success: function (response) {
                                const salaryDetails = response.data.Universitydata[0];
                                const absenteeDetails = response.data.absenteeData[0];
                                 // Reset HRA,DA,CTA percentage 
                                $('#ctapercentage').val('').trigger('change');  
                                $('#daPercentage').val('').trigger('change');
                                $('#HRAPercentage').val('').trigger('change');
                                $('#salaryNo').val(salaryDetails.payIdNo);
                                $('#fullName').val(salaryDetails.prefix + ' ' + salaryDetails.firstName + ' ' + salaryDetails.lastName);
                                $('#gender').val(salaryDetails.gender);
                                $('#dateOfBirth').val(salaryDetails.dateOfBirth);
                                $('#aadharCardNo').val(salaryDetails.aadharCardNo);
                                $('#employeeType').val(salaryDetails.employeeType);
                                $('#dateOfRetirement').val(salaryDetails.dateOfRetirement);
                                $('#basicSalaryFixed').val(salaryDetails.basicSalaryFixed);
                                $('#currentlyPostedAtUniversityOfficeDepartmentCollege').val(salaryDetails.currentlyPostedAtUniversityOfficeDepartmentCollege);
                                $('#bankAccountNo').val(salaryDetails.bankAccountNo);
                                $('#bankName').val(salaryDetails.bankName);
                                $('#branchName').val(salaryDetails.branchName);
                                $('#ifscCode').val(salaryDetails.ifscCode);
                                $('#payLevel').val(salaryDetails.payLevel);
                                $('#basicSalary').val(salaryDetails.basicSalary);
                                $('#hra').val(salaryDetails.hra);
                                $('#cta').val(salaryDetails.cta);
                                $('#da').val(salaryDetails.da);
                                $('#medicalAllowance').val(salaryDetails.medicalAllowance);
                                $('#npsOpted').val(salaryDetails.npsOpted);
                                $('#nps').val(salaryDetails.nps);
                                $('#lastPaymentWithdrawnSalary').val(salaryDetails.lastPaymentWithdrawnSalary);
                                $('#lastPaymentWithdrawnSalarydeduction').val(salaryDetails.lastPaymentWithdrawnSalarydeduction);
                                $('#annualOptedDate').val(salaryDetails.annualOptedDate);
                                $('#lic').val(salaryDetails.lic);
                                $('#pf').val(salaryDetails.pf);
                                $('#pfLoan').val(salaryDetails.pfLoan);
                                $('#gip').val(salaryDetails.gip);
                                $('#incomeTax').val(salaryDetails.incomeTax);
                                $('#specialOtherAllowance').val(salaryDetails.specialOtherAllowance ? salaryDetails.specialOtherAllowance : "0");
                                $('#collegeId').val(salaryDetails.college_id);
                                $('#universityid').val(salaryDetails.university_id);

                                $('#modalresolved').modal('show');
                                // $('#absentee_date').val(absenteeDetails.absentee_date);

                                // Absentee Details
                                $('#employeeName').text(absenteeDetails.name_of_employee);
                                $('#designation').text(absenteeDetails.designation);
                                $('#totalCL').text(absenteeDetails.total_cl);
                                $('#totalEL').text(absenteeDetails.total_el);
                                $('#totalMedical').text(absenteeDetails.total_medical);
                                $('#totalSL').text(absenteeDetails.total_sl);
                                $('#availedCL').text(absenteeDetails.availed_cl);
                                $('#availedEL').text(absenteeDetails.availed_el);
                                $('#balanceCL').text(absenteeDetails.balance_cl);
                                $('#balanceEL').text(absenteeDetails.balance_el);
                                $('#biometricReport').text(absenteeDetails.biometric_report_attach);
                                $('#totalPresence').text(absenteeDetails.total_presence_in_the_month);
                                $('#total_working_in_month').text(absenteeDetails.total_working_in_month);
                                $('#absentee_date1').text(absenteeDetails.absentee_date);
                             
                            },
                            error: function (error) {
                                alert("An error occurred while fetching absentee details.");
                            }
                        });
                    }


// Custom round-off function
function roundOff(value) {
    return Math.round(value);
}


function calculateDA() {
    var basicSalary = parseFloat($('#basicSalary').val());
    var daPercentage = parseFloat($('#daPercentage').val());

    if (!isNaN(basicSalary) && !isNaN(daPercentage)) {
        var daAmount = (basicSalary * daPercentage) / 100;
        $('#da').val(roundOff(daAmount)); // Apply round-off
    } else {
        $('#da').val('');
    }
    calculateGrossSalary(); // Recalculate Gross Salary
}
function calculateHRA() {
    var basicSalary = parseFloat($('#basicSalary').val());
    var hraPercentage = parseFloat($('#HRAPercentage').val());

    if (!isNaN(basicSalary) && !isNaN(hraPercentage)) {
        var hraAmount = (basicSalary * hraPercentage) / 100; // Now calculated from basicSalary
        $('#hra').val(roundOff(hraAmount)); // Apply round-off
    } else {
        $('#hra').val('');
    }

    calculateGrossSalary(); // Recalculate Gross Salary
}

function calculateGrossSalary() {
    var basicSalary = parseFloat($('#basicSalary').val());
    var da = parseFloat($('#da').val());
    var hra = parseFloat($('#hra').val());
    var cta = parseFloat($('#cta').val()); // CTA is entered directly now
    var medicalAllowance = parseFloat($('#medicalAllowance').val());
    var specialOtherAllowance = parseFloat($('#specialOtherAllowance').val());

    var incomeTax = parseFloat($('#incomeTax').val());
    var pf = parseFloat($('#pf').val());
    var pfLoan = parseFloat($('#pfLoan').val());
    var nps = parseFloat($('#nps').val());
    var professionalTax = parseFloat($('#professionalTax').val());
    var lic = parseFloat($('#lic').val());
    var gip = parseFloat($('#gip').val());
    var otherDeduction = parseFloat($('#otherdeduction').val());

    // Ensure all deduction values are valid numbers
    var totalDeductions = (incomeTax || 0) + (pf || 0) + (pfLoan || 0) + (nps || 0) + (professionalTax || 0) + (lic || 0) + (gip || 0) + (otherDeduction || 0);

    // Ensure all the components of the salary are valid numbers
    if (!isNaN(basicSalary) && !isNaN(da) && !isNaN(hra) && !isNaN(cta) && !isNaN(medicalAllowance) && !isNaN(specialOtherAllowance)) {
        var grossSalary = basicSalary + da + hra + cta + medicalAllowance + specialOtherAllowance;
        $('#grosssalary').val(roundOff(grossSalary)); // Apply round-off

        var netSalary = grossSalary - totalDeductions;
        $('#netpaysalary').val(roundOff(netSalary)); // Apply round-off
    } else {
        $('#grosssalary').val('');
        $('#netpaysalary').val('');
    }
}


$('#basicSalary, #daPercentage').on('input change', calculateDA);
$('#HRAPercentage').on('input change', calculateHRA);
$('#cta').on('input change', calculateGrossSalary); // CTA is entered directly now
$('#medicalAllowance, #specialOtherAllowance, #incomeTax, #pf, #pfLoan, #nps, #professionalTax, #lic, #gip, #otherdeduction').on('input change', calculateGrossSalary);

                    $('#frmupdate').on('submit', function (event) {
                        event.preventDefault();
                        const data = {
                            payIdNo: $('#salaryNo').val(),
                            fullName: $('#fullName').val(),
                            dateOfBirth: $('#dateOfBirth').val(),
                            gender: $('#gender').val(),
                            aadharCardNo: $('#aadharCardNo').val(),
                            employeeType: $('#employeeType').val(),
                            dateOfRetirement: $('#dateOfRetirement').val(),
                            collegeName: $('#currentlyPostedAtUniversityOfficeDepartmentCollege').val(),
                            branchName: $('#branchName').val(),
                            bankAccountNo: $('#bankAccountNo').val(),
                            bankName: $('#bankName').val(),
                            ifscCode: $('#ifscCode').val(),
                            payLevel: $('#payLevel').val(),
                            basicSalary: $('#basicSalary').val(),
                            basicSalaryFixed: $('#basicSalaryFixed').val(),
                            hr: $('#hra').val(),
                            cta: $('#cta').val(),
                            da: $('#da').val(),
                            medicalAllowance: $('#medicalAllowance').val(),
                            npsOpted: $('#npsOpted').val(),
                            nps: $('#nps').val(),
                            specialOtherAllowance: $('#specialOtherAllowance').val(),
                            incomeTax: $('#incomeTax').val(),
                            pf: $('#pf').val(),
                            pfLoan: $('#pfLoan').val(),
                            lic: $('#lic').val(),
                            gip: $('#gip').val(),
                            lastPaymentWithdrawnSalary: $('#lastPaymentWithdrawnSalary').val(),
                            lastPaymentWithdrawnSalaryDeduction: $('#lastPaymentWithdrawnSalarydeduction').val(),
                            annualOptedDate: $('#annualOptedDate').val(),
                            date: $('#monthYearDropdown').val(),
                            otherdeduction: $('#otherdeduction').val() || '',
                             grosssalary: $('#grosssalary').val() || '',
                             netpaysalary: $('#netpaysalary').val() || '',
                              professionalTax: $('#professionalTax').val() || '',
                              ctapercentage: $('#ctapercentage').val() || '',
                              daPercentage: $('#daPercentage').val() || '',
                              HRAPercentage: $('#HRAPercentage').val() || '',
                              collegeId: $('#collegeId').val(),
                                universityId: $('#universityid').val(),
                            approvedBy: "MAKER",
                            universityName: universityName,
                            submittedBy: departmentUsername
                        };
                        $.ajax({
                            url: apiBaseUrl + "v1/maker/save-maker-data",
                            type: 'POST',
                            contentType: 'application/json',
                            data: JSON.stringify(data),
                            headers: {
                                "token": localStorage.getItem("access-token-new")
                            },
                            success: function (response) {
                                Swal.fire({
                                    icon: 'success',
                                    title: 'Success',
                                    text: 'Request submitted successfully to Payment Checker!',
                                });
                                $('#modalresolved').modal('hide');
                                refreshTable();
                            },
                            error: function (error) {
                                alert('Error submitting request.');
                                console.log(error);
                            }
                        });
                    });
                </script>
             <script>
                function isTokenExpired(token) {
                    if (!token) return true;
                    try {
                        // Decode JWT payload
                        const payloadBase64 = token.split('.')[1];
                        const payload = JSON.parse(atob(payloadBase64));
                        // Check if current time is past the expiration time
                        const isExpired = payload.exp * 1000 < Date.now();
                        return isExpired;
                    } catch (error) {
                        console.error("Failed to decode token:", error);
                        return true; 
                    }
                }
                // Check token expiration on load
                $(function () {
                    const token = localStorage.getItem("access-token-new");
                    const userData = JSON.parse(userDetails);
                    if (token && userData) {
                        // Check role and token expiration
                        if (usersData.role !== "Maker-Login" || isTokenExpired(token)) {
                            alert("Session expired or unauthorized access. Redirecting to login.");
                            window.location = "/";
                        } else {
                            setInterval(() => {
                                if (isTokenExpired(token)) {
                                    alert("Session expired. Please login again.");
                                    window.location = "/";
                                }
                            }, 60000);
                        }
                    } else {
                        window.location = "/";
                    }
                });
                                </script> 