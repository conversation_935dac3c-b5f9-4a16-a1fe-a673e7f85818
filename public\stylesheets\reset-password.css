
     .form {
        display: flex;
        flex-direction: column;
        gap: 10px;
        max-width: 350px;
        background-color: #fff;
        padding: 20px;
        border-radius: 20px;
        position: relative;
      }
      
      .title {
        font-size: 28px;
        color: rgb(225, 65, 86);
        font-weight: 600;
        letter-spacing: -1px;
        position: relative;
        display: flex;
        align-items: center;
        padding-left: 30px;
      }
      
      .title::before,.title::after {
        position: absolute;
        content: "";
        height: 16px;
        width: 16px;
        border-radius: 50%;
        left: 0px;
        background-color: royalblue;
      }
      
      .title::before {
        width: 18px;
        height: 18px;
        background-color: royalblue;
      }
      
      .title::after {
        width: 18px;
        height: 18px;
        animation: pulse 1s linear infinite;
      }
      
      .message, .signin {
        color: rgba(88, 87, 87, 0.822);
        font-size: 14px;
      }
      
      .signin {
        text-align: center;
      }
      
      .signin a {
        color: royalblue;
      }
      
      .signin a:hover {
        text-decoration: underline royalblue;
      }
      
      .flex {
        display: flex;
        width: 100%;
        gap: 6px;
      }
      
      .form label {
        position: relative;
      }
      
      .form label .input {
        width: 100%;
        padding: 10px 10px 20px 10px;
        outline: 0;
        border: 1px solid rgba(105, 105, 105, 0.397);
        border-radius: 10px;
      }
      
      .form label .input + span {
        position: absolute;
        left: 10px;
        top: 15px;
        color: grey;
        font-size: 0.9em;
        cursor: text;
        transition: 0.3s ease;
      }
      
      .form label .input:placeholder-shown + span {
        top: 15px;
        font-size: 0.9em;
      }
      
      .form label .input:focus + span,.form label .input:valid + span {
        top: 30px;
        font-size: 0.7em;
        font-weight: 600;
      }
      
      .form label .input:valid + span {
        color: green;
      }
      
      .submit {
        border: none;
        outline: none;
        background-color: royalblue;
        padding: 10px;
        border-radius: 10px;
        color: #fff;
        font-size: 16px;
        transform: .3s ease;
      }
      
      .submit:hover {
        background-color: rgb(56, 90, 194);
      }
      
      @keyframes pulse {
        from {
          transform: scale(0.9);
          opacity: 1;
        }
      
        to {
          transform: scale(1.8);
          opacity: 0;
        }
      }
      #frmotp {
                  width: 370px;
                  height: 370px;
                  background-color: rgb(255, 255, 255);
                  display: flex;
                  flex-direction: column;
                  align-items: center;
                  justify-content: center;
                  padding: 20px 30px;
                  gap: 20px;
                  position: relative;
                  box-shadow: 0px 0px 20px rgba(0, 0, 0, 0.082);
                  border-radius: 15px;
              }
      
              .mainHeading {
                  font-size: 1.1em;
                  color: rgb(15, 15, 15);
                  font-weight: 700;
              }
      
              .otpSubheading {
      
                  color: RED;
                  line-height: 17px;
                  text-align: center;
              }
      
              .inputContainer {
                  width: 100%;
                  display: flex;
                  flex-direction: row;
                  gap: 10px;
                  align-items: center;
                  justify-content: center;
              }
      
              .otp-input {
                  background-color: rgb(228, 228, 228);
                  width: 30px;
                  height: 30px;
                  text-align: center;
                  border: none;
                  border-radius: 7px;
                  caret-color: rgb(127, 129, 255);
                  color: rgb(44, 44, 44);
                  outline: none;
                  font-weight: 600;
              }
      
              .otp-input:focus,
              .otp-input:valid {
                  background-color: rgba(127, 129, 255, 0.199);
                  transition-duration: .3s;
              }
      
              #btnverify {
                  width: 100%;
                  border: none;
                  color: white;
                  font-weight: 600;
                  cursor: pointer;
                  border-radius: 10px;
                  transition-duration: .2s;
              }
      
              .verifyButton:hover {
                  background-color: rgb(144, 145, 255);
                  transition-duration: .2s;
              }
      
              .exitBtn {
                  position: absolute;
                  top: 5px;
                  right: 5px;
                  box-shadow: 0px 0px 20px rgba(0, 0, 0, 0.171);
                  background-color: rgb(255, 255, 255);
                  border-radius: 50%;
                  width: 25px;
                  height: 25px;
                  border: none;
                  color: black;
                  font-size: 1.1em;
                  cursor: pointer;
              }
      
              .resendNote {
      
                  color: black;
                  width: 100%;
                  display: flex;
                  flex-direction: column;
                  align-items: center;
                  justify-content: center;
                  gap: 5px;
              }
      
              .resendBtn {
                  background-color: transparent;
                  border: none;
                  color: rgb(127, 129, 255);
                  cursor: pointer;
                  font-size: 1.1em;
                  font-weight: 700;
              }
      
              .input-wrapper {
                  position: relative;
                  width: 50%;
                  max-width: 300px;
                  padding: 3px;
                  border-radius: 1.7rem;
                  overflow: hidden;
              }
      
              .input-wrapper input {
                  background-color: #f5f5f5;
                  border: 2px solid #ddd;
                  padding: 1.2rem 1rem 1.2rem 3rem;
                  font-size: 1.1rem;
                  width: 100%;
                  border-radius: 1.5rem;
                  color: #ff7f7f;
                  box-shadow: 0 0.4rem #dfd9d9, inset 0 0 0 transparent;
                  transition: all 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
                  position: relative;
                  z-index: 2;
              }
      
              .input-wrapper input:focus {
                  outline: none;
                  border-color: #4a90e2;
                  box-shadow: 0 0.6rem #dfd9d9, 0 0 15px rgba(74, 144, 226, 0.7);
                  transform: translateY(-3px) scale(1.01);
              }
      
              .input-wrapper input::placeholder {
                  color: #a0c0e8;
                  transition: all 0.3s ease;
              }
      
              .input-wrapper input:focus::placeholder {
                  opacity: 0;
                  transform: translateX(10px);
              }
      
      
              .input-wrapper::after {
                  content: "😎";
                  position: absolute;
                  left: 1rem;
                  top: 50%;
                  transform: translateY(-50%);
                  font-size: 1.2rem;
                  z-index: 3;
                  transition: all 0.3s ease;
              }
      
      
              @keyframes dance {
      
                  0%,
                  100% {
                      transform: translateY(-50%) rotate(0deg);
                  }
      
                  25% {
                      transform: translateY(-50%) rotate(-20deg) scale(1.1);
                  }
      
                  75% {
                      transform: translateY(-50%) rotate(20deg) scale(1.1);
                  }
              }
      
              .input-wrapper:hover::after {
                  animation: dance 0.5s ease infinite;
              }
      
              .input-wrapper:focus-within::after {
                  content: "🙈";
                  animation: dance 0.3s ease infinite;
              }
      
              .input-wrapper input::placeholder {
                  color: #ccc;
                  transition: all 0.3s ease;
              }
      
              .input-wrapper input:focus::placeholder {
                  opacity: 0;
                  transform: translateX(10px);
              }
      
      
              .input-wrapper::before {
                  content: "";
                  position: absolute;
                  top: -50%;
                  left: -50%;
                  width: 200%;
                  height: 200%;
                  background: conic-gradient(from 0deg,
                          #4a90e2,
                          #6aa9e9,
                          #8bc1f0,
                          #add9f7,
                          #d0f0ff,
                          #add9f7,
                          #8bc1f0,
                          #6aa9e9,
                          #4a90e2);
                  animation: rotate 4s linear infinite;
                  opacity: 0;
                  transition: opacity 0.3s ease;
                  z-index: 1;
              }
      
              .input-wrapper:hover::before,
              .input-wrapper:focus-within::before {
                  opacity: 1;
              }
      
              @keyframes rotate {
                  100% {
                      transform: rotate(360deg);
                  }
              }
      
      
              @keyframes shockwave {
                  0% {
                      transform: scale(1);
                      box-shadow: 0 0 0 0 rgba(255, 127, 127, 0.4);
                  }
      
                  70% {
                      transform: scale(1.02);
                      box-shadow: 0 0 0 20px rgba(255, 127, 127, 0);
                  }
      
                  100% {
                      transform: scale(1);
                      box-shadow: 0 0 0 0 rgba(255, 127, 127, 0);
                  }
              }
      
              .input-wrapper:focus-within {
                  animation: shockwave 0.5s ease-out;
              }
      
      
              .input-wrapper {
                  --label-size: 0.8rem;
                  --label-transform: translateY(-170%) scale(0.8);
              }
      
              .input-wrapper input:placeholder-shown+label {
                  transform: translateY(-50%);
                  font-size: 1rem;
              }
      
              .input-wrapper label {
                  position: absolute;
                  left: 1rem;
                  top: 50%;
                  transform: var(--label-transform);
                  font-size: var(--label-size);
                  color: #ff7f7f;
                  transition: all 0.3s ease;
                  pointer-events: none;
                  z-index: 3;
              }
      
              .input-wrapper input:not(:placeholder-shown)+label,
              .input-wrapper input:focus+label {
                  transform: var(--label-transform);
                  font-size: var(--label-size);
              }
      
      .container {
        border: solid 1px #8d8d8d;
        padding: 20px;
        border-radius: 20px;
        background-color: #fff;
      }
      
      .container .heading {
        font-size: 1.3rem;
        margin-bottom: 20px;
        font-weight: bolder;
      }
      
      #frmpassword {
        max-width: 300px;
        display: flex;
        flex-direction: column;
        gap: 20px;
      }
      
      #frmpassword .btn-container {
        width: 100%;
        display: flex;
        align-items: center;
        gap: 20px;
      }
      
      .btn {
        padding: 10px 20px;
        font-size: 1rem;
        text-transform: uppercase;
        letter-spacing: 3px;
        border-radius: 10px;
        border: solid 1px #1034aa;
        border-bottom: solid 1px #90c2ff;
        background: linear-gradient(135deg, #0034de, #006eff);
        color: #fff;
        font-weight: bolder;
        transition: all 0.2s ease;
        box-shadow: 0px 2px 3px #000d3848, inset 0px 4px 5px #0070f0,
          inset 0px -4px 5px #002cbb;
      }
      
      .form .btn:active {
        box-shadow: inset 0px 4px 5px #0070f0, inset 0px -4px 5px #002cbb;
        transform: scale(0.995);
      }
      
      .input-field {
        position: relative;
      
      }
      
      .input-field label {
        position: absolute;
        color: #8d8d8d;
        pointer-events: none;
        background-color: transparent;
        left: 15px;
        transform: translateY(0.6rem);
        transition: all 0.3s ease;
      }
      
      .input-field input {
        padding: 10px 15px;
        font-size: 1rem;
        border-radius: 8px;
        border: solid 1px #8d8d8d;
        letter-spacing: 1px;
        width: 100%;
      }
      
      .input-field input:focus,
      .input-field input:valid {
        outline: none;
        border: solid 1px #0034de;
      }
      
      .input-field input:focus ~ label,
      .input-field input:valid ~ label {
        transform: translateY(-51%) translateX(-10px) scale(0.8);
        background-color: #fff;
        padding: 0px 5px;
        color: #0034de;
        font-weight: bold;
        letter-spacing: 1px;
        border: none;
        border-radius: 100px;
      }
      
      #frmpassword .passicon {
        cursor: pointer;
        font-size: 1.3rem;
        position: absolute;
        top: 6px;
        right: 8px;
      }
      